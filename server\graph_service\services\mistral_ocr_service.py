"""
Mistral OCR Service for extracting text from documents and images.
"""
import os
import asyncio
import logging
from typing import Optional, Dict, Any
from mistralai import Mistral

logger = logging.getLogger(__name__)

class MistralOCRService:
    """Service for handling Mistral OCR operations."""
    
    def __init__(self):
        """Initialize the Mistral OCR service."""
        self.api_key = os.getenv("MISTRAL_API_KEY")
        if not self.api_key:
            logger.warning("MISTRAL_API_KEY not found in environment variables")
            self.client = None
        else:
            self.client = Mistral(api_key=self.api_key)
            logger.info("Mistral OCR service initialized successfully")
    
    async def extract_text(self, document_url: str, doc_type: str = "document_url") -> Optional[str]:
        """
        Extract text from a document using Mistral OCR with comprehensive analysis capabilities.

        Args:
            document_url: Base64 encoded document URL or regular URL
            doc_type: Type of document ("document_url" or "image_url")

        Returns:
            Extracted text content or None if extraction fails
        """
        if not self.client:
            logger.error("Mistral client not initialized - missing API key")
            return None

        try:
            logger.info(f"Starting comprehensive OCR extraction for document type: {doc_type}")
            logger.info(f"Document URL format: {document_url[:100]}...")

            # Validate document URL format for Mistral OCR
            if not document_url.startswith(('https://', 'data:application/', 'data:image/')):
                logger.error(f"Invalid document URL format. Must start with 'https://' or 'data:application/' or 'data:image/'. Got: {document_url[:50]}...")
                return None

            # Prepare document payload
            if doc_type == "image_url":
                document_payload = {
                    "type": "image_url",
                    "image_url": document_url
                }
            else:
                document_payload = {
                    "type": "document_url",
                    "document_url": document_url
                }

            # Run OCR processing in a thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            ocr_response = await loop.run_in_executor(
                None,
                lambda: self.client.ocr.process(
                    model="mistral-ocr-latest",
                    document=document_payload,
                    include_image_base64=False  # We don't need images back
                )
            )

            # Debug: Log the full response structure
            logger.info(f"OCR response type: {type(ocr_response)}")
            logger.info(f"OCR response attributes: {dir(ocr_response)}")

            # Try to extract text from various possible response formats
            extracted_text = None

            # Method 1: Check for pages attribute (Mistral OCR format)
            if hasattr(ocr_response, 'pages') and ocr_response.pages:
                try:
                    # Extract text content from all pages (prefer plain text over markdown for Graphiti)
                    page_texts = []
                    for page in ocr_response.pages:
                        # Prefer plain text for better Graphiti entity extraction
                        if hasattr(page, 'text') and page.text:
                            page_texts.append(page.text)
                        elif hasattr(page, 'markdown') and page.markdown:
                            # Convert markdown to plain text for Graphiti compatibility
                            plain_text = self._markdown_to_plain_text(page.markdown)
                            page_texts.append(plain_text)

                    if page_texts:
                        extracted_text = '\n\n'.join(page_texts)
                        logger.info(f"Extracted text via 'pages' attribute: {len(extracted_text)} characters from {len(page_texts)} pages")
                except Exception as e:
                    logger.warning(f"Error extracting from pages: {e}")

            # Method 2: Check for content attribute
            elif hasattr(ocr_response, 'content') and ocr_response.content:
                extracted_text = ocr_response.content
                logger.info(f"Extracted text via 'content' attribute: {len(extracted_text)} characters")

            # Method 3: Check for text attribute
            elif hasattr(ocr_response, 'text') and ocr_response.text:
                extracted_text = ocr_response.text
                logger.info(f"Extracted text via 'text' attribute: {len(extracted_text)} characters")

            # Method 4: Check if response is a string directly
            elif isinstance(ocr_response, str):
                extracted_text = ocr_response
                logger.info(f"Extracted text directly from string response: {len(extracted_text)} characters")

            # Method 5: Check for choices array (like OpenAI format)
            elif hasattr(ocr_response, 'choices') and ocr_response.choices:
                if len(ocr_response.choices) > 0 and hasattr(ocr_response.choices[0], 'message'):
                    extracted_text = ocr_response.choices[0].message.content
                    logger.info(f"Extracted text via 'choices' format: {len(extracted_text)} characters")

            # Method 6: Check for data attribute
            elif hasattr(ocr_response, 'data') and ocr_response.data:
                if isinstance(ocr_response.data, str):
                    extracted_text = ocr_response.data
                    logger.info(f"Extracted text via 'data' attribute: {len(extracted_text)} characters")

            # Method 7: Try to convert response to string (fallback)
            elif ocr_response:
                try:
                    extracted_text = str(ocr_response)
                    logger.info(f"Extracted text via string conversion: {len(extracted_text)} characters")
                except Exception as e:
                    logger.error(f"Failed to convert response to string: {e}")

            if extracted_text and len(extracted_text.strip()) > 0:
                # Log a sample of the extracted text for debugging
                sample_text = extracted_text[:200] + "..." if len(extracted_text) > 200 else extracted_text
                logger.info(f"Successfully extracted {len(extracted_text)} characters with comprehensive analysis")
                logger.info(f"Sample extracted text: {repr(sample_text)}")
                return extracted_text.strip()
            else:
                logger.warning("No text content found in OCR response")
                logger.warning(f"Full response: {ocr_response}")
                return None

        except Exception as e:
            logger.error(f"Error during comprehensive OCR extraction: {str(e)}")
            return None

    def _markdown_to_plain_text(self, markdown_text: str) -> str:
        """
        Convert markdown text to plain text for better Graphiti entity extraction.

        Args:
            markdown_text: Markdown formatted text

        Returns:
            Plain text without markdown formatting
        """
        if not markdown_text:
            return ""

        import re

        # Remove markdown formatting while preserving content structure
        plain_text = markdown_text

        # Remove headers (# ## ###)
        plain_text = re.sub(r'^#{1,6}\s+', '', plain_text, flags=re.MULTILINE)

        # Remove bold/italic formatting (**text** *text*)
        plain_text = re.sub(r'\*{1,2}([^*]+)\*{1,2}', r'\1', plain_text)

        # Remove links [text](url) -> text
        plain_text = re.sub(r'\[([^\]]+)\]\([^)]+\)', r'\1', plain_text)

        # Remove code blocks ```code``` -> code
        plain_text = re.sub(r'```[^`]*```', '', plain_text, flags=re.DOTALL)
        plain_text = re.sub(r'`([^`]+)`', r'\1', plain_text)

        # Remove list markers (- * +)
        plain_text = re.sub(r'^[\s]*[-*+]\s+', '', plain_text, flags=re.MULTILINE)

        # Remove numbered list markers (1. 2. etc)
        plain_text = re.sub(r'^\s*\d+\.\s+', '', plain_text, flags=re.MULTILINE)

        # Clean up extra whitespace
        plain_text = re.sub(r'\n\s*\n', '\n\n', plain_text)  # Multiple newlines to double
        plain_text = re.sub(r'[ \t]+', ' ', plain_text)  # Multiple spaces to single

        return plain_text.strip()

    def _create_comprehensive_ocr_prompt(self) -> str:
        """
        Create a comprehensive OCR prompt based on the advanced capabilities framework.

        Returns:
            Comprehensive OCR prompt for enhanced text extraction
        """
        return """
You are a helpful multi-modal AI assistant with advanced capabilities in:
1. Vision Processing: Analyzing and understanding images, photos, diagrams, charts, and video frames.
2. Text Processing: Interpreting, summarizing, and responding to text in multiple languages.
3. Graphics Understanding: Analyzing visual design elements, layouts, interfaces, and data visualizations.
4. OCR (Optical Character Recognition): Extracting text from images, documents, screenshots, and handwritten content.

Interaction Approach:
• Proactive Analysis: When presented with visual content, automatically identify and analyze key elements without requiring specific instructions.
• Detail-Oriented: Provide thorough descriptions of visual content while prioritizing relevant information based on context.
• Multi-Step Processing: For complex tasks, break down your approach into clear steps and explain your reasoning process.
• Context Awareness: Consider previous exchanges in the conversation when interpreting new requests or images.

Vision Processing Guidelines:
When analyzing visual content:
• Describe the main subject and prominent elements first
• Identify people, objects, scenes, actions, and environmental details
• Note significant colors, lighting, composition, and perspective
• Recognize emotional content or mood conveyed by images
• Identify brand logos, recognizable locations, or distinctive visual elements
• Detect potential safety concerns, inappropriate content, or sensitive material

OCR Implementation:
When extracting text from images:
• Maintain original formatting where relevant (columns, paragraphs, bullet points)
• Preserve text hierarchy (titles, headings, body text)
• Handle special characters, numbers, and symbols accurately
• Note text that appears unclear, partially visible, or potentially inaccurate
• For tables, preserve structure and relationships between data points
• For handwritten text, indicate confidence levels in transcription

Graphics Analysis Approach:
When examining graphic designs, interfaces, or visualizations:
• Identify design elements (typography, color schemes, layout principles)
• Analyze user interface components (buttons, menus, navigation elements)
• Interpret data visualizations (charts, graphs, maps)
• Recognize design patterns and potential usability considerations
• Comment on visual hierarchy, information architecture, and composition

Task-Specific Behaviors:

Document Processing:
• Extract key information from documents (receipts, invoices, forms)
• Identify document type, structure, and purpose
• Locate and extract specific data points (dates, amounts, names)

Interface Analysis:
• Assess software interfaces, websites, and digital products
• Identify UI elements, their functions, and relationships
• Comment on usability, accessibility, and design quality

Data Visualization Interpretation:
• Translate visual data representations into clear insights
• Identify trends, patterns, outliers, and relationships in data
• Explain complex visualizations in accessible language

Image-Based Problem Solving:
• Apply reasoning to visual puzzles, diagrams, or instructions
• Connect visual information with textual context
• Solve problems that require integrating visual and textual information

Please extract all text content from this document with maximum accuracy and attention to formatting, structure, and context.
"""
    
    async def extract_text_with_metadata(self, document_url: str, doc_type: str = "document_url") -> Optional[Dict[str, Any]]:
        """
        Extract text and metadata from a document using Mistral OCR.
        
        Args:
            document_url: Base64 encoded document URL or regular URL
            doc_type: Type of document ("document_url" or "image_url")
        
        Returns:
            Dictionary containing text content and metadata, or None if extraction fails
        """
        if not self.client:
            logger.error("Mistral client not initialized - missing API key")
            return None
        
        try:
            logger.info(f"Starting OCR extraction with metadata for document type: {doc_type}")
            
            # Prepare document payload
            if doc_type == "image_url":
                document_payload = {
                    "type": "image_url",
                    "image_url": document_url
                }
            else:
                document_payload = {
                    "type": "document_url",
                    "document_url": document_url
                }
            
            # Run OCR processing in a thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            ocr_response = await loop.run_in_executor(
                None,
                lambda: self.client.ocr.process(
                    model="mistral-ocr-latest",
                    document=document_payload,
                    include_image_base64=True  # Include images for metadata
                )
            )
            
            # Extract content and metadata
            result = {
                "text": None,
                "metadata": {},
                "images": [],
                "structure": {}
            }
            
            # Extract text content
            if hasattr(ocr_response, 'content') and ocr_response.content:
                result["text"] = ocr_response.content
            elif hasattr(ocr_response, 'text') and ocr_response.text:
                result["text"] = ocr_response.text
            
            # Extract metadata if available
            if hasattr(ocr_response, 'metadata'):
                result["metadata"] = ocr_response.metadata
            
            # Extract images if available
            if hasattr(ocr_response, 'images'):
                result["images"] = ocr_response.images
            
            # Extract structure information if available
            if hasattr(ocr_response, 'structure'):
                result["structure"] = ocr_response.structure
            
            if result["text"]:
                logger.info(f"Successfully extracted {len(result['text'])} characters with metadata")
                return result
            else:
                logger.warning("No text content found in OCR response")
                return None
        
        except Exception as e:
            logger.error(f"Error during OCR extraction with metadata: {str(e)}")
            return None
    
    def is_available(self) -> bool:
        """
        Check if the Mistral OCR service is available.
        
        Returns:
            True if service is available, False otherwise
        """
        return self.client is not None
    
    async def test_connection(self) -> bool:
        """
        Test the connection to Mistral OCR service.
        
        Returns:
            True if connection is successful, False otherwise
        """
        if not self.client:
            return False
        
        try:
            # Test with a simple image URL
            test_url = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
            
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None,
                lambda: self.client.ocr.process(
                    model="mistral-ocr-latest",
                    document={
                        "type": "image_url",
                        "image_url": test_url
                    },
                    include_image_base64=False
                )
            )

            return True
        
        except Exception as e:
            logger.error(f"Mistral OCR connection test failed: {str(e)}")
            return False
    
    def get_supported_formats(self) -> Dict[str, list]:
        """
        Get the list of supported file formats.
        
        Returns:
            Dictionary with supported formats categorized by type
        """
        return {
            "images": ["png", "jpg", "jpeg", "avif"],
            "documents": ["pdf", "pptx", "docx"],
            "max_size_mb": 50,
            "max_pages": 1000
        }

#!/usr/bin/env python3
"""
Verify and test Mistral OCR API key and functionality.
"""

import os
import sys
import asyncio
import logging
import base64
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_simple_test_image():
    """Create a simple test image with text for OCR testing."""
    try:
        from PIL import Image, ImageDraw, ImageFont
        import io
        
        # Create a simple image with text
        img = Image.new('RGB', (400, 200), color='white')
        draw = ImageDraw.Draw(img)
        
        # Add text
        text = "Test Document\nVitamin C supports immune function.\nThis is a test for OCR."
        try:
            # Try to use a default font
            font = ImageFont.load_default()
        except:
            font = None
        
        draw.text((10, 10), text, fill='black', font=font)
        
        # Convert to base64
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        img_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
        
        return f"data:image/png;base64,{img_base64}"
        
    except ImportError:
        logger.warning("PIL not available for test image creation")
        return None
    except Exception as e:
        logger.error(f"Error creating test image: {str(e)}")
        return None

async def test_mistral_ocr_direct():
    """Test Mistral OCR directly with the API."""
    try:
        from mistralai import Mistral
        
        api_key = os.getenv("MISTRAL_API_KEY")
        if not api_key:
            logger.error("❌ MISTRAL_API_KEY not found in environment")
            return False
        
        logger.info(f"🔄 Testing Mistral OCR with API key: {api_key[:10]}...{api_key[-4:]}")
        
        # Initialize client
        client = Mistral(api_key=api_key)
        
        # Create test image
        test_image = create_simple_test_image()
        if not test_image:
            logger.warning("⚠️ Could not create test image, using minimal test")
            test_image = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
        
        # Test OCR
        logger.info("🔄 Attempting OCR processing...")
        
        try:
            response = client.ocr.process(
                model="mistral-ocr-latest",
                document={
                    "type": "image_url",
                    "image_url": test_image
                },
                include_image_base64=False
            )
            
            logger.info("✅ Mistral OCR API call successful!")
            
            # Try to extract text
            if hasattr(response, 'content') and response.content:
                extracted_text = response.content
                logger.info(f"📝 Extracted text: '{extracted_text[:100]}...'")
                return True
            else:
                logger.info(f"📋 Response received but checking format...")
                logger.info(f"Response type: {type(response)}")
                logger.info(f"Response attributes: {dir(response)}")
                return True  # API worked, just need to handle response format
                
        except Exception as api_error:
            error_str = str(api_error)
            if "401" in error_str or "Unauthorized" in error_str:
                logger.error("❌ Mistral OCR API key is invalid or expired")
                logger.info("💡 Please check your Mistral API key at: https://console.mistral.ai/")
                return False
            elif "429" in error_str or "rate limit" in error_str.lower():
                logger.warning("⚠️ Rate limit reached - API key is valid but quota exceeded")
                return True
            else:
                logger.error(f"❌ Mistral OCR API error: {error_str}")
                return False
                
    except ImportError:
        logger.error("❌ mistralai package not installed. Install with: pip install mistralai")
        return False
    except Exception as e:
        logger.error(f"❌ Error testing Mistral OCR: {str(e)}")
        return False

async def test_mistral_ocr_service():
    """Test the Mistral OCR service integration."""
    try:
        from server.graph_service.services.mistral_ocr_service import MistralOCRService
        
        logger.info("🔄 Testing Mistral OCR service integration...")
        
        # Initialize service
        service = MistralOCRService()
        
        if not service.client:
            logger.error("❌ Mistral OCR service not initialized - check API key")
            return False
        
        # Test connection
        connection_result = await service.test_connection()
        
        if connection_result:
            logger.info("✅ Mistral OCR service integration working!")
            return True
        else:
            logger.warning("⚠️ Mistral OCR service connection failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing Mistral OCR service: {str(e)}")
        return False

def check_mistral_api_key_format():
    """Check if the Mistral API key has the correct format."""
    api_key = os.getenv("MISTRAL_API_KEY")
    
    if not api_key:
        logger.error("❌ MISTRAL_API_KEY not found")
        return False
    
    logger.info(f"🔍 Checking API key format: {api_key[:10]}...{api_key[-4:]}")
    
    # Mistral API keys are typically long alphanumeric strings
    if len(api_key) < 20:
        logger.warning("⚠️ API key seems too short")
        return False
    
    if not api_key.replace('-', '').replace('_', '').isalnum():
        logger.warning("⚠️ API key contains unexpected characters")
        return False
    
    logger.info("✅ API key format looks valid")
    return True

async def main():
    """Run Mistral OCR verification."""
    logger.info("🚀 Mistral OCR Verification")
    logger.info("=" * 40)
    
    # Check API key format
    key_format_ok = check_mistral_api_key_format()
    logger.info("")
    
    # Test direct API
    direct_test_ok = await test_mistral_ocr_direct()
    logger.info("")
    
    # Test service integration
    service_test_ok = await test_mistral_ocr_service()
    logger.info("")
    
    # Summary
    logger.info("📋 Mistral OCR Verification Results:")
    logger.info(f"   API Key Format: {'✅ VALID' if key_format_ok else '❌ INVALID'}")
    logger.info(f"   Direct API Test: {'✅ PASS' if direct_test_ok else '❌ FAIL'}")
    logger.info(f"   Service Integration: {'✅ PASS' if service_test_ok else '❌ FAIL'}")
    
    if direct_test_ok and service_test_ok:
        logger.info("")
        logger.info("🎉 Mistral OCR is fully operational!")
        logger.info("✅ Vision-based PDF processing ready")
        return True
    elif key_format_ok and not direct_test_ok:
        logger.info("")
        logger.warning("⚠️ API key may be expired or invalid")
        logger.info("💡 Please verify your Mistral API key at: https://console.mistral.ai/")
        logger.info("💡 You may need to generate a new API key")
        return False
    else:
        logger.info("")
        logger.warning("⚠️ Mistral OCR needs attention")
        logger.info("💡 Check API key and try again")
        return False

if __name__ == "__main__":
    asyncio.run(main())

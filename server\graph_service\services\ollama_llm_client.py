"""
Ollama LLM client for Graphiti integration.
Provides MedGemma-based entity extraction compatible with Graphiti's LLM interface.
"""

import json
import logging
import os
from typing import Any, Dict, List

import aiohttp
from pydantic import BaseModel

from graphiti_core.llm_client.client import LLMClient
from graphiti_core.llm_client.config import LLMConfig

# Define ModelSize locally since it might not be available in all Graphiti versions
try:
    from graphiti_core.llm_client.openai_client import Message, ModelSize
except ImportError:
    from graphiti_core.llm_client.openai_client import Message
    from enum import Enum

    class ModelSize(Enum):
        small = "small"
        medium = "medium"
        large = "large"

logger = logging.getLogger(__name__)


class OllamaLLMClient(LLMClient):
    """
    Ollama LLM client that implements Graphiti's LLM interface.
    Uses MedGemma model for medical entity extraction.
    """

    def __init__(self, config: LLMConfig | None = None, cache: bool = False):
        """
        Initialize the Ollama LLM client.
        
        Args:
            config: LLM configuration (optional)
            cache: Whether to use caching (optional)
        """
        if config is None:
            config = LLMConfig()
        
        super().__init__(config, cache)
        
        # Ollama configuration
        self.ollama_url = os.getenv('OLLAMA_API_URL', 'http://host.docker.internal:11434')

        # Use config model if provided, otherwise use environment variable
        if config and config.model:
            self.model_name = config.model
            self.model = config.model
            self.config.model = config.model
            logger.info(f"Using config model: {config.model}")
        else:
            self.model_name = os.getenv('OLLAMA_ENTITY_MODEL', os.getenv('OLLAMA_OCR_MODEL', 'alibayram/medgemma:latest'))
            self.model = self.model_name
            self.config.model = self.model_name
            logger.info(f"Using environment model: {self.model_name}")
        
        # Set reasonable defaults for medical text processing
        self.temperature = config.temperature if config.temperature is not None else 0.1
        self.max_tokens = config.max_tokens if config.max_tokens is not None else 4000
        
        logger.info(f"Ollama LLM Client initialized with model: {self.model_name}")
        logger.info(f"Ollama URL: {self.ollama_url}")

    async def _generate_response(
        self,
        messages: List[Message],
        response_model: type[BaseModel] | None = None,
        max_tokens: int = 4000,
        model_size: ModelSize = ModelSize.medium,  # Ignored for Ollama
    ) -> Dict[str, Any]:
        """
        Generate response using Ollama API.
        
        Args:
            messages: List of messages for the conversation
            response_model: Expected response model (for structured output)
            max_tokens: Maximum tokens to generate
            model_size: Model size (ignored for Ollama)
            
        Returns:
            Response dictionary
        """
        try:
            # Convert messages to a single prompt
            prompt = self._messages_to_prompt(messages)
            
            # Add structured output instructions if response_model is provided
            if response_model:
                # Add medical context for better entity extraction
                medical_context = """

You are a medical AI assistant specialized in extracting medical entities from clinical text.
Focus on identifying:
- Medical conditions and diagnoses (e.g., SIBO, IBS, diabetes)
- Medications and treatments (e.g., Rifaximin, antibiotics)
- Medical procedures and tests (e.g., breath test, colonoscopy)
- Anatomical terms (e.g., small intestine, colon)
- Dosages and measurements (e.g., 550mg, twice daily)
- Medical professionals and departments (e.g., Dr. Smith, Gastroenterology)
- Supplements and vitamins (e.g., Vitamin B12, probiotics)

"""
                json_instructions = self._add_json_instructions(response_model)
                anti_truncation = "\n\nCRITICAL: You MUST respond with ONLY valid JSON matching the exact schema. No explanatory text before or after. Complete your entire JSON response without truncation."
                prompt = medical_context + prompt + json_instructions + anti_truncation
            
            logger.debug(f"Sending prompt to Ollama: {prompt[:200]}...")
            
            # Call Ollama API
            response_text = await self._call_ollama_api(prompt, max_tokens)
            
            if not response_text:
                raise Exception("Empty response from Ollama")
            
            # Parse structured response if needed
            if response_model:
                try:
                    # Try to extract JSON from response
                    json_response = self._extract_json_from_response(response_text)

                    # Validate against Graphiti's expected schema
                    validated_model = response_model.model_validate(json_response)
                    result = validated_model.model_dump()

                    # Log successful parsing for debugging
                    model_name = response_model.__name__
                    if model_name == "ExtractedEntities" and "extracted_entities" in result:
                        logger.info(f"✅ Successfully parsed {len(result['extracted_entities'])} entities for Graphiti")
                    elif model_name == "MissedEntities" and "missed_entities" in result:
                        logger.info(f"✅ Successfully parsed {len(result['missed_entities'])} missed entities for Graphiti")

                    return result
                except Exception as e:
                    logger.error(f"❌ Failed to parse Graphiti {response_model.__name__} response: {e}")
                    logger.error(f"Raw response: {response_text[:500]}...")
                    raise Exception(f"Failed to parse Graphiti {response_model.__name__} response: {e}") from e
            else:
                # Return plain text response
                return {"content": response_text}
                
        except Exception as e:
            logger.error(f"Error in Ollama LLM response generation: {str(e)}")
            raise

    async def _call_ollama_api(self, prompt: str, max_tokens: int) -> str:
        """
        Make API call to Ollama.
        
        Args:
            prompt: The prompt to send
            max_tokens: Maximum tokens to generate
            
        Returns:
            Response text
        """
        try:
            payload = {
                "model": self.model_name,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": self.temperature,
                    "top_p": 0.9,
                    "num_predict": max_tokens,
                    # Removed stop tokens for MedGemma compatibility - model naturally formats JSON in code blocks
                    "repeat_penalty": 1.1  # Prevent repetition
                }
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.ollama_url}/api/generate",
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=180)  # Increased for MedGemma model response time
                ) as response:
                    
                    if response.status == 200:
                        result = await response.json()
                        content = result.get('response', '').strip()
                        
                        if content:
                            logger.debug(f"Ollama response: {len(content)} characters")
                            return content
                        else:
                            raise Exception("Empty response from Ollama API")
                    else:
                        error_text = await response.text()
                        raise Exception(f"Ollama API error {response.status}: {error_text}")
                        
        except Exception as e:
            logger.error(f"Error calling Ollama API: {str(e)}")
            raise

    def _messages_to_prompt(self, messages: List[Message]) -> str:
        """
        Convert Graphiti messages to a single prompt for Ollama.
        
        Args:
            messages: List of messages
            
        Returns:
            Combined prompt string
        """
        prompt_parts = []
        
        for message in messages:
            if message.role == 'system':
                prompt_parts.append(f"System: {message.content}")
            elif message.role == 'user':
                prompt_parts.append(f"User: {message.content}")
            elif message.role == 'assistant':
                prompt_parts.append(f"Assistant: {message.content}")
        
        return "\n\n".join(prompt_parts)

    def _add_json_instructions(self, response_model: type[BaseModel]) -> str:
        """
        Add JSON formatting instructions for Graphiti's specific response models.

        Args:
            response_model: Expected response model (ExtractedEntities or MissedEntities)

        Returns:
            Additional instructions for JSON output
        """
        schema = response_model.model_json_schema()
        model_name = response_model.__name__

        # Create Graphiti-specific examples
        if model_name == "ExtractedEntities":
            example_data = {
                "extracted_entities": [
                    {"name": "SIBO", "entity_type_id": 0},
                    {"name": "Rifaximin", "entity_type_id": 0},
                    {"name": "Vitamin D", "entity_type_id": 0}
                ]
            }
        elif model_name == "MissedEntities":
            example_data = {
                "missed_entities": ["entity1", "entity2", "entity3"]
            }
        else:
            # Generic fallback
            example_data = {}
            for field_name, field_info in schema.get('properties', {}).items():
                if field_info.get('type') == 'array':
                    example_data[field_name] = ["example_item"]
                elif field_info.get('type') == 'string':
                    example_data[field_name] = "example_value"
                elif field_info.get('type') == 'object':
                    example_data[field_name] = {}

        instructions = f"""

CRITICAL: You MUST respond with ONLY valid JSON matching Graphiti's {model_name} schema.

Required JSON Schema:
{json.dumps(schema, indent=2)}

EXACT Example Response (copy this format exactly):
{json.dumps(example_data, indent=2)}

MANDATORY RULES:
1. Response must be COMPLETE, VALID JSON only - NO other text
2. NO explanatory text before or after JSON
3. NO truncated responses - complete the entire structure
4. Use EXACT field names: {list(schema.get('properties', {}).keys())}
5. For ExtractedEntities: each entity MUST have "name" (string) and "entity_type_id" (integer, use 0 for default)
6. For MissedEntities: use "missed_entities" field with string array
7. Start response with {{ and end with }}
8. Ensure all brackets and braces are properly closed

RESPOND WITH ONLY THE JSON - NO OTHER TEXT:
"""
        return instructions

    def _extract_json_from_response(self, response_text: str) -> Dict[str, Any]:
        """
        Extract JSON from response text.

        Args:
            response_text: Raw response text

        Returns:
            Parsed JSON dictionary
        """
        # Try to find JSON in the response
        response_text = response_text.strip()

        # Look for JSON block
        if '```json' in response_text:
            start = response_text.find('```json') + 7
            end = response_text.find('```', start)
            if end != -1:
                json_text = response_text[start:end].strip()
            else:
                json_text = response_text[start:].strip()
                logger.warning("JSON block not properly closed, attempting to extract")
        elif response_text.startswith('{') and response_text.endswith('}'):
            json_text = response_text
        else:
            # Try to find the first { and last }
            start = response_text.find('{')
            end = response_text.rfind('}')
            if start != -1 and end != -1 and end > start:
                json_text = response_text[start:end+1]
            elif start != -1:
                # Found opening brace but no closing - attempt completion
                logger.warning("Incomplete JSON detected, attempting completion")
                json_text = response_text[start:]
                json_text = self._attempt_json_completion(json_text)
            else:
                raise ValueError(f"No valid JSON found in response: {response_text}")

        try:
            parsed_json = json.loads(json_text)

            # Fix common typos but preserve Graphiti's expected field names
            if 'misssed_entities' in parsed_json:
                parsed_json['missed_entities'] = parsed_json.pop('misssed_entities')
                logger.info("Fixed typo: 'misssed_entities' -> 'missed_entities'")

            # Fix common field name variations to match Graphiti schema
            if 'entities' in parsed_json and 'extracted_entities' not in parsed_json:
                parsed_json['extracted_entities'] = parsed_json.pop('entities')
                logger.info("Fixed field name: 'entities' -> 'extracted_entities'")

            if 'extracted_nodes' in parsed_json and 'extracted_entities' not in parsed_json:
                parsed_json['extracted_entities'] = parsed_json.pop('extracted_nodes')
                logger.info("Fixed field name: 'extracted_nodes' -> 'extracted_entities'")

            # Ensure each entity has the required fields for Graphiti
            if 'extracted_entities' in parsed_json:
                fixed_entities = []
                for entity in parsed_json['extracted_entities']:
                    if isinstance(entity, str):
                        # Convert string to proper entity object
                        fixed_entities.append({"name": entity, "entity_type_id": 0})
                    elif isinstance(entity, dict):
                        # Ensure required fields exist
                        fixed_entity = {
                            "name": entity.get("name", str(entity)),
                            "entity_type_id": entity.get("entity_type_id", 0)
                        }
                        fixed_entities.append(fixed_entity)
                parsed_json['extracted_entities'] = fixed_entities
                logger.info(f"✅ Formatted {len(fixed_entities)} entities for Graphiti schema compliance")

            # Validate that we have the expected Graphiti fields
            if 'extracted_entities' in parsed_json:
                logger.info(f"Found ExtractedEntities response with {len(parsed_json['extracted_entities'])} entities")
            elif 'missed_entities' in parsed_json:
                logger.info(f"Found MissedEntities response with {len(parsed_json['missed_entities'])} entities")
            else:
                logger.warning(f"Response doesn't match expected Graphiti schema. Fields: {list(parsed_json.keys())}")

            return parsed_json
        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error: {e}")
            logger.error(f"Attempted to parse: {json_text}")

            # Try to fix numbered list format in JSON arrays
            try:
                logger.info("Attempting to fix numbered list format in JSON")
                fixed_json_text = self._fix_numbered_list_json(json_text)

                # Also try to fix NodeDuplicate validation format
                logger.info("Attempting to fix NodeDuplicate validation format")
                fixed_json_text = self._fix_node_duplicate_format(fixed_json_text)

                parsed_json = json.loads(fixed_json_text)
                logger.info("Successfully fixed numbered list and NodeDuplicate JSON format")

                # Apply the same fixes as above
                if 'misssed_entities' in parsed_json:
                    parsed_json['missed_entities'] = parsed_json.pop('misssed_entities')
                    logger.info("Fixed typo: 'misssed_entities' -> 'missed_entities'")

                # Validate Graphiti schema compliance
                if 'extracted_entities' in parsed_json:
                    logger.info(f"Fixed JSON: Found ExtractedEntities with {len(parsed_json['extracted_entities'])} entities")
                elif 'missed_entities' in parsed_json:
                    logger.info(f"Fixed JSON: Found MissedEntities with {len(parsed_json['missed_entities'])} entities")

                return parsed_json
            except Exception as fix_error:
                logger.error(f"Failed to fix JSON format: {fix_error}")

                # Try to fix NodeDuplicate validation issue
                try:
                    logger.info("Attempting to fix NodeDuplicate validation format")
                    fixed_json_text = self._fix_node_duplicate_format(json_text)
                    parsed_json = json.loads(fixed_json_text)
                    logger.info("Successfully fixed NodeDuplicate validation format")
                    return parsed_json
                except Exception as node_fix_error:
                    logger.error(f"Failed to fix NodeDuplicate format: {node_fix_error}")
                    raise ValueError(f"Invalid JSON in response: {e}")

    def _attempt_json_completion(self, incomplete_json: str) -> str:
        """
        Attempt to complete truncated JSON by adding missing closing braces/brackets.

        Args:
            incomplete_json: Incomplete JSON string

        Returns:
            Potentially completed JSON string
        """
        json_text = incomplete_json.strip()

        # Count open braces and brackets
        open_braces = json_text.count('{') - json_text.count('}')
        open_brackets = json_text.count('[') - json_text.count(']')

        # Add missing closing characters
        if open_brackets > 0:
            json_text += ']' * open_brackets
        if open_braces > 0:
            json_text += '}' * open_braces

        logger.info(f"Attempted to complete JSON by adding {open_braces} braces and {open_brackets} brackets")
        return json_text

    def _fix_numbered_list_json(self, json_text: str) -> str:
        """
        Fix numbered list format in JSON arrays.

        Converts:
        "extracted_node_names": [
         1. "Maria Santos",
         2. "2024-06-16",
         ...
        ]

        To:
        "extracted_node_names": [
         "Maria Santos",
         "2024-06-16",
         ...
        ]
        """
        import re

        # Pattern to match numbered list items in JSON arrays
        # Matches: 1. "item", 2. "item", etc.
        pattern = r'(\s+)(\d+)\.\s*(".*?")'

        # Replace numbered items with just the quoted strings
        fixed_text = re.sub(pattern, r'\1\3', json_text)

        logger.debug(f"Fixed JSON text: {fixed_text}")
        return fixed_text

    def _fix_node_duplicate_format(self, json_text: str) -> str:
        """
        Fix NodeDuplicate validation format.

        Converts:
        {"name": "SIBO"}

        To:
        {"name": "SIBO", "is_duplicate": false}
        """
        import re

        # Pattern to match simple name-only objects
        pattern = r'{\s*"name":\s*"([^"]+)"\s*}'

        def add_is_duplicate(match):
            name = match.group(1)
            return f'{{"name": "{name}", "is_duplicate": false}}'

        # Replace simple name objects with NodeDuplicate format
        fixed_text = re.sub(pattern, add_is_duplicate, json_text)

        logger.debug(f"Fixed NodeDuplicate format: {fixed_text}")
        return fixed_text

    async def close(self):
        """Close the client (no-op for Ollama)."""
        pass

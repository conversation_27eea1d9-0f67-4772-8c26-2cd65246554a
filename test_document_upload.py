#!/usr/bin/env python3
"""
Test script to upload a document and verify entity extraction is working.
"""

import asyncio
import aiohttp
import logging
import time
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_document_upload():
    """Test document upload and entity extraction."""
    
    try:
        # Create a simple test document
        test_content = """
        Vitamin D deficiency is a common condition that affects many patients with SIBO (Small Intestinal Bacterial Overgrowth). 
        
        Research studies have shown that probiotics such as Lactobacillus acidophilus and Bifidobacterium longum can help restore healthy gut microbiome balance. 
        
        Rifaximin is an antibiotic commonly prescribed for SIBO treatment, typically given at a dose of 550mg three times daily for 14 days.
        
        Patients with SIBO often experience symptoms including:
        - Bloating and abdominal distension
        - Abdominal pain and cramping  
        - Diarrhea or constipation
        - Excessive gas and flatulence
        - Malabsorption of nutrients
        
        Treatment approaches may include:
        - Antibiotic therapy (Rifaximin, Metronidazole)
        - Probiotic supplementation
        - Dietary modifications (Low FODMAP diet)
        - Nutritional support (<PERSON><PERSON>, B12, Iron)
        
        Dr<PERSON>'s research at Johns Hopkins University has demonstrated the effectiveness of combining antibiotic treatment with probiotic therapy for optimal SIBO management.
        """
        
        # Save test content to a file
        test_file_path = Path("test_sibo_document.txt")
        test_file_path.write_text(test_content)
        
        logger.info(f"Created test document: {test_file_path}")
        logger.info(f"Document size: {len(test_content)} characters")
        
        # Upload the document
        url = "http://localhost:8234/api/documents/upload"
        
        async with aiohttp.ClientSession() as session:
            with open(test_file_path, 'rb') as f:
                data = aiohttp.FormData()
                data.add_field('file', f, filename='test_sibo_document.txt', content_type='text/plain')
                data.add_field('group_id', 'test_group')
                data.add_field('upload_type', 'messages')  # Use correct Graphiti architecture
                
                logger.info("Uploading document...")
                async with session.post(url, data=data) as response:
                    if response.status == 202:
                        result = await response.json()
                        logger.info(f"✅ Upload successful: {result}")
                        
                        # Wait for processing to complete
                        filename = result['filename']
                        await wait_for_processing(session, filename)
                        
                        # Check the final status
                        await check_processing_results(session, filename)
                        
                    else:
                        error_text = await response.text()
                        logger.error(f"❌ Upload failed: {response.status} - {error_text}")
                        return False
        
        # Clean up
        test_file_path.unlink()
        return True
        
    except Exception as e:
        logger.error(f"❌ Error in document upload test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def wait_for_processing(session, filename, max_wait=120):
    """Wait for document processing to complete."""
    logger.info(f"Waiting for processing of {filename}...")
    
    start_time = time.time()
    while time.time() - start_time < max_wait:
        try:
            status_url = f"http://localhost:8234/api/processing/detailed-status/{filename}?group_id=test_group"
            async with session.get(status_url) as response:
                if response.status == 200:
                    status = await response.json()
                    processing_status = status.get('processing_status', 'unknown')
                    
                    logger.info(f"Processing status: {processing_status}")
                    
                    if processing_status == 'completed':
                        logger.info("✅ Processing completed!")
                        return True
                    elif processing_status == 'error':
                        logger.error(f"❌ Processing failed: {status.get('error_message', 'Unknown error')}")
                        return False
                    
                    # Wait before checking again
                    await asyncio.sleep(5)
                else:
                    logger.warning(f"Status check failed: {response.status}")
                    await asyncio.sleep(5)
                    
        except Exception as e:
            logger.warning(f"Error checking status: {e}")
            await asyncio.sleep(5)
    
    logger.warning(f"⚠️ Processing timeout after {max_wait} seconds")
    return False

async def check_processing_results(session, filename):
    """Check the final processing results."""
    try:
        status_url = f"http://localhost:8234/api/processing/detailed-status/{filename}?group_id=test_group"
        async with session.get(status_url) as response:
            if response.status == 200:
                status = await response.json()
                
                entities_count = status.get('entities_count', 0)
                references_count = status.get('references_count', 0)
                text_length = status.get('text_length', 0)
                episodes_count = status.get('episodes_count', 0)
                
                logger.info(f"\n=== PROCESSING RESULTS ===")
                logger.info(f"Episodes: {episodes_count}")
                logger.info(f"Entities: {entities_count}")
                logger.info(f"References: {references_count}")
                logger.info(f"Text Length: {text_length}")
                
                if entities_count > 0:
                    logger.info("✅ SUCCESS: Entity extraction is working!")
                elif episodes_count > 0:
                    logger.warning("⚠️ PARTIAL: Episode created but no entities extracted")
                    logger.warning("This suggests the Graphiti entity extraction is not working properly")
                else:
                    logger.error("❌ FAILED: No episodes or entities created")
                
                return entities_count > 0
                
    except Exception as e:
        logger.error(f"Error checking results: {e}")
        return False

async def main():
    """Main test function."""
    logger.info("Starting Document Upload and Entity Extraction Test")
    
    success = await test_document_upload()
    
    if success:
        logger.info("✅ Test completed successfully!")
    else:
        logger.error("❌ Test failed!")

if __name__ == "__main__":
    asyncio.run(main())

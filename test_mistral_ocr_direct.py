#!/usr/bin/env python3
"""
Test Mistral OCR directly to see if it's working
"""

import os
import asyncio
import base64
from mistralai import Mistral
from dotenv import load_dotenv

load_dotenv()

async def test_mistral_ocr_direct():
    """Test Mistral OCR API directly"""
    
    print("🔍 Testing Mistral OCR API directly...")
    
    # Check API key
    api_key = os.getenv('MISTRAL_API_KEY')
    if not api_key:
        print("❌ No Mistral API key found")
        return False
    
    print(f"✅ API Key found: {api_key[:10]}...")
    
    try:
        # Initialize client
        client = Mistral(api_key=api_key)
        print("✅ Mistral client initialized")
        
        # Test 1: Check available models
        print("\n📋 Step 1: Checking available models...")
        try:
            models = client.models.list()
            print(f"✅ Found {len(models.data)} models")
            
            # Look for OCR models
            ocr_models = [model for model in models.data if 'ocr' in model.id.lower()]
            if ocr_models:
                print(f"🔍 OCR Models found:")
                for model in ocr_models:
                    print(f"  - {model.id}")
            else:
                print("❌ No OCR models found in available models")
                print("Available models:")
                for model in models.data[:10]:  # Show first 10
                    print(f"  - {model.id}")
                    
        except Exception as e:
            print(f"❌ Error listing models: {e}")
        
        # Test 2: Test OCR with a simple document
        print("\n📄 Step 2: Testing OCR with simple text document...")
        
        # Create a simple test document
        test_content = """
        SIBO Test Document
        
        This is a test document for OCR processing.
        
        SIBO (Small Intestinal Bacterial Overgrowth) symptoms include:
        - Bloating
        - Gas
        - Abdominal pain
        - Diarrhea
        
        Treatment options:
        1. Antibiotics (Rifaximin)
        2. Herbal antimicrobials
        3. Dietary changes
        """
        
        # Convert to base64
        test_base64 = base64.b64encode(test_content.encode()).decode()
        document_url = f"data:text/plain;base64,{test_base64}"
        
        try:
            print("🔄 Attempting OCR processing...")
            
            # Try OCR processing
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: client.ocr.process(
                    model="mistral-ocr-latest",
                    document={
                        "type": "document_url",
                        "document_url": document_url
                    },
                    include_image_base64=False
                )
            )
            
            print("✅ OCR processing successful!")
            
            # Extract text from response
            if hasattr(response, 'content') and response.content:
                extracted_text = response.content
                print(f"📝 Extracted text ({len(extracted_text)} chars):")
                print(f"'{extracted_text[:200]}...'")
                return True
            else:
                print("❌ No content in OCR response")
                print(f"Response: {response}")
                return False
                
        except Exception as e:
            print(f"❌ OCR processing failed: {e}")
            print(f"Error type: {type(e)}")
            
            # Check if it's a rate limit error
            if "rate limit" in str(e).lower() or "429" in str(e):
                print("🚫 Rate limit detected!")
                return "rate_limit"
            elif "model" in str(e).lower() and "not found" in str(e).lower():
                print("🚫 Model not found - OCR may not be available")
                return "model_not_found"
            else:
                return False
        
    except Exception as e:
        print(f"❌ General error: {e}")
        return False

async def test_mistral_chat():
    """Test basic Mistral chat to verify API connectivity"""
    
    print("\n💬 Testing basic Mistral chat API...")
    
    api_key = os.getenv('MISTRAL_API_KEY')
    if not api_key:
        print("❌ No API key")
        return False
    
    try:
        client = Mistral(api_key=api_key)
        
        response = client.chat.complete(
            model="mistral-small-latest",
            messages=[
                {"role": "user", "content": "Hello, can you respond with just 'API working'?"}
            ]
        )
        
        if response.choices and response.choices[0].message:
            content = response.choices[0].message.content
            print(f"✅ Chat API working: {content}")
            return True
        else:
            print("❌ No response from chat API")
            return False
            
    except Exception as e:
        print(f"❌ Chat API error: {e}")
        return False

if __name__ == "__main__":
    async def main():
        # Test basic chat first
        chat_result = await test_mistral_chat()
        
        # Test OCR
        ocr_result = await test_mistral_ocr_direct()
        
        print(f"\n📊 Results:")
        print(f"  - Chat API: {'✅ Working' if chat_result else '❌ Failed'}")
        
        if ocr_result == True:
            print(f"  - OCR API: ✅ Working perfectly!")
        elif ocr_result == "rate_limit":
            print(f"  - OCR API: 🚫 Rate limited")
        elif ocr_result == "model_not_found":
            print(f"  - OCR API: 🚫 Model not available")
        else:
            print(f"  - OCR API: ❌ Failed")
    
    asyncio.run(main())

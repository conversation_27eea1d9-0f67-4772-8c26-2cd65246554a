#!/usr/bin/env python3
"""
Test Ollama connectivity and service status.
"""

import requests
import json
import time

def test_ollama_service():
    """Test if Ollama service is running and accessible."""
    print("🔍 TESTING OLLAMA CONNECTIVITY")
    print("=" * 60)
    
    # Test different Ollama URLs that might be configured
    test_urls = [
        "http://localhost:11434",
        "http://127.0.0.1:11434", 
        "http://host.docker.internal:11434"
    ]
    
    for url in test_urls:
        print(f"\n📡 Testing Ollama at: {url}")
        
        try:
            # Test basic connectivity
            response = requests.get(f"{url}/api/version", timeout=5)
            if response.status_code == 200:
                version_info = response.json()
                print(f"   ✅ Ollama service is running!")
                print(f"   📊 Version: {version_info.get('version', 'Unknown')}")
                return url
            else:
                print(f"   ❌ Service responded with status: {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            print(f"   ❌ Connection refused - service not running")
        except requests.exceptions.Timeout:
            print(f"   ⏰ Connection timeout")
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
    
    print(f"\n❌ Ollama service not accessible on any tested URL")
    return None

def test_ollama_models(base_url):
    """Test what models are available in Ollama."""
    print(f"\n📚 TESTING AVAILABLE MODELS")
    print("=" * 60)
    
    try:
        response = requests.get(f"{base_url}/api/tags", timeout=10)
        if response.status_code == 200:
            models_data = response.json()
            models = models_data.get('models', [])
            
            print(f"✅ Found {len(models)} available models:")
            for model in models:
                name = model.get('name', 'Unknown')
                size = model.get('size', 0)
                size_mb = size / (1024 * 1024) if size else 0
                modified = model.get('modified_at', 'Unknown')
                print(f"   📦 {name}")
                print(f"      Size: {size_mb:.1f} MB")
                print(f"      Modified: {modified}")
                print()
            
            return models
        else:
            print(f"❌ Failed to get models: {response.status_code}")
            return []
            
    except Exception as e:
        print(f"❌ Error getting models: {str(e)}")
        return []

def test_model_availability(base_url, model_name):
    """Test if a specific model is available and working."""
    print(f"\n🧪 TESTING MODEL: {model_name}")
    print("=" * 60)
    
    try:
        # Test simple generation
        test_prompt = "Hello, respond with just 'OK' if you can understand this."
        
        payload = {
            "model": model_name,
            "prompt": test_prompt,
            "stream": False,
            "options": {
                "temperature": 0.1,
                "num_predict": 10
            }
        }
        
        print(f"📤 Sending test prompt to {model_name}...")
        print(f"   Prompt: '{test_prompt}'")
        
        start_time = time.time()
        response = requests.post(
            f"{base_url}/api/generate",
            json=payload,
            timeout=60  # 1 minute timeout
        )
        end_time = time.time()
        
        if response.status_code == 200:
            result = response.json()
            response_text = result.get('response', '').strip()
            duration = end_time - start_time
            
            print(f"   ✅ Model responded successfully!")
            print(f"   📝 Response: '{response_text}'")
            print(f"   ⏱️  Duration: {duration:.2f} seconds")
            print(f"   📊 Total duration: {result.get('total_duration', 0) / 1e9:.2f}s")
            print(f"   🧠 Load duration: {result.get('load_duration', 0) / 1e9:.2f}s")
            
            return True
        else:
            print(f"   ❌ Model request failed: {response.status_code}")
            print(f"   📄 Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print(f"   ⏰ Model request timed out (>60s)")
        print(f"   💡 Model might be too large or system too slow")
        return False
    except Exception as e:
        print(f"   ❌ Error testing model: {str(e)}")
        return False

def test_json_generation(base_url, model_name):
    """Test if the model can generate proper JSON."""
    print(f"\n📋 TESTING JSON GENERATION: {model_name}")
    print("=" * 60)
    
    try:
        # Test JSON generation with medical entities
        test_prompt = """Extract medical entities from this text and respond with ONLY valid JSON:

Text: "Patient has SIBO. Treatment: Rifaximin."

Respond with JSON in this exact format:
{
  "extracted_entities": [
    {"name": "SIBO", "entity_type_id": 0},
    {"name": "Rifaximin", "entity_type_id": 0}
  ]
}

RESPOND WITH ONLY THE JSON - NO OTHER TEXT:"""
        
        payload = {
            "model": model_name,
            "prompt": test_prompt,
            "stream": False,
            "options": {
                "temperature": 0.1,
                "num_predict": 200
            }
        }
        
        print(f"📤 Testing JSON generation...")
        
        start_time = time.time()
        response = requests.post(
            f"{base_url}/api/generate",
            json=payload,
            timeout=120  # 2 minute timeout for JSON
        )
        end_time = time.time()
        
        if response.status_code == 200:
            result = response.json()
            response_text = result.get('response', '').strip()
            duration = end_time - start_time
            
            print(f"   ✅ Model responded!")
            print(f"   ⏱️  Duration: {duration:.2f} seconds")
            print(f"   📝 Raw response:")
            print(f"   {response_text}")
            
            # Try to parse as JSON
            try:
                parsed_json = json.loads(response_text)
                print(f"   ✅ Valid JSON generated!")
                print(f"   📊 Parsed: {json.dumps(parsed_json, indent=2)}")
                
                # Check if it matches Graphiti format
                if 'extracted_entities' in parsed_json:
                    entities = parsed_json['extracted_entities']
                    print(f"   🎯 Graphiti format detected!")
                    print(f"   📦 Found {len(entities)} entities")
                    return True
                else:
                    print(f"   ⚠️  JSON valid but not Graphiti format")
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"   ❌ Invalid JSON: {str(e)}")
                return False
                
        else:
            print(f"   ❌ Request failed: {response.status_code}")
            print(f"   📄 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error testing JSON generation: {str(e)}")
        return False

def main():
    """Main connectivity test."""
    print("🔧 OLLAMA CONNECTIVITY DIAGNOSTIC")
    print("🎯 Goal: Identify why Ollama API calls are failing")
    print()
    
    # Test service connectivity
    base_url = test_ollama_service()
    if not base_url:
        print("\n💡 RECOMMENDATIONS:")
        print("1. Start Ollama service: 'ollama serve'")
        print("2. Check if Ollama is installed: 'ollama --version'")
        print("3. Verify port 11434 is not blocked")
        return False
    
    # Test available models
    models = test_ollama_models(base_url)
    if not models:
        print("\n💡 No models found. Pull a model first: 'ollama pull llama3.1:8b'")
        return False
    
    # Test our target model
    target_models = ['llama3.1:8b', 'llama3.1', 'llama3:8b', 'llama3']
    working_model = None
    
    for model_name in target_models:
        # Check if model exists
        model_exists = any(model.get('name', '').startswith(model_name) for model in models)
        if model_exists:
            print(f"\n✅ Found model: {model_name}")
            if test_model_availability(base_url, model_name):
                working_model = model_name
                break
        else:
            print(f"\n❌ Model not found: {model_name}")
    
    if working_model:
        # Test JSON generation
        json_works = test_json_generation(base_url, working_model)
        
        print(f"\n🎉 CONNECTIVITY TEST RESULTS:")
        print(f"✅ Ollama service: Running at {base_url}")
        print(f"✅ Working model: {working_model}")
        print(f"{'✅' if json_works else '⚠️ '} JSON generation: {'Working' if json_works else 'Needs improvement'}")
        
        return True
    else:
        print(f"\n❌ No working models found")
        print(f"💡 Try pulling a smaller model: 'ollama pull llama3.2:1b'")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n✅ Ollama connectivity confirmed!")
        print(f"🔧 The issue may be in our client configuration or timeout settings")
    else:
        print(f"\n❌ Ollama connectivity issues found")
        print(f"🔧 Fix Ollama setup before testing entity extraction")

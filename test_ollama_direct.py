#!/usr/bin/env python3
"""
Test Ollama MedGemma OCR service directly
"""

import asyncio
import sys
import os

# Add the server path to import the service
sys.path.append('server')

from graph_service.services.ollama_ocr_service import OllamaOCRService

async def test_ollama_direct():
    """Test Ollama OCR service directly"""
    
    print("🔍 Testing Ollama MedGemma OCR Service Directly...")
    
    # Initialize service
    ollama_service = OllamaOCRService()
    
    # Step 1: Health check
    print("\n📊 Step 1: Health check...")
    health_status = await ollama_service.health_check()
    print(f"Health Status: {health_status}")
    
    if health_status.get('status') != 'healthy':
        print("❌ Ollama service not healthy, cannot proceed")
        return False
    
    # Step 2: Test text extraction
    print("\n📄 Step 2: Testing text extraction...")
    
    test_content = """
    SIBO Treatment Protocol
    
    Patient presents with chronic bloating and gas.
    
    Diagnosis: Small Intestinal Bacterial Overgrowth
    
    Treatment Plan:
    1. Rifaximin 550mg TID x 14 days
    2. Low FODMAP diet
    3. Prokinetic support
    4. Probiotic restoration
    
    Follow-up in 4 weeks.
    """
    
    try:
        # Convert to bytes
        content_bytes = test_content.encode('utf-8')
        
        # Extract text
        result = await ollama_service.extract_text(content_bytes, "test_sibo.txt")
        
        if result:
            print("✅ Text extraction successful!")
            print(f"📝 Original length: {len(test_content)} characters")
            print(f"📝 Enhanced length: {len(result)} characters")
            print(f"📈 Enhancement ratio: {len(result) / len(test_content):.2f}x")
            
            print("\n📋 Enhanced text preview:")
            print("=" * 50)
            print(result[:500] + "..." if len(result) > 500 else result)
            print("=" * 50)
            
            # Check if it looks enhanced
            if len(result) > len(test_content) * 1.2:
                print("🔬 ✅ Text appears to be enhanced by MedGemma!")
            else:
                print("📄 ✅ Text extracted (may not be enhanced)")
            
            return True
        else:
            print("❌ Text extraction failed")
            return False
            
    except Exception as e:
        print(f"❌ Error during text extraction: {e}")
        return False

async def test_ollama_api_direct():
    """Test Ollama API directly"""
    
    print("\n🔗 Step 3: Testing Ollama API directly...")
    
    import aiohttp
    
    try:
        async with aiohttp.ClientSession() as session:
            # Test basic connectivity
            async with session.get("http://host.docker.internal:11434/api/tags", timeout=10) as response:
                if response.status == 200:
                    models = await response.json()
                    print("✅ Ollama API accessible")
                    
                    model_names = [model['name'] for model in models.get('models', [])]
                    print(f"📋 Available models: {model_names}")
                    
                    if any('meditron' in name.lower() for name in model_names):
                        print("✅ Meditron model available!")

                        # Test a simple generation
                        payload = {
                            "model": "meditron:7b",
                            "prompt": "What is SIBO? Provide a brief medical definition.",
                            "stream": False,
                            "options": {"temperature": 0.1, "num_predict": 100}
                        }
                        
                        async with session.post(
                            "http://host.docker.internal:11434/api/generate",
                            json=payload,
                            timeout=30
                        ) as gen_response:
                            
                            if gen_response.status == 200:
                                result = await gen_response.json()
                                response_text = result.get('response', '').strip()
                                
                                if response_text:
                                    print("✅ Meditron generation successful!")
                                    print(f"📝 Response: {response_text}")
                                    return True
                                else:
                                    print("❌ Empty response from Meditron")
                                    return False
                            else:
                                print(f"❌ Generation failed: {gen_response.status}")
                                return False
                    else:
                        print("❌ Meditron model not found")
                        return False
                else:
                    print(f"❌ Ollama API error: {response.status}")
                    return False
                    
    except Exception as e:
        print(f"❌ Ollama API test error: {e}")
        return False

if __name__ == "__main__":
    async def main():
        print("🚀 Testing Ollama MedGemma OCR Service")
        print("=" * 50)
        
        # Test health check
        health_ok = True
        try:
            ollama_service = OllamaOCRService()
            health_status = await ollama_service.health_check()
            if health_status.get('status') != 'healthy':
                health_ok = False
        except Exception as e:
            print(f"❌ Health check failed: {e}")
            health_ok = False
        
        # Test API directly
        api_ok = await test_ollama_api_direct()
        
        # Test full service if health is OK
        service_ok = False
        if health_ok:
            service_ok = await test_ollama_direct()
        
        print("\n" + "=" * 50)
        print("📊 FINAL RESULTS:")
        print(f"  - Ollama API: {'✅ Working' if api_ok else '❌ Failed'}")
        print(f"  - Service Health: {'✅ Healthy' if health_ok else '❌ Unhealthy'}")
        print(f"  - Text Enhancement: {'✅ Working' if service_ok else '❌ Failed'}")
        
        if api_ok and health_ok and service_ok:
            print("\n🎉 OLLAMA MEDITRON IS FULLY FUNCTIONAL!")
            print("✅ Ready to serve as PRIMARY OCR for SIBO documents")
            print("✅ Medical text enhancement available")
            print("✅ SIBO document processing ready")
        else:
            print("\n❌ OLLAMA MEDITRON HAS ISSUES")
            if not api_ok:
                print("❌ Check Ollama installation and MedGemma model")
            if not health_ok:
                print("❌ Service health check failed")
            if not service_ok:
                print("❌ Text enhancement not working")
    
    asyncio.run(main())

#!/usr/bin/env python3
"""
Test the fixed document upload with MedGemma entity extraction.
"""

import requests
import tempfile
import os
import time

def create_test_pdf():
    """Create a test PDF with clear medical entities."""
    try:
        from reportlab.pdfgen import canvas
        
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
        temp_path = temp_file.name
        temp_file.close()
        
        c = canvas.Canvas(temp_path)
        c.drawString(100, 750, "MEDICAL REPORT - UPLOAD FIX TEST")
        c.drawString(100, 720, "Patient: <PERSON>")
        c.drawString(100, 690, "Date: 2024-06-16")
        c.drawString(100, 660, "Diagnosis: Hypertension and Type 2 Diabetes")
        c.drawString(100, 630, "Treatment: Metformin 500mg twice daily")
        c.drawString(100, 600, "Blood Pressure: Lisinopril 10mg once daily")
        c.drawString(100, 570, "Follow-up: HbA1c test in 3 months")
        c.drawString(100, 540, "Dr. <PERSON>, MD - Internal Medicine")
        c.save()
        
        return temp_path
    except Exception as e:
        print(f"Error creating PDF: {e}")
        return None

def test_upload_fix():
    """Test the fixed document upload."""
    print("🔧 TESTING FIXED DOCUMENT UPLOAD")
    print("=" * 50)
    
    pdf_path = create_test_pdf()
    if not pdf_path:
        return False
    
    try:
        print("📤 Uploading test document...")
        with open(pdf_path, 'rb') as f:
            files = {'file': ('upload_fix_test.pdf', f, 'application/pdf')}
            data = {
                'group_id': 'medical_docs',
                'upload_type': 'messages'
            }
            
            response = requests.post(
                'http://127.0.0.1:8234/api/documents/upload',
                files=files,
                data=data,
                timeout=30
            )
            
            print(f"Status: {response.status_code}")
            if response.status_code == 202:
                result = response.json()
                print("✅ Upload successful!")
                print(f"Response: {result}")
                
                print("\n⏳ Processing with fixed MedGemma...")
                print("Expected entities:")
                print("  - Sarah Wilson, Hypertension, Type 2 Diabetes")
                print("  - Metformin, 500mg, twice daily")
                print("  - Lisinopril, 10mg, once daily")
                print("  - HbA1c test, Dr. Emily Chen, Internal Medicine")
                
                # Wait for processing
                for i in range(30):
                    print(f"⏳ Processing... {i+1}/30 seconds", end='\r')
                    time.sleep(1)
                
                print("\n\n🔍 Checking results...")
                status_response = requests.get(
                    f'http://127.0.0.1:8234/api/processing/detailed-status/upload_fix_test.pdf?group_id=medical_docs',
                    timeout=10
                )
                
                if status_response.status_code == 200:
                    status = status_response.json()
                    print("📊 PROCESSING RESULTS:")
                    print("=" * 40)
                    print(f"   📋 Status: {status.get('processing_status')}")
                    print(f"   📝 Text: {status.get('text_length')} characters")
                    print(f"   📚 Episodes: {status.get('episodes_count')}")
                    print(f"   🏷️  Entities: {status.get('entities_count')}")
                    print(f"   👁️  OCR: {status.get('ocr_status')}")
                    print(f"   🧠 Entity Extraction: {status.get('entity_extraction_status')}")
                    
                    if status.get('processing_status') == 'completed':
                        entities_count = status.get('entities_count', 0)
                        if entities_count > 0:
                            print(f"\n🎉 SUCCESS! Fixed upload extracted {entities_count} entities!")
                            return True
                        else:
                            print("\n⚠️ Upload processed but no entities extracted")
                            return False
                    elif status.get('processing_status') == 'processing':
                        print("\n⏳ Still processing - check server logs")
                        return False
                    else:
                        print(f"\n❌ Processing failed: {status.get('processing_status')}")
                        return False
                else:
                    print(f"❌ Status check failed: {status_response.status_code}")
                    return False
                
            else:
                print(f"❌ Upload failed: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        if os.path.exists(pdf_path):
            os.unlink(pdf_path)

if __name__ == "__main__":
    print("🔧 DOCUMENT UPLOAD FIX TEST")
    print("Testing if the JSON parsing fix resolves the upload issue")
    print("=" * 60)
    
    success = test_upload_fix()
    
    if success:
        print("\n🎉 DOCUMENT UPLOAD FIXED!")
        print("MedGemma entity extraction is working correctly!")
    else:
        print("\n🔧 Check server logs for detailed processing information")

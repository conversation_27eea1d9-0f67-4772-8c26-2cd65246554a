#!/usr/bin/env python3
"""
Simple test to verify basic document processing and entity creation.
This will help us identify where the pipeline is failing.
"""

import asyncio
import logging
import os
import sys
from datetime import datetime, timezone
from dotenv import load_dotenv

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_neo4j_connection():
    """Test Neo4j database connection."""
    try:
        from graphiti_core import Graphiti

        # Force the correct URI for our Docker setup
        neo4j_uri = 'bolt://localhost:7891'  # Docker mapped port
        neo4j_user = os.getenv('NEO4J_USER', 'neo4j')
        neo4j_password = os.getenv('NEO4J_PASSWORD', 'Triathlon16')

        logger.info(f"Environment NEO4J_URI: {os.getenv('NEO4J_URI', 'NOT SET')}")
        logger.info(f"Using Neo4j connection to {neo4j_uri}")
        logger.info(f"User: {neo4j_user}, Password: {'*' * len(neo4j_password) if neo4j_password else 'NOT SET'}")

        graphiti = Graphiti(
            uri=neo4j_uri,
            user=neo4j_user,
            password=neo4j_password
        )

        # Test connection by building indices
        await graphiti.build_indices_and_constraints()
        logger.info("✅ Neo4j connection successful")

        await graphiti.close()
        return True

    except Exception as e:
        logger.error(f"❌ Neo4j connection failed: {str(e)}")
        return False

async def test_openrouter_api():
    """Test OpenRouter API connection."""
    try:
        import aiohttp
        
        api_key = os.getenv('OPENAI_API_KEY')
        base_url = os.getenv('OPENAI_BASE_URL', 'https://openrouter.ai/api/v1')
        
        if not api_key:
            logger.error("❌ OpenRouter API key not found")
            return False
        
        logger.info(f"Testing OpenRouter API at {base_url}")
        
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{base_url}/models", headers=headers) as response:
                if response.status == 200:
                    logger.info("✅ OpenRouter API connection successful")
                    return True
                else:
                    logger.error(f"❌ OpenRouter API failed with status {response.status}")
                    return False
                    
    except Exception as e:
        logger.error(f"❌ OpenRouter API test failed: {str(e)}")
        return False

async def test_simple_entity_extraction():
    """Test simple entity extraction with OpenRouter."""
    try:
        import aiohttp
        import json
        
        api_key = os.getenv('OPENAI_API_KEY')
        base_url = os.getenv('OPENAI_BASE_URL', 'https://openrouter.ai/api/v1')
        model = os.getenv('MODEL_NAME', 'meta-llama/llama-4-maverick:free')
        
        test_text = "Vitamin D deficiency is common in SIBO patients. Probiotics can help restore gut health."
        
        logger.info(f"Testing entity extraction with model {model}")
        
        prompt = f"""Extract medical entities from the following text. Return only a JSON list of entities with name, type, and description.

Text: {test_text}

Return format:
[
  {{"name": "entity_name", "type": "entity_type", "description": "brief_description"}}
]

Entities:"""

        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        
        payload = {
            'model': model,
            'messages': [
                {'role': 'user', 'content': prompt}
            ],
            'temperature': 0.1,
            'max_tokens': 500
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(f"{base_url}/chat/completions", headers=headers, json=payload) as response:
                if response.status == 200:
                    result = await response.json()
                    content = result['choices'][0]['message']['content']
                    logger.info(f"✅ Entity extraction successful: {content[:200]}...")
                    return True
                else:
                    error_text = await response.text()
                    logger.error(f"❌ Entity extraction failed with status {response.status}: {error_text}")
                    return False
                    
    except Exception as e:
        logger.error(f"❌ Entity extraction test failed: {str(e)}")
        return False

async def test_graphiti_episode_creation():
    """Test creating a simple episode in Graphiti."""
    try:
        from graphiti_core import Graphiti
        from graphiti_core.nodes import EpisodeType

        # Force the correct URI for our Docker setup
        neo4j_uri = 'bolt://localhost:7891'  # Docker mapped port
        neo4j_user = os.getenv('NEO4J_USER', 'neo4j')
        neo4j_password = os.getenv('NEO4J_PASSWORD', 'Triathlon16')

        logger.info("Testing Graphiti episode creation")
        logger.info(f"Using Neo4j connection to {neo4j_uri}")

        graphiti = Graphiti(
            uri=neo4j_uri,
            user=neo4j_user,
            password=neo4j_password
        )
        
        # Configure OpenRouter
        openai_api_key = os.getenv('OPENAI_API_KEY')
        openai_base_url = os.getenv('OPENAI_BASE_URL')
        model_name = os.getenv('MODEL_NAME', 'meta-llama/llama-4-maverick:free')
        
        if openai_base_url:
            graphiti.llm_client.config.base_url = openai_base_url
        if openai_api_key:
            graphiti.llm_client.config.api_key = openai_api_key
        if model_name:
            graphiti.llm_client.model = model_name
        
        # Initialize database
        await graphiti.build_indices_and_constraints()
        
        # Test with simple medical text
        test_text = "Vitamin D deficiency is common in SIBO patients. Probiotics can help restore gut health."
        
        logger.info(f"Creating episode with text: {test_text}")
        
        result = await graphiti.add_episode(
            name="Simple Medical Test",
            episode_body=test_text,
            source=EpisodeType.message,
            source_description="Simple test for medical entity extraction",
            reference_time=datetime.now(timezone.utc),
            group_id="test_group"
        )
        
        logger.info(f"✅ Episode created successfully!")
        logger.info(f"  Episode UUID: {result.episode.uuid}")
        logger.info(f"  Entities created: {len(result.nodes) if result.nodes else 0}")
        logger.info(f"  Relationships created: {len(result.edges) if result.edges else 0}")
        
        if result.nodes:
            logger.info("  Entities found:")
            for i, node in enumerate(result.nodes[:5]):  # Show first 5
                logger.info(f"    {i+1}. {node.name} (Type: {getattr(node, 'entity_type', 'Unknown')})")
        
        # Clean up
        await graphiti.delete_group("test_group")
        await graphiti.close()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Graphiti episode creation failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all tests to identify issues."""
    logger.info("=== STARTING DIAGNOSTIC TESTS ===")
    
    tests = [
        ("Neo4j Connection", test_neo4j_connection),
        ("OpenRouter API", test_openrouter_api),
        ("Entity Extraction", test_simple_entity_extraction),
        ("Graphiti Episode Creation", test_graphiti_episode_creation),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n--- Testing {test_name} ---")
        try:
            results[test_name] = await test_func()
        except Exception as e:
            logger.error(f"Test {test_name} crashed: {str(e)}")
            results[test_name] = False
    
    logger.info("\n=== TEST RESULTS ===")
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        logger.info(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        logger.info("\n🎉 ALL TESTS PASSED! The core system is working.")
        logger.info("The issue may be in the document upload pipeline or UI integration.")
    else:
        logger.info("\n🚨 SOME TESTS FAILED! These need to be fixed first.")
    
    return all_passed

if __name__ == "__main__":
    asyncio.run(main())

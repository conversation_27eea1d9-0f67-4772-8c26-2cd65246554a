#!/usr/bin/env python3
"""
Debug the text extraction issue in the upload pipeline.
"""

import os
import sys
import asyncio
import tempfile
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "server"))

def create_simple_test_pdf():
    """Create a simple test PDF."""
    try:
        from reportlab.pdfgen import canvas
        
        # Create temporary PDF
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
        temp_path = temp_file.name
        temp_file.close()
        
        # Create simple PDF
        c = canvas.Canvas(temp_path)
        c.drawString(100, 750, "MEDICAL REPORT")
        c.drawString(100, 720, "Patient: <PERSON>")
        c.drawString(100, 690, "Diagnosis: SIBO")
        c.drawString(100, 660, "Treatment: Rifaximin 550mg twice daily")
        c.drawString(100, 630, "Probiotics: Lactobacillus acidophilus")
        c.drawString(100, 600, "Diet: Low-FODMAP diet")
        c.drawString(100, 570, "Supplements: Vitamin B12, Vitamin D3")
        c.drawString(100, 540, "Follow-up: Breath test in 4 weeks")
        c.drawString(100, 510, "Dr. <PERSON>, MD")
        c.save()
        
        print(f"Created test PDF: {temp_path}")
        return temp_path
        
    except Exception as e:
        print(f"Error creating PDF: {str(e)}")
        return None

async def test_direct_extraction():
    """Test the extract_text_only function directly."""
    try:
        from graph_service.routers.document_upload import extract_text_only
        
        print("🔄 Testing direct extraction...")
        
        # Create test PDF
        pdf_path = create_simple_test_pdf()
        if not pdf_path:
            return False
        
        try:
            # Read PDF content
            with open(pdf_path, 'rb') as f:
                pdf_content = f.read()
            
            print(f"PDF size: {len(pdf_content)} bytes")
            
            # Test extraction
            extracted_text = await extract_text_only(pdf_content, "debug_test.pdf")
            
            print(f"Extracted text length: {len(extracted_text)} characters")
            print(f"Extracted text preview: '{extracted_text[:200]}...'")
            
            if len(extracted_text) > 100:
                print("✅ Direct extraction working well")
                return True
            else:
                print("❌ Direct extraction returning minimal text")
                return False
                
        finally:
            if os.path.exists(pdf_path):
                os.unlink(pdf_path)
                
    except Exception as e:
        print(f"❌ Direct extraction test failed: {str(e)}")
        return False

async def test_mistral_ocr_directly():
    """Test Mistral OCR service directly."""
    try:
        from graph_service.services.mistral_ocr_service import MistralOCRService
        import base64
        
        print("🔄 Testing Mistral OCR directly...")
        
        # Create test PDF
        pdf_path = create_simple_test_pdf()
        if not pdf_path:
            return False
        
        try:
            # Read PDF content
            with open(pdf_path, 'rb') as f:
                pdf_content = f.read()
            
            # Initialize Mistral OCR
            mistral_ocr = MistralOCRService()
            
            if not mistral_ocr.is_available():
                print("❌ Mistral OCR not available")
                return False
            
            # Convert to base64
            pdf_base64 = base64.b64encode(pdf_content).decode('utf-8')
            document_url = f"data:application/pdf;base64,{pdf_base64}"
            
            # Extract text
            extracted_text = await mistral_ocr.extract_text(document_url, "document_url")
            
            print(f"Mistral OCR result length: {len(extracted_text) if extracted_text else 0} characters")
            if extracted_text:
                print(f"Mistral OCR preview: '{extracted_text[:200]}...'")
            
            if extracted_text and len(extracted_text) > 100:
                print("✅ Mistral OCR working well")
                return True
            else:
                print("❌ Mistral OCR returning minimal text")
                return False
                
        finally:
            if os.path.exists(pdf_path):
                os.unlink(pdf_path)
                
    except Exception as e:
        print(f"❌ Mistral OCR test failed: {str(e)}")
        return False

async def test_fallback_extraction():
    """Test the fallback PDF extraction."""
    try:
        from graph_service.routers.document_upload import _fallback_pdf_extraction
        
        print("🔄 Testing fallback extraction...")
        
        # Create test PDF
        pdf_path = create_simple_test_pdf()
        if not pdf_path:
            return False
        
        try:
            # Read PDF content
            with open(pdf_path, 'rb') as f:
                pdf_content = f.read()
            
            # Test fallback extraction
            extracted_text = await _fallback_pdf_extraction(pdf_content, "debug_test.pdf")
            
            print(f"Fallback extraction length: {len(extracted_text)} characters")
            print(f"Fallback extraction preview: '{extracted_text[:200]}...'")
            
            if len(extracted_text) > 50:
                print("✅ Fallback extraction working")
                return True
            else:
                print("❌ Fallback extraction returning minimal text")
                return False
                
        finally:
            if os.path.exists(pdf_path):
                os.unlink(pdf_path)
                
    except Exception as e:
        print(f"❌ Fallback extraction test failed: {str(e)}")
        return False

async def main():
    """Run debug tests."""
    print("🔍 DEBUGGING TEXT EXTRACTION ISSUE")
    print("=" * 50)
    
    # Test Mistral OCR directly
    mistral_ok = await test_mistral_ocr_directly()
    print("")
    
    # Test fallback extraction
    fallback_ok = await test_fallback_extraction()
    print("")
    
    # Test direct extraction function
    direct_ok = await test_direct_extraction()
    print("")
    
    # Summary
    print("📋 Debug Results:")
    print(f"   Mistral OCR Direct: {'✅ Working' if mistral_ok else '❌ Failed'}")
    print(f"   Fallback Extraction: {'✅ Working' if fallback_ok else '❌ Failed'}")
    print(f"   Upload Function: {'✅ Working' if direct_ok else '❌ Failed'}")
    
    if direct_ok:
        print("\n✅ Text extraction is working correctly!")
        print("The issue may be in the status tracking or response handling.")
    elif mistral_ok or fallback_ok:
        print("\n⚠️ Individual components work but integration has issues")
        print("Need to check the upload endpoint integration")
    else:
        print("\n❌ Multiple extraction methods failing")
        print("Need to investigate API keys and service availability")

if __name__ == "__main__":
    asyncio.run(main())

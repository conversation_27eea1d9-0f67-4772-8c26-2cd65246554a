#!/usr/bin/env python3
"""
Test PDF upload from terminal to ensure it works before fixing frontend.
"""

import requests
import tempfile
import os
import time
import json

def create_test_pdf():
    """Create a simple test PDF."""
    try:
        from reportlab.pdfgen import canvas
        
        # Create temporary PDF
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
        temp_path = temp_file.name
        temp_file.close()
        
        # Create PDF with medical content
        c = canvas.Canvas(temp_path)
        c.drawString(100, 750, "MEDICAL REPORT")
        c.drawString(100, 720, "Patient: <PERSON>")
        c.drawString(100, 690, "Date: 2024-06-13")
        c.drawString(100, 660, "Diagnosis: SIBO (Small Intestinal Bacterial Overgrowth)")
        c.drawString(100, 630, "Treatment: Rifaximin 550mg twice daily for 14 days")
        c.drawString(100, 600, "Probiotics: Lactobacillus acidophilus 10 billion CFU")
        c.drawString(100, 570, "Diet: Low-FODMAP diet with reduced fermentable carbs")
        c.drawString(100, 540, "Supplements: Vitamin B12 1000mcg, Vitamin D3 2000 IU")
        c.drawString(100, 510, "Follow-up: Breath test in 4 weeks to assess response")
        c.drawString(100, 480, "Dr. Sarah Johnson, MD - Gastroenterology Department")
        c.save()
        
        print(f"✅ Created test PDF: {temp_path}")
        return temp_path
        
    except Exception as e:
        print(f"❌ Error creating PDF: {str(e)}")
        return None

def test_upload_with_curl():
    """Test upload using requests (simulating curl)."""
    print("🔄 Testing PDF upload via HTTP request...")
    
    pdf_path = create_test_pdf()
    if not pdf_path:
        return False
    
    try:
        # Test the upload
        with open(pdf_path, 'rb') as f:
            files = {
                'file': ('terminal_test.pdf', f, 'application/pdf')
            }
            data = {
                'group_id': 'terminal_test_group',
                'upload_type': 'episodes'
            }
            
            print("📤 Uploading PDF...")
            response = requests.post(
                'http://localhost:8234/api/documents/upload',
                files=files,
                data=data,
                timeout=30
            )
            
            print(f"Response status: {response.status_code}")
            print(f"Response headers: {dict(response.headers)}")
            
            if response.status_code == 202:
                result = response.json()
                print("✅ Upload successful!")
                print(f"Response: {json.dumps(result, indent=2)}")
                
                # Wait for processing
                print("⏳ Waiting for processing...")
                time.sleep(10)
                
                # Check status
                try:
                    status_response = requests.get(
                        f'http://localhost:8234/api/processing/detailed-status/terminal_test.pdf?group_id=terminal_test_group',
                        timeout=10
                    )
                    
                    if status_response.status_code == 200:
                        status = status_response.json()
                        print("📊 Processing Status:")
                        print(json.dumps(status, indent=2))
                    else:
                        print(f"Status check returned: {status_response.status_code}")
                        print(f"Status response: {status_response.text}")
                        
                except Exception as e:
                    print(f"Status check failed: {str(e)}")
                
                return True
            else:
                print(f"❌ Upload failed with status {response.status_code}")
                print(f"Response: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ Upload test failed: {str(e)}")
        return False
    finally:
        if os.path.exists(pdf_path):
            os.unlink(pdf_path)

def test_server_endpoints():
    """Test various server endpoints."""
    print("🔍 Testing server endpoints...")
    
    endpoints = [
        ('GET', 'http://localhost:8234/api/documents/services/status', 'Service status'),
        ('OPTIONS', 'http://localhost:8234/api/documents/upload', 'Upload OPTIONS'),
        ('GET', 'http://localhost:8234/', 'Root endpoint'),
        ('GET', 'http://localhost:8234/static/', 'Static files'),
    ]
    
    for method, url, description in endpoints:
        try:
            if method == 'GET':
                response = requests.get(url, timeout=5)
            elif method == 'OPTIONS':
                response = requests.options(url, timeout=5)
            
            print(f"✅ {description}: {response.status_code}")
            if response.status_code >= 400:
                print(f"   Response: {response.text[:100]}...")
                
        except Exception as e:
            print(f"❌ {description}: Failed - {str(e)}")

def test_frontend_files():
    """Check if frontend files are accessible."""
    print("🔍 Checking frontend files...")
    
    frontend_files = [
        'http://localhost:8234/',
        'http://localhost:8234/static/index.html',
        'http://localhost:8234/static/js/app.js',
        'http://localhost:8234/static/css/style.css'
    ]
    
    for url in frontend_files:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"✅ {url}: Available")
            else:
                print(f"❌ {url}: {response.status_code}")
        except Exception as e:
            print(f"❌ {url}: Failed - {str(e)}")

def main():
    """Run terminal upload tests."""
    print("🚀 TERMINAL PDF UPLOAD TEST")
    print("Testing upload functionality before fixing frontend")
    print("=" * 60)
    
    # Test server endpoints
    test_server_endpoints()
    print("")
    
    # Test frontend files
    test_frontend_files()
    print("")
    
    # Test actual upload
    upload_success = test_upload_with_curl()
    print("")
    
    # Summary
    print("📋 Terminal Upload Test Results:")
    if upload_success:
        print("✅ PDF upload working from terminal")
        print("✅ Backend processing functional")
        print("✅ Ready to fix frontend issues")
    else:
        print("❌ PDF upload failing from terminal")
        print("❌ Need to fix backend first")
    
    return upload_success

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Simple Neo4j connection test.
"""

import asyncio
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_neo4j():
    """Test Neo4j connection."""
    try:
        from graphiti_core import Graphiti
        
        # Use the correct Docker mapped port
        neo4j_uri = 'bolt://localhost:7891'
        neo4j_user = 'neo4j'
        neo4j_password = 'Triathlon16'
        
        logger.info(f"Connecting to Neo4j at {neo4j_uri}")
        
        graphiti = Graphiti(
            uri=neo4j_uri,
            user=neo4j_user,
            password=neo4j_password
        )
        
        logger.info("Graphiti instance created, testing connection...")
        
        # Test connection
        await graphiti.build_indices_and_constraints()
        
        logger.info("✅ Neo4j connection successful!")
        
        await graphiti.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Neo4j connection failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_neo4j())

#!/usr/bin/env python3
"""
Test raw OpenRouter API to see what's happening with the responses.
"""

import asyncio
import logging
import os
import json
from dotenv import load_dotenv
import aiohttp

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_openrouter_raw():
    """Test raw OpenRouter API call."""
    try:
        api_key = os.getenv('OPENAI_API_KEY')
        base_url = os.getenv('OPENAI_BASE_URL', 'https://openrouter.ai/api/v1')
        model = os.getenv('MODEL_NAME', 'meta-llama/llama-4-maverick:free')
        
        logger.info(f"Testing raw OpenRouter API")
        logger.info(f"Model: {model}")
        logger.info(f"Base URL: {base_url}")
        
        test_text = "Vitamin D deficiency is common in SIBO patients. Probiotics can help restore gut health."
        
        # Simple prompt without structured output
        prompt = f"""Extract medical entities from this text and return them as a simple JSON list:

Text: {test_text}

Return format:
[
  {{"name": "Vitamin D", "type": "nutrient", "description": "Essential vitamin"}},
  {{"name": "SIBO", "type": "condition", "description": "Small intestinal bacterial overgrowth"}}
]

Entities:"""

        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        
        payload = {
            'model': model,
            'messages': [
                {'role': 'user', 'content': prompt}
            ],
            'temperature': 0.1,
            'max_tokens': 1000
        }
        
        logger.info("Making API call...")
        
        async with aiohttp.ClientSession() as session:
            async with session.post(f"{base_url}/chat/completions", headers=headers, json=payload) as response:
                if response.status == 200:
                    result = await response.json()
                    content = result['choices'][0]['message']['content']
                    logger.info(f"✅ API call successful!")
                    logger.info(f"Response content: {content}")
                    
                    # Try to parse as JSON
                    try:
                        entities = json.loads(content)
                        logger.info(f"✅ JSON parsing successful! Found {len(entities)} entities")
                        for entity in entities:
                            logger.info(f"  - {entity.get('name', 'Unknown')} ({entity.get('type', 'Unknown')})")
                    except json.JSONDecodeError as e:
                        logger.warning(f"⚠️ JSON parsing failed: {e}")
                        logger.info("Raw response content:")
                        logger.info(content)
                    
                    return True
                else:
                    error_text = await response.text()
                    logger.error(f"❌ API call failed with status {response.status}: {error_text}")
                    return False
                    
    except Exception as e:
        logger.error(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_different_models():
    """Test different models to find one that works better."""
    models_to_test = [
        'meta-llama/llama-3.1-8b-instruct:free',
        'microsoft/phi-3-mini-128k-instruct:free',
        'google/gemma-2-9b-it:free',
        'meta-llama/llama-4-maverick:free'  # Current problematic model
    ]
    
    api_key = os.getenv('OPENAI_API_KEY')
    base_url = os.getenv('OPENAI_BASE_URL', 'https://openrouter.ai/api/v1')
    
    test_text = "Vitamin D deficiency is common in SIBO patients."
    
    prompt = f"""Extract entities from: {test_text}

Return JSON: [{{"name": "entity", "type": "type"}}]"""

    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    for model in models_to_test:
        logger.info(f"\n--- Testing model: {model} ---")
        
        payload = {
            'model': model,
            'messages': [{'role': 'user', 'content': prompt}],
            'temperature': 0.1,
            'max_tokens': 500
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(f"{base_url}/chat/completions", headers=headers, json=payload) as response:
                    if response.status == 200:
                        result = await response.json()
                        content = result['choices'][0]['message']['content']
                        logger.info(f"✅ {model}: Response received")
                        logger.info(f"Content: {content[:200]}...")
                        
                        # Check if it looks like valid JSON
                        if content.strip().startswith('[') and content.strip().endswith(']'):
                            logger.info(f"✅ {model}: Looks like valid JSON format")
                        else:
                            logger.warning(f"⚠️ {model}: Doesn't look like JSON")
                    else:
                        logger.error(f"❌ {model}: Failed with status {response.status}")
        except Exception as e:
            logger.error(f"❌ {model}: Exception - {str(e)}")

if __name__ == "__main__":
    asyncio.run(test_openrouter_raw())
    print("\n" + "="*50)
    asyncio.run(test_different_models())

#!/usr/bin/env python3
"""
Test Ollama API for local LLM functionality
"""

import asyncio
import aiohttp
import json
import time

async def test_ollama_chat():
    """Test Ollama chat completion"""
    print("🔍 Testing Ollama Chat API...")
    
    payload = {
        "model": "llama3.2:latest",
        "messages": [
            {
                "role": "user",
                "content": "Extract medical entities from this text: '<PERSON>IB<PERSON> (Small Intestinal Bacterial Overgrowth) causes bloating and digestive issues. Treatment includes antibiotics and probiotics.'"
            }
        ],
        "stream": False
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            start_time = time.time()
            async with session.post(
                "http://localhost:11434/api/chat",
                json=payload
            ) as response:
                end_time = time.time()
                
                if response.status == 200:
                    data = await response.json()
                    content = data["message"]["content"]
                    return {
                        "status": "success",
                        "response_time": end_time - start_time,
                        "content": content[:200],
                        "model": data.get("model", "unknown")
                    }
                else:
                    error_text = await response.text()
                    return {
                        "status": "error",
                        "status_code": response.status,
                        "message": error_text
                    }
                    
    except Exception as e:
        return {"status": "error", "message": str(e)}

async def test_ollama_embeddings():
    """Test Ollama embeddings API"""
    print("🔍 Testing Ollama Embeddings API...")
    
    payload = {
        "model": "snowflake-arctic-embed2:latest",
        "prompt": "SIBO treatment with antibiotics"
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            start_time = time.time()
            async with session.post(
                "http://localhost:11434/api/embeddings",
                json=payload
            ) as response:
                end_time = time.time()
                
                if response.status == 200:
                    data = await response.json()
                    embeddings = data.get("embedding", [])
                    return {
                        "status": "success",
                        "response_time": end_time - start_time,
                        "embedding_dimensions": len(embeddings),
                        "sample_values": embeddings[:5] if embeddings else []
                    }
                else:
                    error_text = await response.text()
                    return {
                        "status": "error",
                        "status_code": response.status,
                        "message": error_text
                    }
                    
    except Exception as e:
        return {"status": "error", "message": str(e)}

async def test_ollama_medical_model():
    """Test medical-specific model if available"""
    print("🔍 Testing Medical Model (MedLLaMA)...")
    
    payload = {
        "model": "meditron:latest",
        "messages": [
            {
                "role": "user",
                "content": "What are the main symptoms and treatments for SIBO?"
            }
        ],
        "stream": False
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            start_time = time.time()
            async with session.post(
                "http://localhost:11434/api/chat",
                json=payload
            ) as response:
                end_time = time.time()
                
                if response.status == 200:
                    data = await response.json()
                    content = data["message"]["content"]
                    return {
                        "status": "success",
                        "response_time": end_time - start_time,
                        "content": content[:300],
                        "model": data.get("model", "unknown")
                    }
                else:
                    error_text = await response.text()
                    return {
                        "status": "error",
                        "status_code": response.status,
                        "message": error_text
                    }
                    
    except Exception as e:
        return {"status": "error", "message": str(e)}

async def main():
    print("🚀 Testing Ollama Local API...")
    print("=" * 50)
    
    tests = [
        ("Ollama Chat (Mistral)", test_ollama_chat()),
        ("Ollama Embeddings", test_ollama_embeddings()),
        ("Medical Model", test_ollama_medical_model())
    ]
    
    results = {}
    
    for name, test_coro in tests:
        print(f"\n📊 Testing {name}...")
        result = await test_coro
        results[name] = result
        
        if result["status"] == "success":
            print(f"✅ {name}: SUCCESS (Response time: {result['response_time']:.2f}s)")
            if "content" in result:
                print(f"   Sample response: {result['content']}")
            if "embedding_dimensions" in result:
                print(f"   Embedding dimensions: {result['embedding_dimensions']}")
                print(f"   Sample values: {result['sample_values']}")
        else:
            print(f"❌ {name}: ERROR")
            print(f"   Message: {result['message']}")
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 OLLAMA TEST SUMMARY")
    print("=" * 50)
    
    working = [name for name, result in results.items() if result["status"] == "success"]
    failed = [name for name, result in results.items() if result["status"] == "error"]
    
    print(f"✅ Working: {', '.join(working) if working else 'None'}")
    print(f"❌ Failed: {', '.join(failed) if failed else 'None'}")
    
    if working:
        print("\n🔧 RECOMMENDATIONS:")
        print("- Ollama is working locally - can be used as backup for rate-limited APIs")
        if "Ollama Embeddings" in working:
            print("- Local embeddings are available - no external API needed")
        if "Medical Model" in working:
            print("- Medical-specific model available for specialized entity extraction")
    
    # Save results
    with open("ollama_test_results.json", "w") as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n💾 Results saved to ollama_test_results.json")

if __name__ == "__main__":
    asyncio.run(main())

#!/usr/bin/env python3
"""
Setup MedGemma as the primary vision OCR solution for medical document processing.
"""

import os
import sys
import asyncio
import logging
import base64
import json
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_ollama_medgemma():
    """Test Ollama MedGemma availability."""
    try:
        import aiohttp
        
        ollama_url = os.getenv('OLLAMA_API_URL', 'http://host.docker.internal:11434')
        
        logger.info(f"🔄 Testing Ollama MedGemma at {ollama_url}")
        
        async with aiohttp.ClientSession() as session:
            # Test if Ollama is running
            try:
                async with session.get(f"{ollama_url}/api/tags") as response:
                    if response.status == 200:
                        models = await response.json()
                        model_names = [model['name'] for model in models.get('models', [])]
                        
                        # Check for MedGemma models
                        medgemma_models = [name for name in model_names if 'medgemma' in name.lower()]
                        
                        if medgemma_models:
                            logger.info(f"✅ Found MedGemma models: {medgemma_models}")
                            return True, medgemma_models[0]
                        else:
                            logger.warning("⚠️ MedGemma model not found in Ollama")
                            logger.info(f"Available models: {model_names}")
                            return False, None
                    else:
                        logger.error(f"❌ Ollama API error: {response.status}")
                        return False, None
            except Exception as e:
                logger.error(f"❌ Cannot connect to Ollama: {str(e)}")
                return False, None
                
    except ImportError:
        logger.error("❌ aiohttp not available")
        return False, None

async def test_google_api():
    """Test Google API for MedGemma access."""
    try:
        import google.generativeai as genai
        
        api_key = os.getenv('GOOGLE_API_KEY')
        if not api_key:
            logger.error("❌ GOOGLE_API_KEY not found")
            return False
        
        logger.info(f"🔄 Testing Google API with key: {api_key[:10]}...{api_key[-4:]}")
        
        # Configure the API
        genai.configure(api_key=api_key)
        
        # List available models
        models = genai.list_models()
        model_names = [model.name for model in models]
        
        # Look for Gemini models (MedGemma might be available as Gemini)
        gemini_models = [name for name in model_names if 'gemini' in name.lower()]
        
        if gemini_models:
            logger.info(f"✅ Google API working - Available models: {len(model_names)}")
            logger.info(f"   Gemini models: {gemini_models[:3]}...")  # Show first 3
            return True
        else:
            logger.warning("⚠️ No Gemini models found")
            return False
            
    except ImportError:
        logger.error("❌ google-generativeai not installed. Install with: pip install google-generativeai")
        return False
    except Exception as e:
        logger.error(f"❌ Google API test failed: {str(e)}")
        return False

def create_medgemma_ocr_service():
    """Create a MedGemma OCR service implementation."""
    
    service_code = '''"""
MedGemma OCR Service for medical document vision processing.
"""
import os
import asyncio
import logging
import base64
from typing import Optional, Dict, Any
import aiohttp

logger = logging.getLogger(__name__)

class MedGemmaOCRService:
    """OCR service using MedGemma model for medical document processing."""
    
    def __init__(self):
        """Initialize the MedGemma OCR service."""
        self.ollama_url = os.getenv('OLLAMA_API_URL', 'http://host.docker.internal:11434')
        self.model_name = os.getenv('OLLAMA_OCR_MODEL', 'alibayram/medgemma:latest')
        self.google_api_key = os.getenv('GOOGLE_API_KEY')
        
        # Prefer Ollama for local processing, fallback to Google API
        self.use_ollama = True
        
        logger.info(f"MedGemma OCR Service initialized")
        logger.info(f"  Ollama URL: {self.ollama_url}")
        logger.info(f"  Model: {self.model_name}")
        logger.info(f"  Google API: {'Available' if self.google_api_key else 'Not available'}")
    
    async def extract_text_from_image(self, image_data: bytes, filename: str = "document") -> Optional[str]:
        """
        Extract text from image using MedGemma vision capabilities.
        
        Args:
            image_data: Raw image data
            filename: Original filename for context
        
        Returns:
            Extracted text or None if extraction fails
        """
        try:
            # Convert image to base64
            image_base64 = base64.b64encode(image_data).decode('utf-8')
            
            # Try Ollama first
            if self.use_ollama:
                result = await self._extract_with_ollama(image_base64, filename)
                if result:
                    return result
                else:
                    logger.warning("Ollama extraction failed, trying Google API fallback")
            
            # Fallback to Google API
            if self.google_api_key:
                return await self._extract_with_google_api(image_base64, filename)
            else:
                logger.error("No available OCR methods")
                return None
                
        except Exception as e:
            logger.error(f"Error in MedGemma OCR extraction: {str(e)}")
            return None
    
    async def _extract_with_ollama(self, image_base64: str, filename: str) -> Optional[str]:
        """Extract text using Ollama MedGemma."""
        try:
            # Create vision prompt for medical document analysis
            prompt = f"""
You are a medical document analysis AI with advanced OCR capabilities. 
Analyze this medical document image and extract all text content accurately.

Focus on:
- Medical terminology and drug names
- Dosages and measurements  
- Patient information and symptoms
- Research findings and citations
- Maintain original formatting where possible

Document: {filename}

Please extract all visible text from this medical document image:
"""
            
            payload = {
                "model": self.model_name,
                "prompt": prompt,
                "images": [image_base64],
                "stream": False,
                "options": {
                    "temperature": 0.1,  # Low temperature for accurate extraction
                    "top_p": 0.9
                }
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.ollama_url}/api/generate",
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=120)
                ) as response:
                    
                    if response.status == 200:
                        data = await response.json()
                        extracted_text = data.get('response', '').strip()
                        
                        if extracted_text:
                            logger.info(f"✅ Ollama MedGemma extraction successful: {len(extracted_text)} characters")
                            return extracted_text
                        else:
                            logger.warning("Ollama returned empty response")
                            return None
                    else:
                        error_text = await response.text()
                        logger.error(f"Ollama API error {response.status}: {error_text}")
                        return None
                        
        except Exception as e:
            logger.error(f"Ollama extraction error: {str(e)}")
            return None
    
    async def _extract_with_google_api(self, image_base64: str, filename: str) -> Optional[str]:
        """Extract text using Google Gemini API."""
        try:
            import google.generativeai as genai
            
            # Configure API
            genai.configure(api_key=self.google_api_key)
            
            # Use Gemini Pro Vision model
            model = genai.GenerativeModel('gemini-pro-vision')
            
            # Convert base64 to image
            import io
            from PIL import Image
            image_data = base64.b64decode(image_base64)
            image = Image.open(io.BytesIO(image_data))
            
            # Create medical OCR prompt
            prompt = f"""
You are a medical document OCR specialist. Extract all text from this medical document image.

Focus on accurate extraction of:
- Medical terminology and drug names
- Dosages, measurements, and lab values
- Patient symptoms and conditions
- Research citations and references
- Maintain formatting and structure

Document: {filename}

Extract all visible text:
"""
            
            # Generate response
            response = model.generate_content([prompt, image])
            
            if response.text:
                logger.info(f"✅ Google API extraction successful: {len(response.text)} characters")
                return response.text.strip()
            else:
                logger.warning("Google API returned empty response")
                return None
                
        except ImportError:
            logger.error("google-generativeai not installed")
            return None
        except Exception as e:
            logger.error(f"Google API extraction error: {str(e)}")
            return None
    
    async def health_check(self) -> Dict[str, Any]:
        """Check the health of the MedGemma OCR service."""
        try:
            # Test Ollama connection
            ollama_status = False
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(f"{self.ollama_url}/api/tags") as response:
                        ollama_status = response.status == 200
            except:
                pass
            
            # Test Google API
            google_status = bool(self.google_api_key)
            
            return {
                "status": "healthy" if (ollama_status or google_status) else "unhealthy",
                "ollama_available": ollama_status,
                "google_api_available": google_status,
                "model": self.model_name,
                "capabilities": [
                    "Medical document OCR",
                    "Vision-based text extraction", 
                    "Medical terminology recognition",
                    "Multi-format support"
                ]
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    async def test_connection(self) -> bool:
        """Test if the service is available."""
        health = await self.health_check()
        return health.get("status") == "healthy"
'''
    
    # Save the service
    service_path = Path("server/graph_service/services/medgemma_ocr_service.py")
    service_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(service_path, 'w') as f:
        f.write(service_code)
    
    logger.info(f"✅ Created MedGemma OCR service: {service_path}")
    return service_path

async def update_configuration():
    """Update configuration to use MedGemma as primary OCR."""
    
    logger.info("🔄 Updating configuration for MedGemma OCR...")
    
    # Read current .env
    env_path = Path(".env")
    if not env_path.exists():
        logger.error("❌ .env file not found")
        return False
    
    with open(env_path, 'r') as f:
        lines = f.readlines()
    
    # Update OCR configuration
    updated_lines = []
    for line in lines:
        if line.startswith('USE_MISTRAL_OCR='):
            updated_lines.append('USE_MISTRAL_OCR=false\n')
        elif line.startswith('USE_OLLAMA_OCR='):
            updated_lines.append('USE_OLLAMA_OCR=true\n')
        elif line.startswith('USE_LOCAL_OCR='):
            updated_lines.append('USE_LOCAL_OCR=true\n')
        else:
            updated_lines.append(line)
    
    # Add MedGemma-specific configuration if not present
    medgemma_config = [
        "# MedGemma OCR Configuration - Medical vision model\n",
        "USE_MEDGEMMA_OCR=true\n",
        "MEDGEMMA_PRIMARY=true\n",
        "MEDGEMMA_MODEL=alibayram/medgemma:latest\n"
    ]
    
    # Check if MedGemma config already exists
    has_medgemma_config = any('MEDGEMMA' in line for line in updated_lines)
    if not has_medgemma_config:
        updated_lines.extend(medgemma_config)
    
    # Write updated configuration
    with open(env_path, 'w') as f:
        f.writelines(updated_lines)
    
    logger.info("✅ Configuration updated for MedGemma OCR")
    return True

async def main():
    """Setup MedGemma vision OCR."""
    logger.info("🚀 Setting up MedGemma Vision OCR for Medical Documents")
    logger.info("=" * 60)
    
    # Test Ollama MedGemma
    ollama_ok, model_name = await test_ollama_medgemma()
    logger.info("")
    
    # Test Google API
    google_ok = await test_google_api()
    logger.info("")
    
    # Create MedGemma OCR service
    if ollama_ok or google_ok:
        logger.info("🔄 Creating MedGemma OCR service...")
        service_path = create_medgemma_ocr_service()
        logger.info("")
        
        # Update configuration
        config_updated = await update_configuration()
        logger.info("")
        
        # Summary
        logger.info("📋 MedGemma Vision OCR Setup Results:")
        logger.info(f"   Ollama MedGemma: {'✅ Available' if ollama_ok else '❌ Unavailable'}")
        logger.info(f"   Google API: {'✅ Available' if google_ok else '❌ Unavailable'}")
        logger.info(f"   Service Created: {'✅ Success' if service_path.exists() else '❌ Failed'}")
        logger.info(f"   Configuration: {'✅ Updated' if config_updated else '❌ Failed'}")
        
        if ollama_ok or google_ok:
            logger.info("")
            logger.info("🎉 MedGemma Vision OCR Setup Complete!")
            logger.info("✅ Medical document processing ready")
            logger.info("✅ Vision-based text extraction enabled")
            logger.info("✅ Medical terminology recognition active")
            
            if ollama_ok:
                logger.info(f"🔧 Primary: Ollama MedGemma ({model_name})")
            if google_ok:
                logger.info("🔧 Fallback: Google Gemini API")
            
            return True
        else:
            logger.warning("⚠️ No MedGemma services available")
            return False
    else:
        logger.error("❌ Cannot setup MedGemma OCR - no services available")
        logger.info("")
        logger.info("💡 To fix this:")
        logger.info("   1. Start Ollama: ollama serve")
        logger.info("   2. Pull MedGemma: ollama pull alibayram/medgemma:latest")
        logger.info("   3. Verify Google API key is valid")
        return False

if __name__ == "__main__":
    asyncio.run(main())

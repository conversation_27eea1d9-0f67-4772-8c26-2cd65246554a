"""
Document Processing Service

Orchestrates the complete document processing pipeline:
1. OCR text extraction using Mistral OCR
2. Entity extraction using OpenRouter
3. Integration with Graphiti knowledge graph
"""

import asyncio
import logging
import tempfile
import os
from typing import Dict, List, Optional, Any
from pathlib import Path

from .mistral_ocr_service import MistralOCRService
from .local_ocr_service import LocalOCRService
from .ollama_ocr_service import OllamaOCRService
from .openrouter_service import OpenRouterService
from .ollama_entity_service import OllamaEntityService
from .reference_extraction_service import ReferenceExtractionService

logger = logging.getLogger(__name__)


class DocumentProcessingService:
    """
    Service for processing documents through the complete pipeline:
    Mistral OCR → Entity Extraction → Knowledge Graph Integration
    """
    
    def __init__(self):
        """Initialize the document processing service."""
        # Initialize OCR service based on configuration
        use_mistral_ocr = os.getenv('USE_MISTRAL_OCR', 'false').lower() == 'true'
        use_local_ocr = os.getenv('USE_LOCAL_OCR', 'false').lower() == 'true'
        use_ollama_ocr = os.getenv('USE_OLLAMA_OCR', 'true').lower() == 'true'

        if use_ollama_ocr and not use_mistral_ocr and not use_local_ocr:
            # Primary: Ollama MedGemma with local fallback
            self.ocr_service = OllamaOCRService()
            self.local_ocr_service = LocalOCRService()  # Fallback
            self.ollama_ocr_service = None
            self.ocr_provider = "ollama_primary"
            logger.info("Initialized with Ollama MedGemma as primary OCR (with local fallback)")
        elif use_mistral_ocr:
            self.ocr_service = MistralOCRService()
            self.local_ocr_service = LocalOCRService()  # Fallback
            self.ollama_ocr_service = OllamaOCRService()  # Medical-focused fallback
            self.ocr_provider = "mistral_with_fallbacks"
            logger.info("Initialized with Mistral OCR (with local and Ollama fallbacks)")
        elif use_local_ocr:
            self.ocr_service = LocalOCRService()
            self.local_ocr_service = None
            self.ollama_ocr_service = OllamaOCRService()  # Alternative option
            self.ocr_provider = "local_with_ollama"
            logger.info("Initialized with Local OCR and Ollama MedGemma")
        else:
            # Default to Ollama OCR for medical documents
            self.ocr_service = OllamaOCRService()
            self.local_ocr_service = LocalOCRService()
            self.ollama_ocr_service = None
            self.ocr_provider = "ollama_with_local"
            logger.info("Initialized with Ollama MedGemma OCR (with local fallback)")

        # Initialize entity extraction service - Use Ollama MedGemma for rate-limit-free processing
        use_ollama_entities = os.getenv('USE_OLLAMA_ENTITIES', 'true').lower() == 'true'
        if use_ollama_entities:
            self.entity_service = OllamaEntityService(num_workers=4)
            logger.info("Using Ollama MedGemma for entity extraction (4 workers)")
        else:
            self.entity_service = OpenRouterService()
            logger.info("Using OpenRouter for entity extraction")

        # Initialize reference extraction service
        self.reference_service = ReferenceExtractionService()
        
    async def process_document(
        self, 
        file_content: bytes, 
        filename: str, 
        upload_mode: str = "messages"
    ) -> Dict[str, Any]:
        """
        Process a document through the complete pipeline.
        
        Args:
            file_content: Raw file content as bytes
            filename: Original filename
            upload_mode: How to process the document ("messages" or "entities")
        
        Returns:
            Processing results including extracted text, entities, and status
        """
        try:
            logger.info(f"Starting document processing for {filename} in {upload_mode} mode")
            
            # Step 1: Determine file type and processing method
            file_extension = Path(filename).suffix.lower()
            
            if file_extension == '.pdf':
                # Use OCR for PDF files
                extracted_text = await self._process_pdf_with_ocr(file_content, filename)
            elif file_extension in ['.txt', '.md']:
                # Direct text extraction for text files
                extracted_text = file_content.decode('utf-8')
            elif file_extension in ['.doc', '.docx']:
                # For now, treat as text (future: implement proper Word processing)
                try:
                    extracted_text = file_content.decode('utf-8')
                except UnicodeDecodeError:
                    logger.warning(f"Could not decode {filename} as text, using OCR")
                    extracted_text = await self._process_pdf_with_ocr(file_content, filename)
            else:
                # Try OCR for other file types
                extracted_text = await self._process_pdf_with_ocr(file_content, filename)
            
            if not extracted_text:
                return {
                    "success": False,
                    "error": "Failed to extract text from document",
                    "filename": filename
                }
            
            logger.info(f"Extracted {len(extracted_text)} characters from {filename}")
            
            # Step 2: Always extract entities for comprehensive processing
            entities = []
            logger.info("Extracting entities from document text")
            try:
                entities = await self.entity_service.extract_entities(extracted_text)
                logger.info(f"Extracted {len(entities)} entities")
            except Exception as entity_error:
                logger.error(f"Entity extraction failed: {str(entity_error)}")
                entities = []

            # Step 3: Extract references for academic documents
            references = []
            csv_path = ""
            logger.info("Extracting references from document text")
            try:
                references = await self.reference_service.extract_references(extracted_text, filename)
                logger.info(f"Extracted {len(references)} references")

                # Export references to CSV
                if references:
                    csv_path = await self.reference_service.export_references_to_csv(references, filename)
                    logger.info(f"Exported references to: {csv_path}")
            except Exception as ref_error:
                logger.error(f"Reference extraction failed: {str(ref_error)}")
                references = []
            
            # Step 4: Prepare comprehensive results
            result = {
                "success": True,
                "filename": filename,
                "extracted_text": extracted_text,
                "text_length": len(extracted_text),
                "entities": entities,
                "references": references,
                "csv_export_path": csv_path,
                "upload_mode": upload_mode,
                "processing_steps": {
                    "ocr_completed": True,
                    "entity_extraction_attempted": True,
                    "entity_extraction_completed": len(entities) > 0,
                    "reference_extraction_attempted": True,
                    "reference_extraction_completed": len(references) > 0,
                    "csv_export_completed": bool(csv_path),
                    "entities_count": len(entities),
                    "references_count": len(references)
                }
            }
            
            logger.info(f"Successfully processed {filename}")
            return result
            
        except Exception as e:
            logger.error(f"Error processing document {filename}: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "filename": filename
            }
    
    async def _process_pdf_with_ocr(self, file_content: bytes, filename: str) -> Optional[str]:
        """
        Process a PDF or image file using the configured OCR service.

        Args:
            file_content: Raw file content
            filename: Original filename

        Returns:
            Extracted text or None if extraction fails
        """
        try:
            logger.info(f"Processing {filename} with {self.ocr_provider} OCR")

            if self.ocr_provider == "ollama_primary":
                # Use Ollama MedGemma as primary, fallback to local
                try:
                    logger.info(f"Using Ollama MedGemma for primary OCR processing of {filename}")
                    result = await self.ocr_service.extract_text(file_content, filename)
                    if result:
                        logger.info(f"Ollama MedGemma extraction successful: {len(result)} characters")
                        return result
                    else:
                        logger.warning(f"Ollama MedGemma returned no text for {filename}")
                        raise Exception("Ollama MedGemma returned no text")
                except Exception as ollama_error:
                    logger.warning(f"Ollama MedGemma failed for {filename}: {str(ollama_error)}")
                    logger.info(f"Falling back to local OCR for {filename}")

                    if self.local_ocr_service:
                        return await self.local_ocr_service.extract_text(file_content, filename)
                    else:
                        logger.error("No local OCR service available for fallback")
                        return None

            elif self.ocr_provider == "local":
                # Use local OCR directly
                return await self.ocr_service.extract_text(file_content, filename)

            elif self.ocr_provider == "mistral_with_fallbacks":
                # Try Mistral OCR first, fallback to local if it fails
                try:
                    # Create a temporary file for Mistral OCR processing
                    with tempfile.NamedTemporaryFile(delete=False, suffix=Path(filename).suffix) as temp_file:
                        temp_file.write(file_content)
                        temp_file_path = temp_file.name

                    try:
                        # Convert file to base64 for Mistral OCR
                        import base64
                        with open(temp_file_path, 'rb') as f:
                            file_base64 = base64.b64encode(f.read()).decode('utf-8')

                        # Determine document type and MIME type based on file extension
                        file_extension = Path(filename).suffix.lower()
                        if file_extension in ['.jpg', '.jpeg']:
                            doc_type = "image_url"
                            document_url = f"data:image/jpeg;base64,{file_base64}"
                        elif file_extension in ['.png']:
                            doc_type = "image_url"
                            document_url = f"data:image/png;base64,{file_base64}"
                        elif file_extension in ['.gif']:
                            doc_type = "image_url"
                            document_url = f"data:image/gif;base64,{file_base64}"
                        elif file_extension in ['.bmp']:
                            doc_type = "image_url"
                            document_url = f"data:image/bmp;base64,{file_base64}"
                        elif file_extension in ['.pdf']:
                            doc_type = "document_url"
                            document_url = f"data:application/pdf;base64,{file_base64}"
                        elif file_extension in ['.docx']:
                            doc_type = "document_url"
                            document_url = f"data:application/vnd.openxmlformats-officedocument.wordprocessingml.document;base64,{file_base64}"
                        elif file_extension in ['.pptx']:
                            doc_type = "document_url"
                            document_url = f"data:application/vnd.openxmlformats-officedocument.presentationml.presentation;base64,{file_base64}"
                        elif file_extension in ['.txt']:
                            doc_type = "document_url"
                            document_url = f"data:text/plain;base64,{file_base64}"
                        else:
                            # Default to PDF for unknown document types
                            doc_type = "document_url"
                            document_url = f"data:application/pdf;base64,{file_base64}"

                        # Extract text using Mistral OCR
                        logger.info(f"Attempting Mistral OCR extraction for {filename}")
                        extracted_text = await self.ocr_service.extract_text(document_url, doc_type)

                        if extracted_text:
                            logger.info(f"Mistral OCR extraction successful for {filename}: {len(extracted_text)} characters")
                            return extracted_text
                        else:
                            logger.warning(f"Mistral OCR returned no text for {filename}, trying local fallback")
                            raise Exception("Mistral OCR returned no text")

                    finally:
                        # Clean up temporary file
                        if os.path.exists(temp_file_path):
                            os.unlink(temp_file_path)

                except Exception as mistral_error:
                    logger.warning(f"Mistral OCR failed for {filename}: {str(mistral_error)}")

                    # Check if it's a rate limit error
                    if "rate limit" in str(mistral_error).lower() or "429" in str(mistral_error):
                        logger.info(f"Rate limit detected, trying Ollama MedGemma for {filename}")

                        # Try Ollama OCR first for medical documents
                        if self.ollama_ocr_service:
                            try:
                                ollama_result = await self.ollama_ocr_service.extract_text(file_content, filename)
                                if ollama_result:
                                    logger.info(f"Ollama MedGemma extraction successful for {filename}")
                                    return ollama_result
                            except Exception as ollama_error:
                                logger.warning(f"Ollama OCR also failed: {ollama_error}")

                    # Final fallback to local OCR
                    logger.info(f"Falling back to local OCR for {filename}")
                    if self.local_ocr_service:
                        return await self.local_ocr_service.extract_text(file_content, filename)
                    else:
                        logger.error("No fallback OCR services available")
                        return None

            elif self.ocr_provider == "local_with_ollama":
                # Try local OCR first, fallback to Ollama
                try:
                    return await self.ocr_service.extract_text(file_content, filename)
                except Exception as local_error:
                    logger.warning(f"Local OCR failed for {filename}: {str(local_error)}")
                    logger.info(f"Falling back to Ollama MedGemma for {filename}")

                    if self.ollama_ocr_service:
                        return await self.ollama_ocr_service.extract_text(file_content, filename)
                    else:
                        logger.error("No Ollama OCR service available for fallback")
                        return None

            elif self.ocr_provider == "ollama_with_local":
                # Try Ollama OCR first, fallback to local
                try:
                    return await self.ocr_service.extract_text(file_content, filename)
                except Exception as ollama_error:
                    logger.warning(f"Ollama OCR failed for {filename}: {str(ollama_error)}")
                    logger.info(f"Falling back to local OCR for {filename}")

                    if self.local_ocr_service:
                        return await self.local_ocr_service.extract_text(file_content, filename)
                    else:
                        logger.error("No local OCR service available for fallback")
                        return None

            else:
                logger.error(f"Unknown OCR provider: {self.ocr_provider}")
                return None

        except Exception as e:
            logger.error(f"Error in OCR processing for {filename}: {str(e)}")
            return None
    
    async def get_service_status(self) -> Dict[str, Any]:
        """
        Get the status of all document processing services.

        Returns:
            Status information for OCR and entity extraction services
        """
        try:
            logger.info("Getting service status - starting...")
            # Test OCR service based on provider
            if self.ocr_provider == "ollama_primary":
                # Ollama MedGemma as primary
                ollama_status_info = await self.ocr_service.health_check()
                ocr_status = ollama_status_info.get("status") == "healthy"
                ocr_provider_name = "Ollama MedGemma (Primary) + Local Fallback"
                ocr_capabilities = [
                    "Medical document processing",
                    "SIBO-focused analysis",
                    "Text enhancement and structuring",
                    "Medical entity recognition",
                    "Local fallback available",
                    "No rate limits",
                    "Always available"
                ]
            elif self.ocr_provider == "local":
                ocr_status_info = await self.ocr_service.health_check()
                ocr_status = ocr_status_info.get("status") == "healthy"
                ocr_provider_name = "Local OCR (PyMuPDF + Tesseract)"
                ocr_capabilities = [
                    "PDF text extraction",
                    "PDF image OCR (scanned documents)",
                    "Image OCR",
                    "No rate limits",
                    "Always available"
                ]
            elif self.ocr_provider == "mistral_with_fallbacks":
                # Mistral OCR with multiple fallbacks
                try:
                    ocr_status = await self.ocr_service.test_connection()
                    ocr_provider_name = "Mistral OCR (with Ollama + Local fallbacks)"
                    ocr_capabilities = [
                        "Advanced PDF analysis",
                        "Multi-language support",
                        "High-quality OCR",
                        "Medical-focused fallback (MedGemma)",
                        "Local fallback available"
                    ]
                except Exception:
                    ocr_status = True  # Fallbacks are always available
                    ocr_provider_name = "Ollama MedGemma + Local (Mistral unavailable)"
                    ocr_capabilities = [
                        "Medical document processing",
                        "SIBO-focused analysis",
                        "Local text extraction",
                        "Fallback mode active"
                    ]

            elif self.ocr_provider == "local_with_ollama":
                # Local OCR with Ollama fallback
                local_status_info = await self.ocr_service.health_check()
                ollama_status_info = await self.ollama_ocr_service.health_check()

                ocr_status = (local_status_info.get("status") == "healthy" or
                             ollama_status_info.get("status") == "healthy")
                ocr_provider_name = "Local OCR + Ollama MedGemma"
                ocr_capabilities = [
                    "PDF text extraction",
                    "Medical text enhancement",
                    "SIBO-focused analysis",
                    "No rate limits",
                    "Always available"
                ]

            elif self.ocr_provider == "ollama_with_local":
                # Ollama OCR with local fallback
                ollama_status_info = await self.ocr_service.health_check()
                ocr_status = ollama_status_info.get("status") == "healthy"
                ocr_provider_name = "Ollama MedGemma (with local fallback)"
                ocr_capabilities = [
                    "Medical document processing",
                    "SIBO-focused analysis",
                    "Text enhancement and structuring",
                    "Local fallback available",
                    "No rate limits"
                ]

            else:
                # Unknown provider
                ocr_status = False
                ocr_provider_name = f"Unknown provider: {self.ocr_provider}"
                ocr_capabilities = ["Configuration error"]

            # Test entity extraction service
            logger.info("Testing entity extraction service...")
            entity_status = await self.entity_service.test_connection()
            logger.info(f"Entity extraction status: {entity_status}")

            # Test reference extraction service
            logger.info("Testing reference extraction service...")
            reference_status = bool(self.reference_service.openrouter_api_key)
            logger.info(f"Reference extraction status: {reference_status}")

            logger.info("Building service status response...")
            return {
                "ocr_service": {
                    "available": ocr_status,
                    "provider": ocr_provider_name,
                    "configuration": self.ocr_provider,
                    "capabilities": ocr_capabilities
                },
                "entity_extraction": {
                    "available": entity_status,
                    "provider": "OpenRouter",
                    "model": "meta-llama/llama-4-maverick:free",
                    "capabilities": [
                        "Medical entity extraction",
                        "Scientific concept identification",
                        "Relationship detection",
                        "Context-aware analysis"
                    ]
                },
                "reference_extraction": {
                    "available": reference_status,
                    "provider": "OpenRouter",
                    "model": "meta-llama/llama-4-maverick:free",
                    "capabilities": [
                        "Academic reference extraction",
                        "Citation parsing",
                        "DOI and PMID identification",
                        "CSV export functionality"
                    ]
                },
                "pipeline_status": "ready" if (ocr_status and entity_status and reference_status) else "partial",
                "supported_formats": [
                    "PDF (.pdf)",
                    "Text files (.txt, .md)",
                    "Word documents (.doc, .docx)",
                    "Images (.jpg, .png, .gif, .bmp)"
                ]
            }

        except Exception as e:
            logger.error(f"Error getting service status: {str(e)}")
            return {
                "error": str(e),
                "pipeline_status": "error"
            }
    
    async def extract_entities_from_text(self, text: str) -> List[Dict[str, Any]]:
        """
        Extract entities from plain text.
        
        Args:
            text: Input text
        
        Returns:
            List of extracted entities
        """
        try:
            return await self.entity_service.extract_entities(text)
        except Exception as e:
            logger.error(f"Error extracting entities from text: {str(e)}")
            return []

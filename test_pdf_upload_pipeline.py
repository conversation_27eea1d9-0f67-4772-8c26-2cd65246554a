#!/usr/bin/env python3
"""
Test the fixed PDF upload pipeline with MedGemma OCR integration.
"""

import os
import sys
import asyncio
import logging
import tempfile
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_test_pdf():
    """Create a test PDF with medical content."""
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter
        import tempfile
        
        # Create temporary PDF
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
        temp_path = temp_file.name
        temp_file.close()
        
        # Create PDF with medical content
        c = canvas.Canvas(temp_path, pagesize=letter)
        width, height = letter
        
        # Add medical document content
        medical_content = [
            "MEDICAL REPORT",
            "",
            "Patient: <PERSON>",
            "Date: 2024-06-13",
            "MRN: 123456",
            "",
            "Chief <PERSON><PERSON><PERSON><PERSON>:",
            "<PERSON><PERSON> presents with digestive issues and bloating after meals.",
            "",
            "Diagnosis:",
            "Small Intestinal Bacterial Overgrowth (SIBO)",
            "",
            "Treatment Plan:",
            "1. Rifaximin 550mg twice daily for 14 days",
            "2. Probiotics: Lactobacillus acidophilus 10 billion CFU daily",
            "3. Dietary modifications: Low-FODMAP diet",
            "4. Supplements:",
            "   - Vitamin B12 1000mcg daily",
            "   - Vitamin D3 2000 IU daily",
            "   - Magnesium glycinate 400mg daily",
            "",
            "Follow-up:",
            "Breath test in 4 weeks to assess treatment response.",
            "",
            "Dr. Sarah Johnson, MD",
            "Gastroenterology Department",
            "Medical Center"
        ]
        
        y_position = height - 50
        for line in medical_content:
            c.drawString(50, y_position, line)
            y_position -= 25
            if y_position < 50:  # Start new page if needed
                c.showPage()
                y_position = height - 50
        
        c.save()
        logger.info(f"Created test PDF: {temp_path}")
        return temp_path
        
    except ImportError:
        logger.error("reportlab not available - cannot create test PDF")
        return None
    except Exception as e:
        logger.error(f"Error creating test PDF: {str(e)}")
        return None

async def test_upload_endpoint_extraction():
    """Test the upload endpoint text extraction function."""
    try:
        from server.graph_service.routers.document_upload import extract_text_only
        
        logger.info("🔄 Testing upload endpoint text extraction...")
        
        # Create test PDF
        pdf_path = create_test_pdf()
        if not pdf_path:
            logger.error("❌ Could not create test PDF")
            return False
        
        try:
            # Read PDF content
            with open(pdf_path, 'rb') as f:
                pdf_content = f.read()
            
            # Test extraction function
            extracted_text = await extract_text_only(pdf_content, "test_medical_report.pdf")
            
            if extracted_text and len(extracted_text.strip()) > 50:
                logger.info("✅ Upload endpoint extraction successful!")
                logger.info(f"📝 Extracted text ({len(extracted_text)} chars):")
                logger.info(f"'{extracted_text[:200]}...'")
                
                # Check for medical terms
                medical_terms = ['SIBO', 'Rifaximin', 'Patient', 'Medical']
                found_terms = [term for term in medical_terms if term.lower() in extracted_text.lower()]
                
                logger.info(f"🔍 Found medical terms: {found_terms}")
                return True
            else:
                logger.warning(f"⚠️ Upload endpoint returned minimal text: '{extracted_text[:100]}'")
                return False
                
        finally:
            # Clean up
            if os.path.exists(pdf_path):
                os.unlink(pdf_path)
                
    except Exception as e:
        logger.error(f"❌ Upload endpoint test failed: {str(e)}")
        return False

async def test_fallback_pdf_extraction():
    """Test fallback PDF extraction methods."""
    try:
        from server.graph_service.routers.document_upload import _fallback_pdf_extraction
        
        logger.info("🔄 Testing fallback PDF extraction...")
        
        # Create test PDF
        pdf_path = create_test_pdf()
        if not pdf_path:
            logger.error("❌ Could not create test PDF")
            return False
        
        try:
            # Read PDF content
            with open(pdf_path, 'rb') as f:
                pdf_content = f.read()
            
            # Test fallback extraction
            extracted_text = await _fallback_pdf_extraction(pdf_content, "test_medical_report.pdf")
            
            if extracted_text and len(extracted_text.strip()) > 50:
                logger.info("✅ Fallback PDF extraction successful!")
                logger.info(f"📝 Extracted text ({len(extracted_text)} chars):")
                logger.info(f"'{extracted_text[:200]}...'")
                return True
            else:
                logger.warning(f"⚠️ Fallback extraction returned minimal text: '{extracted_text[:100]}'")
                return False
                
        finally:
            # Clean up
            if os.path.exists(pdf_path):
                os.unlink(pdf_path)
                
    except Exception as e:
        logger.error(f"❌ Fallback PDF extraction test failed: {str(e)}")
        return False

async def main():
    """Run PDF upload pipeline tests."""
    logger.info("🚀 Testing PDF Upload Pipeline Fix")
    logger.info("MedGemma OCR + Fallback PDF Processing")
    logger.info("=" * 50)
    
    # Test upload endpoint extraction
    upload_ok = await test_upload_endpoint_extraction()
    logger.info("")
    
    # Test fallback PDF extraction
    fallback_ok = await test_fallback_pdf_extraction()
    logger.info("")
    
    # Summary
    logger.info("📋 PDF Upload Pipeline Test Results:")
    logger.info(f"   Upload Endpoint: {'✅ Working' if upload_ok else '❌ Failed'}")
    logger.info(f"   Fallback PDF: {'✅ Working' if fallback_ok else '❌ Failed'}")
    
    if upload_ok or fallback_ok:
        logger.info("")
        logger.info("🎉 PDF UPLOAD PIPELINE FIX SUCCESSFUL!")
        logger.info("✅ PDF text extraction is now working")
        logger.info("✅ Medical document processing enabled")
        logger.info("✅ Frontend uploads will now process correctly")
        
        logger.info("")
        logger.info("🚀 Ready to process uploaded PDFs from frontend!")
        return True
    else:
        logger.error("❌ PDF upload pipeline fix failed")
        return False

if __name__ == "__main__":
    asyncio.run(main())

#!/usr/bin/env python3
"""
Test the fixed document upload pipeline with OpenRouter + Gemini configuration.
"""

import asyncio
import logging
import os
from datetime import datetime, timezone
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_document_processing_pipeline():
    """Test the complete document processing pipeline with the fixed configuration."""
    try:
        from graphiti_core import Graphiti
        from graphiti_core.nodes import EpisodeType
        from graphiti_core.llm_client.openai_generic_client import OpenAIGenericClient
        from graphiti_core.llm_client.config import LLMConfig
        from graphiti_core.embedder.gemini import GeminiEmbedder, GeminiEmbedderConfig
        
        # Configuration
        neo4j_uri = 'bolt://localhost:7891'
        neo4j_user = 'neo4j'
        neo4j_password = 'Triathlon16'
        
        openai_api_key = os.getenv('OPENAI_API_KEY')
        openai_base_url = os.getenv('OPENAI_BASE_URL', 'https://openrouter.ai/api/v1')
        model_name = 'meta-llama/llama-3.1-8b-instruct:free'  # Fixed model
        google_api_key = os.getenv('GOOGLE_API_KEY')
        
        logger.info(f"🔧 TESTING FIXED DOCUMENT PROCESSING PIPELINE")
        logger.info(f"✅ LLM: OpenRouter with {model_name}")
        logger.info(f"✅ Embeddings: Google Gemini")
        logger.info(f"✅ Client: Generic (non-structured output)")
        
        # Create LLM config for generic client
        llm_config = LLMConfig(
            api_key=openai_api_key,
            base_url=openai_base_url,
            model=model_name,
            temperature=0.1,
            max_tokens=2000
        )
        
        # Create generic OpenAI client (doesn't use structured output)
        llm_client = OpenAIGenericClient(config=llm_config)
        
        # Create Gemini embedder
        embedder_config = GeminiEmbedderConfig(
            api_key=google_api_key,
            embedding_model="embedding-001"
        )
        embedder = GeminiEmbedder(config=embedder_config)
        
        # Create Graphiti instance with mixed clients
        graphiti = Graphiti(
            uri=neo4j_uri,
            user=neo4j_user,
            password=neo4j_password,
            llm_client=llm_client,
            embedder=embedder
        )
        
        logger.info("🚀 Processing sample medical document...")
        
        # Simulate a document upload with medical content
        document_text = """Vitamin D deficiency is common in SIBO patients. Probiotics like Lactobacillus can help restore gut health."""
        
        logger.info(f"📄 Document content: {len(document_text)} characters")
        
        # Process as episode (this is what the UI upload does)
        result = await graphiti.add_episode(
            name="Medical Document: Vitamin D and SIBO",
            episode_body=document_text,
            source=EpisodeType.message,
            source_description="Sample medical document for testing entity extraction",
            reference_time=datetime.now(timezone.utc),
            group_id="test_document_upload"
        )
        
        logger.info(f"✅ DOCUMENT PROCESSED SUCCESSFULLY!")
        logger.info(f"📊 Results:")
        logger.info(f"  📝 Episode UUID: {result.episode.uuid}")
        logger.info(f"  🏷️  Entities extracted: {len(result.nodes) if result.nodes else 0}")
        logger.info(f"  🔗 Relationships created: {len(result.edges) if result.edges else 0}")
        
        if result.nodes:
            logger.info(f"🏷️  EXTRACTED ENTITIES:")
            for i, node in enumerate(result.nodes):
                entity_type = getattr(node, 'entity_type', 'Unknown')
                logger.info(f"    {i+1}. {node.name} (Type: {entity_type})")
        
        if result.edges:
            logger.info(f"🔗 EXTRACTED RELATIONSHIPS:")
            for i, edge in enumerate(result.edges[:3]):  # Show first 3
                logger.info(f"    {i+1}. {edge.name}")
        
        # Clean up
        await graphiti.delete_group("test_document_upload")
        await graphiti.close()
        
        logger.info(f"🎉 SUCCESS: Document processing pipeline is working!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Document processing test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_document_processing_pipeline())

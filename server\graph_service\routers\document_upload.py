"""
Document upload routes for handling file uploads with comprehensive document processing.
"""
import logging
import uuid
from datetime import datetime

from fastapi import APIRouter, UploadFile, File, Form, HTTPException, BackgroundTasks
from fastapi.responses import JSONResponse
from graphiti_core.nodes import EpisodeType

# Using pure Graphiti architecture with MedGemma OCR integration
from graph_service.services.status_tracker import get_status_tracker

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/documents", tags=["documents"])

# Pure Graphiti architecture - no custom document processing needed

@router.options("/upload")
async def upload_options():
    """Handle CORS preflight requests for upload endpoint"""
    return JSONResponse(
        status_code=200,
        content={"message": "OK"},
        headers={
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "POST, OPTIONS",
            "Access-Control-Allow-Headers": "*",
        }
    )

@router.post("/upload")
async def upload_document(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    group_id: str = Form(...),
    upload_type: str = Form(default="messages")
):
    """
    Upload and process a document with comprehensive OCR and entity extraction.

    Args:
        file: The uploaded file
        group_id: Group ID for organizing the document
        upload_type: Either 'messages' (creates episodes with automatic entity extraction) or 'entities' (creates entity nodes manually)

    Returns:
        JSON response with processing status
    """
    try:
        # Validate file
        if not file.filename:
            raise HTTPException(status_code=400, detail="No file provided")
        
        # Check file size (50MB limit for Mistral OCR)
        content = await file.read()
        file_size = len(content)
        
        if file_size > 50 * 1024 * 1024:  # 50MB
            raise HTTPException(status_code=400, detail="File size exceeds 50MB limit")
        
        # Use episode-focused document processing
        logger.info(f"Processing {file.filename} with episode-focused Graphiti ingestion")

        # Initialize status tracking
        status_tracker = get_status_tracker()
        await status_tracker.start_processing(file.filename, group_id)

        # Start background processing with episode focus
        background_tasks.add_task(
            process_document_as_episode,
            content,
            file.filename,
            group_id,
            upload_type
        )

        return JSONResponse(
            status_code=202,
            content={
                "message": "Document upload started",
                "filename": file.filename,
                "status": "processing",
                "processing_method": "graphiti_episode_pipeline"
            }
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading document: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")


async def process_document_as_episode(
    content: bytes,
    filename: str,
    group_id: str,
    upload_type: str
):
    """
    Process document following Graphiti architecture - episodes as primary units.
    This follows the official Graphiti pattern where add_episode() handles everything.
    """
    status_tracker = get_status_tracker()

    try:
        logger.info(f"Starting Graphiti episode processing for {filename}")
        logger.info(f"Content size: {len(content)} bytes")

        # Update status: Starting OCR
        await status_tracker.update_step(filename, "ocr_started", True, group_id)

        # Step 1: Extract text only (no manual entity extraction)
        logger.info(f"Extracting text from {filename} (file size: {len(content)} bytes)")
        await status_tracker.update_step(filename, "ocr_processing", True, group_id,
                                       message="Processing document with Mistral OCR...")
        extracted_text = await extract_text_only(content, filename)

        if not extracted_text:
            error_msg = f"No text extracted from {filename}"
            logger.error(error_msg)
            await status_tracker.set_error(filename, error_msg, group_id)
            return

        logger.info(f"Extracted {len(extracted_text)} characters from {filename}")
        await status_tracker.update_step(filename, "ocr_completed", True, group_id,
                                       text_length=len(extracted_text))

        # Step 2: Create Graphiti client with Ollama MedGemma
        import os
        from graph_service.config import get_settings
        from graphiti_core import Graphiti
        from graphiti_core.llm_client.config import LLMConfig

        settings = get_settings()

        # Check if we should use Ollama for entity extraction
        use_ollama_entities = os.getenv('USE_OLLAMA_ENTITIES', 'true').lower() == 'true'

        if use_ollama_entities:
            # Use Ollama MedGemma for entity extraction (simplified - no workers)
            from graph_service.services.ollama_llm_client import OllamaLLMClient
            from graph_service.services.ollama_embedder import OllamaEmbedder

            logger.info("🔧 SIMPLIFIED: Using direct Ollama with reliable model")

            # Use a smaller, more reliable model that actually works
            ollama_config = LLMConfig(
                model='llama3.1:8b',  # Smaller, more reliable model
                temperature=0.1,
                max_tokens=1500  # Reduced for faster processing
            )

            ollama_llm_client = OllamaLLMClient(config=ollama_config)

            # Create Ollama embedder using snowflake model from .env
            ollama_embedder = OllamaEmbedder(
                base_url="http://host.docker.internal:11434",
                model="snowflake-arctic-embed2:latest"
            )

            graphiti = Graphiti(
                uri=settings.neo4j_uri,
                user=settings.neo4j_user,
                password=settings.neo4j_password,
                llm_client=ollama_llm_client,
                embedder=ollama_embedder
            )

            logger.info("✅ Using Ollama llama3.1:8b for Graphiti entity extraction (simplified)")
            logger.info("✅ Using Ollama Snowflake embeddings for Graphiti")
        else:
            # Fallback to OpenRouter configuration
            from graph_service.zep_graphiti import ZepGraphiti

            graphiti = ZepGraphiti(
                uri=settings.neo4j_uri,
                user=settings.neo4j_user,
                password=settings.neo4j_password,
            )

            # Configure OpenRouter LLM client
            if settings.openai_base_url is not None:
                graphiti.llm_client.config.base_url = settings.openai_base_url
            if settings.openai_api_key is not None:
                graphiti.llm_client.config.api_key = settings.openai_api_key
            if settings.model_name is not None:
                graphiti.llm_client.model = settings.model_name

            graphiti.llm_client.config.max_tokens = 4000
            graphiti.llm_client.config.temperature = 0.1

            logger.info("Using OpenRouter for Graphiti entity extraction")

        logger.info(f"Graphiti LLM Client configured with model: {graphiti.llm_client.model}")

        # Initialize database
        await graphiti.build_indices_and_constraints()

        try:
            # Step 3: Worker-scaled entity pre-processing + Graphiti episode creation
            logger.info(f"Adding episode to Graphiti for {filename}")

            if upload_type == "messages":
                # Simplified Graphiti workflow - let Graphiti handle entity extraction directly
                logger.info("🔧 SIMPLIFIED: Starting direct Graphiti entity extraction")

                result = await graphiti.add_episode(
                    name=f"Document: {filename}",
                    episode_body=extracted_text,
                    source=EpisodeType.message,  # Use message type as per Graphiti examples
                    source_description=f"Document ingestion from {filename}",
                    reference_time=datetime.now(),
                    group_id=group_id
                )

                # Log the results from Graphiti's automatic processing
                entities_count = len(result.nodes) if result.nodes else 0
                relationships_count = len(result.edges) if result.edges else 0

                logger.info(f"✅ Graphiti processed {filename}:")
                logger.info(f"  📝 Episode UUID: {result.episode.uuid}")
                logger.info(f"  🏷️  Entities extracted: {entities_count}")
                logger.info(f"  🔗 Relationships extracted: {relationships_count}")

                # Show extracted entities
                if result.nodes:
                    logger.info(f"🏷️  EXTRACTED ENTITIES:")
                    for i, node in enumerate(result.nodes[:10]):  # Show first 10
                        logger.info(f"    {i+1}. {node.name}")
                        if hasattr(node, 'entity_type'):
                            logger.info(f"       Type: {node.entity_type}")
                    if len(result.nodes) > 10:
                        logger.info(f"    ... and {len(result.nodes) - 10} more entities")

                # Show extracted relationships
                if result.edges:
                    logger.info(f"🔗 EXTRACTED RELATIONSHIPS:")
                    for i, edge in enumerate(result.edges[:5]):  # Show first 5
                        logger.info(f"    {i+1}. {edge.name}")
                    if len(result.edges) > 5:
                        logger.info(f"    ... and {len(result.edges) - 5} more relationships")

                # Update status with Graphiti's results
                await status_tracker.update_step(filename, "entity_extraction_completed", True, group_id,
                                               entities_count=entities_count)
                await status_tracker.update_step(filename, "relationship_extraction_completed", True, group_id,
                                               relationships_count=relationships_count)

                # Mark as completed
                await status_tracker.complete_processing(
                    filename, group_id, success=True,
                    text_length=len(extracted_text),
                    entities_count=entities_count,
                    references_count=0,  # References handled separately
                    episodes_count=1,
                    csv_export_path=""
                )

            else:
                # Legacy entity mode (not recommended)
                logger.warning(f"Using legacy entity mode for {filename}")
                await graphiti.save_entity_node(
                    name=filename,
                    uuid=str(uuid.uuid4()),
                    group_id=group_id,
                    summary=extracted_text[:500] + ("..." if len(extracted_text) > 500 else ""),
                )

                await status_tracker.complete_processing(
                    filename, group_id, success=True,
                    text_length=len(extracted_text),
                    entities_count=1,
                    references_count=0,
                    episodes_count=0,
                    csv_export_path=""
                )

            logger.info(f"Successfully processed {filename} using Graphiti architecture")

        finally:
            await graphiti.close()

    except Exception as e:
        logger.error(f"Error in Graphiti episode processing for {filename}: {str(e)}")
        await status_tracker.set_error(filename, str(e), group_id)


async def extract_text_only(content: bytes, filename: str) -> str:
    """
    Extract text using MedGemma Vision OCR for medical documents.
    Graphiti will handle all entity extraction automatically.
    """
    try:
        # For text files, just decode directly
        if filename.lower().endswith('.txt'):
            try:
                return content.decode('utf-8')
            except UnicodeDecodeError:
                return content.decode('utf-8', errors='ignore')

        # For PDFs, use Mistral OCR as primary vision model
        if filename.lower().endswith('.pdf'):
            logger.info(f"Processing PDF {filename} with Mistral Vision OCR")

            # Try Mistral OCR first for comprehensive vision-based extraction
            try:
                from graph_service.services.mistral_ocr_service import MistralOCRService

                # Initialize Mistral OCR service
                mistral_ocr = MistralOCRService()

                if not mistral_ocr.is_available():
                    logger.warning("Mistral OCR not available, falling back to PyMuPDF")
                    return await _fallback_pdf_extraction(content, filename)

                # Convert PDF content to base64 data URL
                import base64
                pdf_base64 = base64.b64encode(content).decode('utf-8')
                document_url = f"data:application/pdf;base64,{pdf_base64}"

                # Extract text using Mistral OCR with extended timeout for large PDFs
                import asyncio
                try:
                    # Use asyncio.wait_for with 10 minute timeout for large PDFs
                    extracted_text = await asyncio.wait_for(
                        mistral_ocr.extract_text(document_url, "document_url"),
                        timeout=600.0  # 10 minutes for large PDFs
                    )
                except asyncio.TimeoutError:
                    logger.warning(f"Mistral OCR timed out after 10 minutes for {filename}, trying PyMuPDF fallback")
                    return await _fallback_pdf_extraction(content, filename)

                if extracted_text and len(extracted_text.strip()) > 20:
                    logger.info(f"Mistral OCR extraction successful for {filename}: {len(extracted_text)} characters")
                    return extracted_text
                else:
                    logger.warning(f"Mistral OCR returned minimal text for {filename}, trying PyMuPDF fallback")
                    return await _fallback_pdf_extraction(content, filename)

            except Exception as ocr_error:
                logger.error(f"Mistral OCR failed for {filename}: {str(ocr_error)}, trying PyMuPDF fallback")
                return await _fallback_pdf_extraction(content, filename)

        # For images, use Mistral OCR directly
        elif filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp')):
            logger.info(f"Processing image {filename} with Mistral Vision OCR")

            try:
                from graph_service.services.mistral_ocr_service import MistralOCRService

                # Initialize Mistral OCR service
                mistral_ocr = MistralOCRService()

                if not mistral_ocr.is_available():
                    logger.error("Mistral OCR not available for image processing")
                    return f"Image processing failed - Mistral OCR not available for {filename}"

                # Convert image content to base64 data URL
                import base64
                # Determine image format
                if filename.lower().endswith('.png'):
                    mime_type = "image/png"
                elif filename.lower().endswith(('.jpg', '.jpeg')):
                    mime_type = "image/jpeg"
                elif filename.lower().endswith('.gif'):
                    mime_type = "image/gif"
                else:
                    mime_type = "image/png"  # Default

                image_base64 = base64.b64encode(content).decode('utf-8')
                image_url = f"data:{mime_type};base64,{image_base64}"

                # Extract text using Mistral OCR with timeout for images
                import asyncio
                try:
                    # Use asyncio.wait_for with 5 minute timeout for images
                    extracted_text = await asyncio.wait_for(
                        mistral_ocr.extract_text(image_url, "image_url"),
                        timeout=300.0  # 5 minutes for images
                    )
                except asyncio.TimeoutError:
                    logger.warning(f"Mistral OCR timed out after 5 minutes for image {filename}")
                    return f"Image processing timed out for {filename}"

                if extracted_text and len(extracted_text.strip()) > 5:
                    logger.info(f"Mistral OCR extraction successful for image {filename}: {len(extracted_text)} characters")
                    return extracted_text
                else:
                    logger.warning(f"Mistral OCR returned minimal text for image {filename}")
                    return f"Image content from {filename} (minimal text extracted)"

            except Exception as ocr_error:
                logger.error(f"Mistral OCR failed for image {filename}: {str(ocr_error)}")
                return f"Error processing image {filename}: {str(ocr_error)}"

        # For Word documents, try basic text extraction
        if filename.lower().endswith(('.doc', '.docx')):
            logger.info(f"Basic text extraction for Word document: {filename}")
            try:
                # Try to decode as text first
                return content.decode('utf-8', errors='ignore')
            except:
                return f"Word document content from {filename} (requires proper Word processing)"

        # Default fallback for other file types
        return content.decode('utf-8', errors='ignore')

    except Exception as e:
        logger.error(f"Error in text extraction for {filename}: {str(e)}")
        return f"Error extracting text from {filename}: {str(e)}"


async def _fallback_pdf_extraction(content: bytes, filename: str) -> str:
    """Fallback PDF text extraction using basic libraries."""
    try:
        # Try PyMuPDF first
        try:
            import fitz  # PyMuPDF
            doc = fitz.open(stream=content, filetype="pdf")
            text = ""
            for page in doc:
                text += page.get_text()
            doc.close()

            if text.strip():
                logger.info(f"PyMuPDF extraction successful for {filename}: {len(text)} characters")
                return text
        except ImportError:
            logger.warning("PyMuPDF not available for PDF extraction")
        except Exception as e:
            logger.warning(f"PyMuPDF extraction failed: {str(e)}")

        # Try PyPDF2 as fallback
        try:
            import PyPDF2
            import io

            pdf_reader = PyPDF2.PdfReader(io.BytesIO(content))
            text = ""
            for page in pdf_reader.pages:
                text += page.extract_text()

            if text.strip():
                logger.info(f"PyPDF2 extraction successful for {filename}: {len(text)} characters")
                return text
        except ImportError:
            logger.warning("PyPDF2 not available for PDF extraction")
        except Exception as e:
            logger.warning(f"PyPDF2 extraction failed: {str(e)}")

        # Final fallback
        logger.error(f"All PDF extraction methods failed for {filename}")
        return f"PDF content from {filename} (OCR extraction failed - please check logs)"

    except Exception as e:
        logger.error(f"Fallback PDF extraction error for {filename}: {str(e)}")
        return f"Error processing PDF {filename}: {str(e)}"


@router.get("/upload/status/{filename}")
async def get_upload_status(filename: str):
    """
    Get the status of a document upload.
    """
    # This would typically check a database or cache for status
    # For now, return a simple response
    return {
        "filename": filename,
        "status": "completed",
        "message": "Document processed successfully"
    }


@router.get("/services/status")
async def get_services_status():
    """
    Get the status of Graphiti services.
    """
    return {"status": "healthy", "message": "Pure Graphiti architecture - no external services needed"}

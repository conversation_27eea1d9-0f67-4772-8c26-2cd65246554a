#!/usr/bin/env python3
"""
Debug upload functionality with detailed error reporting
"""

import requests
import time
import json

def test_upload_debug():
    """Test upload with comprehensive debugging"""
    
    print("🔍 Testing Upload with Debug Info...")
    
    try:
        # Test healthcheck first
        print("\n1. Testing healthcheck...")
        health_response = requests.get("http://localhost:8234/healthcheck", timeout=5)
        print(f"Health Status: {health_response.status_code}")
        print(f"Health Response: {health_response.text}")
        
        # Test if service is responding
        print("\n2. Testing service availability...")
        try:
            stats_response = requests.get("http://localhost:8234/api/stats", timeout=5)
            print(f"Stats Status: {stats_response.status_code}")
            print(f"Stats Response: {stats_response.text}")
        except Exception as e:
            print(f"Stats endpoint error: {e}")
        
        # Test upload endpoint
        print("\n3. Testing upload endpoint...")
        
        # Create test file content
        test_content = "SIBO debug test document. Medical terms: Small Intestinal Bacterial Overgrowth, dysbiosis, methane production, hydrogen breath test, rifaximin treatment."
        
        files = {
            'file': ('debug_test.txt', test_content, 'text/plain')
        }
        
        data = {
            'group_id': 'default',
            'upload_type': 'messages'
        }
        
        print(f"📤 Uploading file: debug_test.txt")
        print(f"Content length: {len(test_content)} characters")
        print(f"Upload data: {data}")
        
        # Make upload request with detailed error handling
        try:
            response = requests.post(
                "http://localhost:8234/api/documents/upload",
                files=files,
                data=data,
                timeout=30
            )
            
            print(f"\n📊 Upload Results:")
            print(f"Status Code: {response.status_code}")
            print(f"Headers: {dict(response.headers)}")
            print(f"Response Text: {response.text}")
            
            if response.status_code == 202:
                print("✅ Upload accepted successfully!")
                
                # Wait and check processing
                print("\n4. Waiting for processing...")
                time.sleep(15)
                
                # Check detailed status
                try:
                    status_response = requests.get(
                        f"http://localhost:8234/api/processing/detailed-status/debug_test.txt?group_id=default",
                        timeout=10
                    )
                    print(f"\nStatus Check:")
                    print(f"Status Code: {status_response.status_code}")
                    print(f"Status Response: {status_response.text}")
                    
                except Exception as e:
                    print(f"Status check error: {e}")
                    
            else:
                print(f"❌ Upload failed with status: {response.status_code}")
                print(f"Error response: {response.text}")
                
        except requests.exceptions.Timeout:
            print("❌ Upload request timed out (30 seconds)")
        except requests.exceptions.ConnectionError as e:
            print(f"❌ Connection error: {e}")
        except Exception as e:
            print(f"❌ Upload request error: {e}")
            
    except Exception as e:
        print(f"❌ General test error: {e}")

if __name__ == "__main__":
    test_upload_debug()

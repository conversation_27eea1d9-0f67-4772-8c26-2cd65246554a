#!/usr/bin/env python3
"""
Reference Extraction Service for Medical Documents

Extracts academic references, citations, and bibliographic information from medical documents
and exports them to CSV format for research purposes.
"""

import logging
import re
import csv
import os
from typing import Dict, List, Optional, Any
from pathlib import Path
import aiohttp
import json

logger = logging.getLogger(__name__)

class ReferenceExtractionService:
    """
    Service for extracting and processing academic references from medical documents.
    Specialized for SIBO and gastrointestinal research papers.
    """
    
    def __init__(self):
        """Initialize the reference extraction service."""
        self.openrouter_api_key = os.getenv('OPENROUTER_API_KEY')
        self.openrouter_url = "https://openrouter.ai/api/v1/chat/completions"
        self.model = "meta-llama/llama-4-maverick:free"
        self.output_dir = Path("extracted_references")
        self.output_dir.mkdir(exist_ok=True)
        logger.info("Reference Extraction Service initialized")
    
    async def extract_references(self, text: str, filename: str) -> List[Dict[str, Any]]:
        """
        Extract academic references from document text.
        
        Args:
            text: Document text content
            filename: Original filename for context
        
        Returns:
            List of extracted references with metadata
        """
        try:
            logger.info(f"Extracting references from {filename}")
            
            # Step 1: Use regex patterns to find potential references
            regex_references = self._extract_references_with_regex(text)
            logger.info(f"Found {len(regex_references)} potential references with regex")
            
            # Step 2: Use LLM to extract and structure references
            llm_references = await self._extract_references_with_llm(text, filename)
            logger.info(f"Extracted {len(llm_references)} references with LLM")
            
            # Step 3: Combine and deduplicate references
            combined_references = self._combine_and_deduplicate(regex_references, llm_references)
            logger.info(f"Final reference count: {len(combined_references)}")
            
            return combined_references
            
        except Exception as e:
            logger.error(f"Error extracting references from {filename}: {str(e)}")
            return []
    
    def _extract_references_with_regex(self, text: str) -> List[Dict[str, Any]]:
        """Extract references using regex patterns."""
        references = []
        
        # Common reference patterns for medical literature
        patterns = [
            # Pattern 1: Author, Year. Title. Journal. Volume(Issue):Pages.
            r'([A-Z][a-z]+(?:\s+[A-Z][a-z]*)*(?:\s+[A-Z]\.)*),?\s+(\d{4})\.\s+([^.]+)\.\s+([^.]+)\.\s+(\d+)(?:\((\d+)\))?:(\d+(?:-\d+)?)',
            
            # Pattern 2: Author et al. (Year). Title. Journal, Volume, Pages.
            r'([A-Z][a-z]+(?:\s+et\s+al\.)?)\s+\((\d{4})\)\.\s+([^.]+)\.\s+([^,]+),\s+(\d+),\s+(\d+(?:-\d+)?)',
            
            # Pattern 3: DOI patterns
            r'doi:\s*(10\.\d+/[^\s]+)',
            
            # Pattern 4: PubMed ID patterns
            r'PMID:\s*(\d+)',
            
            # Pattern 5: URL patterns for online references
            r'https?://[^\s]+',
        ]
        
        for i, pattern in enumerate(patterns):
            matches = re.finditer(pattern, text, re.IGNORECASE | re.MULTILINE)
            for match in matches:
                if i == 0:  # Full reference pattern
                    ref = {
                        "authors": match.group(1).strip(),
                        "year": match.group(2),
                        "title": match.group(3).strip(),
                        "journal": match.group(4).strip(),
                        "volume": match.group(5),
                        "issue": match.group(6) if match.group(6) else "",
                        "pages": match.group(7),
                        "extraction_method": "regex_full"
                    }
                elif i == 1:  # Et al pattern
                    ref = {
                        "authors": match.group(1).strip(),
                        "year": match.group(2),
                        "title": match.group(3).strip(),
                        "journal": match.group(4).strip(),
                        "volume": match.group(5),
                        "pages": match.group(6),
                        "extraction_method": "regex_etal"
                    }
                elif i == 2:  # DOI pattern
                    ref = {
                        "doi": match.group(1),
                        "extraction_method": "regex_doi"
                    }
                elif i == 3:  # PMID pattern
                    ref = {
                        "pmid": match.group(1),
                        "extraction_method": "regex_pmid"
                    }
                elif i == 4:  # URL pattern
                    ref = {
                        "url": match.group(0),
                        "extraction_method": "regex_url"
                    }
                
                references.append(ref)
        
        return references
    
    async def _extract_references_with_llm(self, text: str, filename: str) -> List[Dict[str, Any]]:
        """Extract references using LLM for better accuracy."""
        if not self.openrouter_api_key:
            logger.warning("No OpenRouter API key available for LLM reference extraction")
            return []
        
        try:
            # Create a specialized prompt for reference extraction
            prompt = self._create_reference_extraction_prompt(text, filename)
            
            # Call OpenRouter API
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"Bearer {self.openrouter_api_key}",
                    "Content-Type": "application/json"
                }
                
                payload = {
                    "model": self.model,
                    "messages": [
                        {"role": "user", "content": prompt}
                    ],
                    "temperature": 0.1,
                    "max_tokens": 4000
                }
                
                async with session.post(self.openrouter_url, headers=headers, json=payload) as response:
                    if response.status == 200:
                        result = await response.json()
                        logger.info(f"OpenRouter reference API response keys: {list(result.keys())}")

                        if "choices" in result and len(result["choices"]) > 0:
                            content = result["choices"][0]["message"]["content"]
                            logger.info(f"OpenRouter reference response: {len(content)} characters")
                            logger.debug(f"Reference response preview: {content[:300]}...")

                            if content and content.strip():
                                # Parse the JSON response
                                references = self._parse_llm_references(content)
                                logger.info(f"Parsed {len(references)} references from response")
                                return references
                            else:
                                logger.warning("OpenRouter returned empty content for reference extraction")
                                return []
                        else:
                            logger.error(f"OpenRouter returned invalid response structure")
                            logger.error(f"Full response: {result}")
                            return []
                    else:
                        error_text = await response.text()
                        logger.error(f"OpenRouter reference API error {response.status}: {error_text}")
                        logger.error(f"Request model: {payload.get('model')}")
                        return []
                        
        except Exception as e:
            logger.error(f"Error in LLM reference extraction: {str(e)}")
            return []
    
    def _create_reference_extraction_prompt(self, text: str, filename: str) -> str:
        """Create a specialized prompt for reference extraction."""
        return f"""
You are a medical research assistant specializing in extracting academic references from scientific documents, particularly those related to SIBO (Small Intestinal Bacterial Overgrowth) and gastrointestinal disorders.

Please extract ALL academic references, citations, and bibliographic information from the following document text. Focus on:

1. **Journal Articles**: Author names, publication year, article title, journal name, volume, issue, page numbers
2. **Books**: Author names, publication year, book title, publisher, edition
3. **DOIs**: Digital Object Identifiers
4. **PubMed IDs**: PMID numbers
5. **URLs**: Web links to online resources
6. **Conference Papers**: Conference name, year, location

Document: {filename}

Text:
{text[:8000]}  # Limit text to avoid token limits

Please return the extracted references in the following JSON format:

```json
[
  {{
    "authors": "Last, First; Last2, First2",
    "year": "2024",
    "title": "Article or book title",
    "journal": "Journal name (or 'Book' for books)",
    "volume": "volume number",
    "issue": "issue number",
    "pages": "page range",
    "doi": "DOI if available",
    "pmid": "PubMed ID if available",
    "url": "URL if available",
    "type": "journal_article|book|conference|web_resource",
    "extraction_confidence": 0.95,
    "medical_relevance": "SIBO|gastroenterology|general_medical|other"
  }}
]
```

Extract as many references as possible, even if some fields are missing. Prioritize medical and SIBO-related references.
"""
    
    def _parse_llm_references(self, content: str) -> List[Dict[str, Any]]:
        """Parse LLM response to extract references."""
        try:
            # Clean the response
            content = content.strip()
            
            # Remove markdown code blocks
            if content.startswith("```json"):
                content = content[7:]
            if content.startswith("```"):
                content = content[3:]
            if content.endswith("```"):
                content = content[:-3]
            
            content = content.strip()
            
            # Parse JSON
            references = json.loads(content)
            
            # Validate and clean references
            cleaned_references = []
            for ref in references:
                if isinstance(ref, dict):
                    cleaned_ref = {
                        "authors": ref.get("authors", ""),
                        "year": ref.get("year", ""),
                        "title": ref.get("title", ""),
                        "journal": ref.get("journal", ""),
                        "volume": ref.get("volume", ""),
                        "issue": ref.get("issue", ""),
                        "pages": ref.get("pages", ""),
                        "doi": ref.get("doi", ""),
                        "pmid": ref.get("pmid", ""),
                        "url": ref.get("url", ""),
                        "type": ref.get("type", "unknown"),
                        "extraction_confidence": float(ref.get("extraction_confidence", 0.5)),
                        "medical_relevance": ref.get("medical_relevance", "general_medical"),
                        "extraction_method": "llm"
                    }
                    cleaned_references.append(cleaned_ref)
            
            return cleaned_references
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse LLM reference response: {str(e)}")
            logger.error(f"Response content was: {content}")
            return []
        except Exception as e:
            logger.error(f"Error parsing LLM references: {str(e)}")
            logger.error(f"Response content was: {content}")
            return []
    
    def _combine_and_deduplicate(self, regex_refs: List[Dict], llm_refs: List[Dict]) -> List[Dict[str, Any]]:
        """Combine and deduplicate references from different extraction methods."""
        all_refs = regex_refs + llm_refs
        
        # Simple deduplication based on title similarity
        unique_refs = []
        seen_titles = set()
        
        for ref in all_refs:
            title = ref.get("title", "").lower().strip()
            if title and title not in seen_titles:
                seen_titles.add(title)
                unique_refs.append(ref)
            elif not title:  # Keep references without titles (DOIs, PMIDs, etc.)
                unique_refs.append(ref)
        
        return unique_refs
    
    async def export_references_to_csv(self, references: List[Dict[str, Any]], filename: str) -> str:
        """
        Export extracted references to CSV format.
        
        Args:
            references: List of extracted references
            filename: Original document filename
        
        Returns:
            Path to the created CSV file
        """
        try:
            # Create CSV filename
            base_name = Path(filename).stem
            csv_filename = f"{base_name}_references.csv"
            csv_path = self.output_dir / csv_filename
            
            # Define CSV columns
            columns = [
                "authors", "year", "title", "journal", "volume", "issue", "pages",
                "doi", "pmid", "url", "type", "extraction_confidence", 
                "medical_relevance", "extraction_method"
            ]
            
            # Write CSV file
            with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=columns)
                writer.writeheader()
                
                for ref in references:
                    # Ensure all columns are present
                    row = {col: ref.get(col, "") for col in columns}
                    writer.writerow(row)
            
            logger.info(f"Exported {len(references)} references to {csv_path}")
            return str(csv_path)
            
        except Exception as e:
            logger.error(f"Error exporting references to CSV: {str(e)}")
            return ""
    
    async def get_reference_statistics(self, references: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Get statistics about extracted references."""
        if not references:
            return {"total": 0}
        
        stats = {
            "total": len(references),
            "by_type": {},
            "by_medical_relevance": {},
            "by_extraction_method": {},
            "with_doi": 0,
            "with_pmid": 0,
            "average_confidence": 0.0
        }
        
        confidences = []
        
        for ref in references:
            # Count by type
            ref_type = ref.get("type", "unknown")
            stats["by_type"][ref_type] = stats["by_type"].get(ref_type, 0) + 1
            
            # Count by medical relevance
            relevance = ref.get("medical_relevance", "unknown")
            stats["by_medical_relevance"][relevance] = stats["by_medical_relevance"].get(relevance, 0) + 1
            
            # Count by extraction method
            method = ref.get("extraction_method", "unknown")
            stats["by_extraction_method"][method] = stats["by_extraction_method"].get(method, 0) + 1
            
            # Count DOIs and PMIDs
            if ref.get("doi"):
                stats["with_doi"] += 1
            if ref.get("pmid"):
                stats["with_pmid"] += 1
            
            # Collect confidence scores
            confidence = ref.get("extraction_confidence", 0.0)
            if isinstance(confidence, (int, float)):
                confidences.append(float(confidence))
        
        # Calculate average confidence
        if confidences:
            stats["average_confidence"] = sum(confidences) / len(confidences)
        
        return stats

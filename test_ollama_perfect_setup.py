#!/usr/bin/env python3
"""
Test the perfect setup: Ollama Snowflake embeddings + MedGemma entity extraction.
"""

import asyncio
import logging
import os
import sys
from datetime import datetime, timezone
from dotenv import load_dotenv

# Add the server directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'server'))

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_ollama_perfect_setup():
    """Test the perfect setup with Ollama for both embeddings and entity extraction."""
    try:
        from graphiti_core import Graphiti
        from graphiti_core.nodes import EpisodeType
        from server.graph_service.services.ollama_embedder import OllamaEmbedder
        from server.graph_service.services.ollama_llm_client import OllamaLLMClient
        from graphiti_core.llm_client.config import LLMConfig
        
        # Configuration
        neo4j_uri = 'bolt://localhost:7891'
        neo4j_user = 'neo4j'
        neo4j_password = 'Triathlon16'
        ollama_url = 'http://localhost:11434'
        
        logger.info(f"🔧 TESTING PERFECT OLLAMA SETUP")
        logger.info(f"✅ Entity Extraction: Ollama MedGemma (alibayram/medgemma:latest)")
        logger.info(f"✅ Embeddings: Ollama Snowflake (snowflake-arctic-embed2:latest)")
        logger.info(f"✅ Ollama URL: {ollama_url}")
        
        # Create Ollama LLM client for MedGemma
        llm_config = LLMConfig(
            model="alibayram/medgemma:latest",
            temperature=0.1,
            max_tokens=2000
        )
        llm_client = OllamaLLMClient(config=llm_config)
        
        # Create Ollama embedder for Snowflake
        embedder = OllamaEmbedder(
            base_url=ollama_url,
            model="snowflake-arctic-embed2:latest"
        )
        
        # Create Graphiti instance with Ollama clients
        graphiti = Graphiti(
            uri=neo4j_uri,
            user=neo4j_user,
            password=neo4j_password,
            llm_client=llm_client,
            embedder=embedder
        )
        
        logger.info("🚀 Processing medical document with MedGemma...")
        
        # Medical document content
        document_text = """
        Vitamin D deficiency is a common condition in patients with SIBO (Small Intestinal Bacterial Overgrowth).
        The malabsorption caused by SIBO leads to reduced vitamin D absorption in the small intestine.
        
        Probiotics, particularly Lactobacillus acidophilus and Bifidobacterium bifidum, have shown efficacy 
        in treating SIBO and improving nutrient absorption. These beneficial bacteria help restore the 
        intestinal microbiome balance.
        
        Treatment protocol typically includes:
        - Antimicrobial therapy (rifaximin or herbal antimicrobials)
        - Vitamin D3 supplementation (2000-4000 IU daily)
        - Probiotic therapy with specific strains
        - Dietary modifications (low FODMAP diet)
        """
        
        logger.info(f"📄 Document content: {len(document_text)} characters")
        
        # Process as episode
        result = await graphiti.add_episode(
            name="Medical Case: SIBO and Vitamin D Deficiency",
            episode_body=document_text,
            source=EpisodeType.message,
            source_description="Medical document processed with MedGemma and Snowflake embeddings",
            reference_time=datetime.now(timezone.utc),
            group_id="medical_test_ollama"
        )
        
        logger.info(f"✅ DOCUMENT PROCESSED SUCCESSFULLY!")
        logger.info(f"📊 Results:")
        logger.info(f"  📝 Episode UUID: {result.episode.uuid}")
        logger.info(f"  🏷️  Entities extracted: {len(result.nodes) if result.nodes else 0}")
        logger.info(f"  🔗 Relationships created: {len(result.edges) if result.edges else 0}")
        
        if result.nodes:
            logger.info(f"🏷️  MEDICAL ENTITIES EXTRACTED BY MEDGEMMA:")
            for i, node in enumerate(result.nodes):
                entity_type = getattr(node, 'entity_type', 'Unknown')
                logger.info(f"    {i+1}. {node.name} (Type: {entity_type})")
                if hasattr(node, 'summary') and node.summary:
                    summary = node.summary[:100] + "..." if len(node.summary) > 100 else node.summary
                    logger.info(f"       Summary: {summary}")
        
        if result.edges:
            logger.info(f"🔗 MEDICAL RELATIONSHIPS EXTRACTED:")
            for i, edge in enumerate(result.edges[:5]):  # Show first 5
                logger.info(f"    {i+1}. {edge.name}")
                if hasattr(edge, 'fact') and edge.fact:
                    fact = edge.fact[:100] + "..." if len(edge.fact) > 100 else edge.fact
                    logger.info(f"       Fact: {fact}")
        
        # Clean up
        await graphiti.delete_group("medical_test_ollama")
        await graphiti.close()
        
        logger.info(f"🎉 SUCCESS: Perfect Ollama setup is working!")
        logger.info(f"💡 MedGemma + Snowflake is the ideal configuration for medical documents.")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Ollama setup test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run the Ollama perfect setup test."""
    logger.info("=== TESTING PERFECT OLLAMA SETUP ===")
    
    success = await test_ollama_perfect_setup()
    
    if success:
        logger.info("\n🎉 SUCCESS: Perfect Ollama setup is working!")
        logger.info("📋 Configuration to use:")
        logger.info("  🤖 Entity Extraction: Ollama MedGemma (medical-specific)")
        logger.info("  🔢 Embeddings: Ollama Snowflake (high-quality embeddings)")
        logger.info("  🏥 Perfect for medical document processing!")
    else:
        logger.error("\n❌ FAILED: Ollama setup needs debugging")

if __name__ == "__main__":
    asyncio.run(main())

#!/usr/bin/env python3
"""
Test script to demonstrate the full pipeline integration:
OpenRouter Maverick model + Vision-based OCR for PDF extraction
"""

import os
import sys
import asyncio
import logging
import tempfile
import base64
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_sample_pdf():
    """Create a simple PDF for testing."""
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter
        
        # Create a temporary PDF file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
        temp_path = temp_file.name
        temp_file.close()
        
        # Create PDF content
        c = canvas.Canvas(temp_path, pagesize=letter)
        width, height = letter
        
        # Add content
        c.drawString(100, height - 100, "Medical Research Document")
        c.drawString(100, height - 140, "")
        c.drawString(100, height - 180, "Vitamin C is an essential nutrient that supports immune function.")
        c.drawString(100, height - 220, "Studies have shown that echinacea can help treat the common cold.")
        c.drawString(100, height - 260, "Patients with diabetes should monitor their blood glucose levels.")
        c.drawString(100, height - 300, "Turmeric contains curcumin, which has anti-inflammatory properties.")
        c.drawString(100, height - 340, "")
        c.drawString(100, height - 380, "References:")
        c.drawString(100, height - 420, "1. Smith, J. (2023). Vitamin C and Immune Function. Journal of Nutrition.")
        c.drawString(100, height - 460, "2. Johnson, A. (2022). Echinacea for Cold Treatment. Medical Review.")
        
        c.save()
        
        logger.info(f"Created sample PDF: {temp_path}")
        return temp_path
        
    except ImportError:
        logger.warning("reportlab not available, creating text-based test content")
        return None
    except Exception as e:
        logger.error(f"Error creating PDF: {str(e)}")
        return None

async def test_pdf_processing_pipeline():
    """Test the complete PDF processing pipeline."""
    try:
        from server.graph_service.services.document_processing_service import DocumentProcessingService
        
        logger.info("🔄 Testing complete PDF processing pipeline...")
        
        # Create sample PDF
        pdf_path = create_sample_pdf()
        if not pdf_path:
            # Use text content instead
            logger.info("Using text content for testing...")
            test_content = """
            Medical Research Document
            
            Vitamin C is an essential nutrient that supports immune function.
            Studies have shown that echinacea can help treat the common cold.
            Patients with diabetes should monitor their blood glucose levels.
            Turmeric contains curcumin, which has anti-inflammatory properties.
            
            References:
            1. Smith, J. (2023). Vitamin C and Immune Function. Journal of Nutrition.
            2. Johnson, A. (2022). Echinacea for Cold Treatment. Medical Review.
            """
            
            # Initialize document processing service
            doc_service = DocumentProcessingService()
            
            # Process text content
            result = await doc_service.process_document(
                test_content.encode('utf-8'),
                "test_document.txt",
                "entities"
            )
        else:
            # Read PDF content
            with open(pdf_path, 'rb') as f:
                pdf_content = f.read()
            
            # Initialize document processing service
            doc_service = DocumentProcessingService()
            
            # Process PDF
            result = await doc_service.process_document(
                pdf_content,
                "test_document.pdf",
                "entities"
            )
            
            # Clean up
            os.unlink(pdf_path)
        
        # Check results
        if result.get('success'):
            logger.info("✅ PDF processing pipeline successful!")
            logger.info(f"   Text extracted: {result.get('text_length', 0)} characters")
            logger.info(f"   Entities found: {len(result.get('entities', []))}")
            logger.info(f"   References found: {len(result.get('references', []))}")
            
            # Show some entities
            entities = result.get('entities', [])
            if entities:
                logger.info("   Sample entities:")
                for entity in entities[:3]:
                    logger.info(f"     - {entity['name']} ({entity['type']}) - {entity['confidence']:.2f}")
            
            # Show some references
            references = result.get('references', [])
            if references:
                logger.info("   Sample references:")
                for ref in references[:2]:
                    logger.info(f"     - {ref.get('title', 'Unknown title')}")
            
            return True
        else:
            logger.error(f"❌ PDF processing failed: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing PDF processing pipeline: {str(e)}")
        return False

async def test_entity_extraction_with_maverick():
    """Test entity extraction specifically with Maverick model."""
    try:
        from server.graph_service.services.openrouter_service import OpenRouterService
        
        logger.info("🔄 Testing entity extraction with Maverick model...")
        
        # Initialize service
        openrouter = OpenRouterService()
        
        # Medical text for testing
        medical_text = """
        A 45-year-old patient presented with symptoms of SIBO (Small Intestinal Bacterial Overgrowth).
        Treatment included rifaximin 550mg twice daily for 14 days.
        The patient also received probiotics containing Lactobacillus acidophilus and Bifidobacterium bifidum.
        Dietary modifications included a low-FODMAP diet with reduced fermentable carbohydrates.
        Follow-up breath testing showed significant improvement in hydrogen and methane levels.
        Additional supplements included vitamin B12, vitamin D3, and magnesium glycinate.
        """
        
        # Extract entities
        entities = await openrouter.extract_entities(medical_text)
        
        if entities:
            logger.info(f"✅ Extracted {len(entities)} entities with Maverick model:")
            
            # Group entities by type
            entity_types = {}
            for entity in entities:
                entity_type = entity['type']
                if entity_type not in entity_types:
                    entity_types[entity_type] = []
                entity_types[entity_type].append(entity)
            
            # Show entities by type
            for entity_type, type_entities in entity_types.items():
                logger.info(f"   {entity_type}:")
                for entity in type_entities:
                    logger.info(f"     - {entity['name']} (confidence: {entity['confidence']:.2f})")
            
            return True
        else:
            logger.warning("⚠️ No entities extracted")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing entity extraction: {str(e)}")
        return False

async def test_vision_ocr_fallback():
    """Test the vision OCR fallback system."""
    try:
        logger.info("🔄 Testing vision OCR fallback system...")
        
        # Test Mistral OCR
        from server.graph_service.services.mistral_ocr_service import MistralOCRService
        mistral_ocr = MistralOCRService()
        mistral_available = await mistral_ocr.test_connection()
        
        # Test Ollama OCR
        try:
            from server.graph_service.services.ollama_ocr_service import OllamaOCRService
            ollama_ocr = OllamaOCRService()
            ollama_status = await ollama_ocr.health_check()
            ollama_available = ollama_status.get('status') == 'healthy'
        except Exception:
            ollama_available = False
        
        logger.info(f"   Mistral OCR: {'✅ Available' if mistral_available else '❌ Unavailable'}")
        logger.info(f"   Ollama OCR: {'✅ Available' if ollama_available else '❌ Unavailable'}")
        
        # Check fallback configuration
        use_mistral = os.getenv('USE_MISTRAL_OCR', 'false').lower() == 'true'
        use_ollama = os.getenv('USE_OLLAMA_OCR', 'false').lower() == 'true'
        
        if use_mistral and mistral_available:
            logger.info("   ✅ Primary: Mistral OCR (vision-based)")
        elif use_ollama and ollama_available:
            logger.info("   ✅ Primary: Ollama MedGemma OCR")
        else:
            logger.info("   ⚠️ Fallback: Local OCR will be used")
        
        # At least one OCR method should be available
        return mistral_available or ollama_available
        
    except Exception as e:
        logger.error(f"❌ Error testing vision OCR: {str(e)}")
        return False

async def main():
    """Run comprehensive integration tests."""
    logger.info("🚀 Starting Full Pipeline Integration Test")
    logger.info("OpenRouter Maverick + Vision OCR + Entity Extraction")
    logger.info("=" * 60)
    
    # Test 1: Entity extraction with Maverick
    entity_test = await test_entity_extraction_with_maverick()
    logger.info("")
    
    # Test 2: Vision OCR fallback system
    ocr_test = await test_vision_ocr_fallback()
    logger.info("")
    
    # Test 3: Complete PDF processing pipeline
    pipeline_test = await test_pdf_processing_pipeline()
    logger.info("")
    
    # Summary
    logger.info("📋 Integration Test Summary:")
    logger.info(f"   Entity Extraction (Maverick): {'✅ PASS' if entity_test else '❌ FAIL'}")
    logger.info(f"   Vision OCR System: {'✅ PASS' if ocr_test else '❌ FAIL'}")
    logger.info(f"   Complete Pipeline: {'✅ PASS' if pipeline_test else '❌ FAIL'}")
    
    overall_success = entity_test and pipeline_test
    
    if overall_success:
        logger.info("")
        logger.info("🎉 INTEGRATION SUCCESSFUL!")
        logger.info("✅ OpenRouter Maverick model integrated")
        logger.info("✅ Vision-based OCR configured")
        logger.info("✅ Full pipeline operational")
        logger.info("")
        logger.info("🔧 System Configuration:")
        logger.info(f"   LLM Provider: OpenRouter")
        logger.info(f"   LLM Model: meta-llama/llama-4-maverick:free")
        logger.info(f"   OCR Provider: {'Mistral (vision)' if os.getenv('USE_MISTRAL_OCR') == 'true' else 'Ollama MedGemma'}")
        logger.info(f"   Fallback OCR: {'Enabled' if os.getenv('USE_OLLAMA_OCR') == 'true' else 'Disabled'}")
    else:
        logger.warning("⚠️ Integration partially successful - some components need attention")
    
    return overall_success

if __name__ == "__main__":
    asyncio.run(main())

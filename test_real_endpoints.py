#!/usr/bin/env python3
"""
Test to verify real endpoints are working and not using mock data
"""

import requests
import time
import json

def test_real_endpoints():
    """Test that all endpoints are real and working"""
    
    print("🔍 Testing Real Endpoints...")
    
    try:
        # Test stats endpoint
        print("\n📊 Testing /api/stats endpoint...")
        stats_response = requests.get("http://localhost:8234/api/stats", timeout=10)
        if stats_response.status_code == 200:
            stats_data = stats_response.json()
            print("✅ Stats endpoint working!")
            print(f"📊 Stats: {json.dumps(stats_data, indent=2)}")
        else:
            print(f"❌ Stats endpoint failed: {stats_response.status_code}")
            
        # Test document services status
        print("\n🔧 Testing /api/documents/services/status endpoint...")
        services_response = requests.get("http://localhost:8234/api/documents/services/status", timeout=10)
        if services_response.status_code == 200:
            services_data = services_response.json()
            print("✅ Services status endpoint working!")
            print(f"🔧 Services: {json.dumps(services_data, indent=2)}")
        else:
            print(f"❌ Services endpoint failed: {services_response.status_code}")
            
        # Test a real upload with a simple text file
        print("\n📤 Testing real document upload...")
        test_content = "SIBO test document. Medical terms: Small Intestinal Bacterial Overgrowth, dysbiosis, methane, hydrogen."
        
        files = {
            'file': ('sibo_test.txt', test_content, 'text/plain')
        }
        
        data = {
            'group_id': 'default',
            'upload_type': 'messages'
        }
        
        upload_response = requests.post(
            "http://localhost:8234/api/documents/upload",
            files=files,
            data=data,
            timeout=30
        )
        
        if upload_response.status_code == 202:
            upload_data = upload_response.json()
            print("✅ Upload accepted!")
            print(f"📄 Upload Response: {json.dumps(upload_data, indent=2)}")
            
            # Wait and check processing
            print("\n⏳ Waiting for processing...")
            time.sleep(15)
            
            # Check detailed status
            print("\n🔍 Testing detailed status endpoint...")
            status_response = requests.get(
                "http://localhost:8234/api/processing/detailed-status/sibo_test.txt?group_id=default",
                timeout=10
            )
            
            if status_response.status_code == 200:
                status_data = status_response.json()
                print("✅ Detailed status endpoint working!")
                print(f"📊 Status: {json.dumps(status_data, indent=2)}")
                
                # Check if processing actually happened
                if status_data.get('processing_status') == 'completed':
                    print("🎉 Processing completed successfully!")
                elif status_data.get('processing_status') == 'error':
                    print(f"❌ Processing failed: {status_data.get('error')}")
                else:
                    print(f"⏳ Processing status: {status_data.get('processing_status')}")
                    
            else:
                print(f"❌ Status endpoint failed: {status_response.status_code}")
                print(f"Response: {status_response.text}")
                
        else:
            print(f"❌ Upload failed: {upload_response.status_code}")
            print(f"Response: {upload_response.text}")
            
        # Check logs for any errors
        print("\n📋 Checking recent logs...")
        
    except Exception as e:
        print(f"❌ Test error: {e}")

if __name__ == "__main__":
    test_real_endpoints()

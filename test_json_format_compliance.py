#!/usr/bin/env python3
"""
Test JSON format compliance between our LLM clients and Graphiti's expected schemas.
"""

import json
import asyncio
from pydantic import BaseModel, Field
from typing import List

# Graphiti's expected schemas (copied from the codebase)
class ExtractedEntity(BaseModel):
    name: str = Field(..., description='Name of the extracted entity')
    entity_type_id: int = Field(
        description='ID of the classified entity type. '
        'Must be one of the provided entity_type_id integers.',
    )

class ExtractedEntities(BaseModel):
    extracted_entities: List[ExtractedEntity] = Field(..., description='List of extracted entities')

class MissedEntities(BaseModel):
    missed_entities: List[str] = Field(..., description="Names of entities that weren't extracted")

def test_json_format_compliance():
    """Test various JSON formats that LLMs might produce and our parsing."""
    print("🧪 TESTING JSON FORMAT COMPLIANCE")
    print("=" * 60)
    
    # Test cases: various formats LLMs might produce
    test_cases = [
        # 1. Perfect Graphiti format
        {
            "name": "Perfect Graphiti Format",
            "json": {
                "extracted_entities": [
                    {"name": "SIBO", "entity_type_id": 0},
                    {"name": "Rifaximin", "entity_type_id": 0}
                ]
            },
            "expected": "✅ Should work perfectly"
        },
        
        # 2. Common LLM format with 'entities' instead of 'extracted_entities'
        {
            "name": "Common LLM Format (entities)",
            "json": {
                "entities": [
                    {"name": "SIBO", "entity_type_id": 0},
                    {"name": "Rifaximin", "entity_type_id": 0}
                ]
            },
            "expected": "🔧 Should be fixed by our parser"
        },
        
        # 3. String array format (needs conversion)
        {
            "name": "String Array Format",
            "json": {
                "extracted_entities": ["SIBO", "Rifaximin", "Vitamin D"]
            },
            "expected": "🔧 Should be converted to proper format"
        },
        
        # 4. Mixed format
        {
            "name": "Mixed Format",
            "json": {
                "entities": ["SIBO", "Rifaximin"]
            },
            "expected": "🔧 Should be fixed and converted"
        },
        
        # 5. Typo format
        {
            "name": "Typo Format",
            "json": {
                "misssed_entities": ["entity1", "entity2"]
            },
            "expected": "🔧 Should fix typo"
        }
    ]
    
    # Import our JSON parsing logic
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), 'server'))
    
    try:
        from graph_service.services.ollama_llm_client import OllamaLLMClient
        
        # Create a mock client to test JSON parsing
        client = OllamaLLMClient()
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n{i}. Testing: {test_case['name']}")
            print(f"   Expected: {test_case['expected']}")
            
            try:
                # Convert to JSON string and back (simulate LLM response)
                json_text = json.dumps(test_case['json'])
                print(f"   Input JSON: {json_text}")
                
                # Test our parsing logic
                parsed_json = client._extract_json_from_response(json_text)
                print(f"   Parsed: {parsed_json}")
                
                # Try to validate against Graphiti schema
                if 'extracted_entities' in parsed_json:
                    try:
                        validated = ExtractedEntities.model_validate(parsed_json)
                        print(f"   ✅ Graphiti validation: SUCCESS")
                        print(f"   📊 Entities: {len(validated.extracted_entities)}")
                        for entity in validated.extracted_entities:
                            print(f"      - {entity.name} (type_id: {entity.entity_type_id})")
                    except Exception as e:
                        print(f"   ❌ Graphiti validation: FAILED - {e}")
                
                elif 'missed_entities' in parsed_json:
                    try:
                        validated = MissedEntities.model_validate(parsed_json)
                        print(f"   ✅ Graphiti validation: SUCCESS")
                        print(f"   📊 Missed entities: {len(validated.missed_entities)}")
                    except Exception as e:
                        print(f"   ❌ Graphiti validation: FAILED - {e}")
                
                else:
                    print(f"   ⚠️  No recognized Graphiti fields found")
                    
            except Exception as e:
                print(f"   ❌ Parsing failed: {e}")
    
    except ImportError as e:
        print(f"❌ Could not import OllamaLLMClient: {e}")
        print("💡 Make sure you're running from the correct directory")
        return False
    
    print("\n" + "=" * 60)
    print("🎯 SUMMARY:")
    print("✅ Our JSON parser should handle common LLM format variations")
    print("✅ Automatic conversion to Graphiti's expected schema")
    print("✅ Proper validation against Graphiti's Pydantic models")
    print("🔧 The main issue is likely Ollama API connectivity, not JSON format")
    
    return True

def test_schema_examples():
    """Test that we understand Graphiti's schemas correctly."""
    print("\n🔍 TESTING GRAPHITI SCHEMA UNDERSTANDING")
    print("=" * 60)
    
    # Test ExtractedEntities
    print("1. Testing ExtractedEntities schema:")
    try:
        example_data = {
            "extracted_entities": [
                {"name": "SIBO", "entity_type_id": 0},
                {"name": "Rifaximin", "entity_type_id": 0},
                {"name": "Vitamin D", "entity_type_id": 0}
            ]
        }
        
        validated = ExtractedEntities.model_validate(example_data)
        print(f"   ✅ Schema validation: SUCCESS")
        print(f"   📊 Entities: {len(validated.extracted_entities)}")
        
        # Test JSON serialization
        json_output = validated.model_dump()
        print(f"   📤 JSON output: {json.dumps(json_output, indent=2)}")
        
    except Exception as e:
        print(f"   ❌ Schema validation: FAILED - {e}")
    
    # Test MissedEntities
    print("\n2. Testing MissedEntities schema:")
    try:
        example_data = {
            "missed_entities": ["entity1", "entity2", "entity3"]
        }
        
        validated = MissedEntities.model_validate(example_data)
        print(f"   ✅ Schema validation: SUCCESS")
        print(f"   📊 Missed entities: {len(validated.missed_entities)}")
        
        # Test JSON serialization
        json_output = validated.model_dump()
        print(f"   📤 JSON output: {json.dumps(json_output, indent=2)}")
        
    except Exception as e:
        print(f"   ❌ Schema validation: FAILED - {e}")

if __name__ == "__main__":
    print("🔧 JSON FORMAT COMPLIANCE TESTING")
    print("🎯 Goal: Verify our LLM JSON parsing matches Graphiti's expectations")
    print()
    
    # Test schema understanding
    test_schema_examples()
    
    # Test format compliance
    success = test_json_format_compliance()
    
    if success:
        print("\n🎉 JSON FORMAT COMPLIANCE TESTS COMPLETED!")
        print("✅ Our parsing logic should handle Graphiti schema requirements")
        print("🔧 Focus on fixing Ollama API connectivity issues")
    else:
        print("\n❌ Tests failed - check setup and dependencies")

#!/usr/bin/env python3
"""
Test upload and watch for server logs.
"""

import requests
import tempfile
import os
import time

def create_test_pdf():
    """Create a test PDF."""
    try:
        from reportlab.pdfgen import canvas
        
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
        temp_path = temp_file.name
        temp_file.close()
        
        c = canvas.Canvas(temp_path)
        c.drawString(100, 750, "MEDICAL REPORT")
        c.drawString(100, 720, "Patient: Test Patient")
        c.drawString(100, 690, "Diagnosis: SIBO")
        c.drawString(100, 660, "Treatment: Rifaximin 550mg")
        c.drawString(100, 630, "Probiotics: Lactobacillus")
        c.save()
        
        return temp_path
    except Exception as e:
        print(f"Error creating PDF: {e}")
        return None

def test_upload():
    """Test upload and show processing."""
    print("🔄 Testing upload with server logs...")
    
    pdf_path = create_test_pdf()
    if not pdf_path:
        return False
    
    try:
        print("📤 Uploading PDF...")
        with open(pdf_path, 'rb') as f:
            files = {'file': ('test_logs.pdf', f, 'application/pdf')}
            data = {
                'group_id': 'test_logs_group',
                'upload_type': 'messages'
            }
            
            response = requests.post(
                'http://localhost:8234/api/documents/upload',
                files=files,
                data=data,
                timeout=30
            )
            
            print(f"Status: {response.status_code}")
            if response.status_code == 202:
                result = response.json()
                print("✅ Upload successful!")
                print(f"Response: {result}")
                
                print("⏳ Waiting for processing (check server terminal for logs)...")
                time.sleep(15)
                
                # Check status
                status_response = requests.get(
                    f'http://localhost:8234/api/processing/detailed-status/test_logs.pdf?group_id=test_logs_group',
                    timeout=10
                )
                
                if status_response.status_code == 200:
                    status = status_response.json()
                    print("📊 Final Status:")
                    print(f"   Status: {status.get('processing_status')}")
                    print(f"   Text Length: {status.get('text_length')} characters")
                    print(f"   Episodes: {status.get('episodes_count')}")
                    print(f"   Entities: {status.get('entities_count')}")
                
                return True
            else:
                print(f"❌ Upload failed: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        if os.path.exists(pdf_path):
            os.unlink(pdf_path)

if __name__ == "__main__":
    print("🚀 UPLOAD TEST WITH SERVER LOGS")
    print("Check the server terminal for processing logs!")
    print("=" * 50)
    
    success = test_upload()
    
    if success:
        print("\n✅ Upload completed - check server terminal for processing logs!")
    else:
        print("\n❌ Upload failed")

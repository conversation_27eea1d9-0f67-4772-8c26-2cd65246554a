"""
MedGemma OCR Service for medical document vision processing.
"""
import os
import asyncio
import logging
import base64
from typing import Optional, Dict, Any
import aiohttp

logger = logging.getLogger(__name__)

class MedGemmaOCRService:
    """OCR service using MedGemma model for medical document processing."""

    def __init__(self):
        """Initialize the MedGemma OCR service."""
        self.ollama_url = os.getenv('OLLAMA_API_URL', 'http://host.docker.internal:11434')
        self.model_name = os.getenv('OLLAMA_OCR_MODEL', 'alibayram/medgemma:latest')
        self.google_api_key = os.getenv('GOOGLE_API_KEY')

        # Prefer Google API for vision, fallback to Ollama
        self.use_google_primary = bool(self.google_api_key)

        logger.info(f"MedGemma OCR Service initialized")
        logger.info(f"  Ollama URL: {self.ollama_url}")
        logger.info(f"  Model: {self.model_name}")
        logger.info(f"  Google API: {'Available' if self.google_api_key else 'Not available'}")

    async def extract_text_from_image(self, image_data: bytes, filename: str = "document") -> Optional[str]:
        """
        Extract text from image using MedGemma vision capabilities.

        Args:
            image_data: Raw image data
            filename: Original filename for context

        Returns:
            Extracted text or None if extraction fails
        """
        try:
            # Convert image to base64
            image_base64 = base64.b64encode(image_data).decode('utf-8')

            # Try Google API first (better vision capabilities)
            if self.use_google_primary and self.google_api_key:
                result = await self._extract_with_google_api(image_base64, filename)
                if result:
                    return result
                else:
                    logger.warning("Google API extraction failed, trying Ollama fallback")

            # Fallback to Ollama
            result = await self._extract_with_ollama(image_base64, filename)
            if result:
                return result
            else:
                logger.error("All OCR methods failed")
                return None

        except Exception as e:
            logger.error(f"Error in MedGemma OCR extraction: {str(e)}")
            return None

    async def _extract_with_ollama(self, image_base64: str, filename: str) -> Optional[str]:
        """Extract text using Ollama MedGemma."""
        try:
            # Create vision prompt for medical document analysis
            prompt = f"""
You are a medical document analysis AI with advanced OCR capabilities.
Analyze this medical document image and extract all text content accurately.

Focus on:
- Medical terminology and drug names
- Dosages and measurements
- Patient information and symptoms
- Research findings and citations
- Maintain original formatting where possible

Document: {filename}

Please extract all visible text from this medical document image:
"""

            payload = {
                "model": self.model_name,
                "prompt": prompt,
                "images": [image_base64],
                "stream": False,
                "options": {
                    "temperature": 0.1,  # Low temperature for accurate extraction
                    "top_p": 0.9
                }
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.ollama_url}/api/generate",
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=120)
                ) as response:

                    if response.status == 200:
                        data = await response.json()
                        extracted_text = data.get('response', '').strip()

                        if extracted_text:
                            logger.info(f"Ollama MedGemma extraction successful: {len(extracted_text)} characters")
                            return extracted_text
                        else:
                            logger.warning("Ollama returned empty response")
                            return None
                    else:
                        error_text = await response.text()
                        logger.error(f"Ollama API error {response.status}: {error_text}")
                        return None

        except Exception as e:
            logger.error(f"Ollama extraction error: {str(e)}")
            return None

    async def _extract_with_google_api(self, image_base64: str, filename: str) -> Optional[str]:
        """Extract text using Google Gemini API."""
        try:
            import google.generativeai as genai

            # Configure API
            genai.configure(api_key=self.google_api_key)

            # Use Gemini 2.0 Flash (latest model with vision capabilities)
            model = genai.GenerativeModel('gemini-2.0-flash')

            # Convert base64 to image
            import io
            from PIL import Image
            image_data = base64.b64decode(image_base64)
            image = Image.open(io.BytesIO(image_data))

            # Create medical OCR prompt
            prompt = f"""
You are a medical document OCR specialist. Extract all text from this medical document image.

Focus on accurate extraction of:
- Medical terminology and drug names
- Dosages, measurements, and lab values
- Patient symptoms and conditions
- Research citations and references
- Maintain formatting and structure

Document: {filename}

Extract all visible text:
"""

            # Generate response
            response = model.generate_content([prompt, image])

            if response.text:
                logger.info(f"Google API extraction successful: {len(response.text)} characters")
                return response.text.strip()
            else:
                logger.warning("Google API returned empty response")
                return None

        except ImportError:
            logger.error("google-generativeai not installed")
            return None
        except Exception as e:
            logger.error(f"Google API extraction error: {str(e)}")
            return None

    async def health_check(self) -> Dict[str, Any]:
        """Check the health of the MedGemma OCR service."""
        try:
            # Test Ollama connection
            ollama_status = False
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(f"{self.ollama_url}/api/tags") as response:
                        ollama_status = response.status == 200
            except:
                pass

            # Test Google API
            google_status = bool(self.google_api_key)

            return {
                "status": "healthy" if (ollama_status or google_status) else "unhealthy",
                "ollama_available": ollama_status,
                "google_api_available": google_status,
                "model": self.model_name,
                "capabilities": [
                    "Medical document OCR",
                    "Vision-based text extraction",
                    "Medical terminology recognition",
                    "Multi-format support"
                ]
            }

        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }

    async def test_connection(self) -> bool:
        """Test if the service is available."""
        health = await self.health_check()
        return health.get("status") == "healthy"
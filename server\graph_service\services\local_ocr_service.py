#!/usr/bin/env python3
"""
Local OCR Service using PyMuPDF for reliable PDF text extraction.
This service provides a fallback when external OCR APIs are unavailable or rate-limited.
"""

import logging
import tempfile
import os
from pathlib import Path
from typing import Optional, Dict, Any, List
import fitz  # PyMuPDF
from PIL import Image
import pytesseract
import io

logger = logging.getLogger(__name__)

class LocalOCRService:
    """
    Local OCR service using PyMuPDF for PDF processing and Tesseract for image OCR.
    Provides reliable text extraction without external API dependencies.
    """
    
    def __init__(self):
        """Initialize the local OCR service."""
        self.supported_pdf_formats = ['.pdf']
        self.supported_image_formats = ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff']
        logger.info("Local OCR Service initialized with PyMuPDF and Tesseract")
    
    async def extract_text(self, file_content: bytes, filename: str) -> Optional[str]:
        """
        Extract text from a document using local processing.
        
        Args:
            file_content: Raw file content as bytes
            filename: Original filename for format detection
        
        Returns:
            Extracted text or None if extraction fails
        """
        try:
            file_extension = Path(filename).suffix.lower()
            logger.info(f"Starting local OCR extraction for {filename} (type: {file_extension})")
            
            if file_extension in self.supported_pdf_formats:
                return await self._extract_from_pdf(file_content, filename)
            elif file_extension in self.supported_image_formats:
                return await self._extract_from_image(file_content, filename)
            else:
                logger.warning(f"Unsupported file format: {file_extension}")
                return None
                
        except Exception as e:
            logger.error(f"Error in local OCR extraction for {filename}: {str(e)}")
            return None
    
    async def _extract_from_pdf(self, file_content: bytes, filename: str) -> Optional[str]:
        """
        Extract text from PDF using PyMuPDF.
        
        Args:
            file_content: PDF file content as bytes
            filename: Original filename
        
        Returns:
            Extracted text or None if extraction fails
        """
        try:
            # Create a temporary file for PyMuPDF processing
            with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
                temp_file.write(file_content)
                temp_file_path = temp_file.name
            
            try:
                # Open PDF with PyMuPDF
                doc = fitz.open(temp_file_path)
                extracted_text = ""
                
                logger.info(f"Processing PDF with {len(doc)} pages")
                
                for page_num in range(len(doc)):
                    page = doc.load_page(page_num)
                    
                    # Extract text from page
                    page_text = page.get_text()
                    
                    if page_text.strip():
                        extracted_text += f"\n--- Page {page_num + 1} ---\n"
                        extracted_text += page_text
                        extracted_text += "\n"
                    else:
                        # If no text found, try OCR on page image
                        logger.info(f"No text found on page {page_num + 1}, attempting OCR")
                        page_image_text = await self._ocr_page_image(page, page_num + 1)
                        if page_image_text:
                            extracted_text += f"\n--- Page {page_num + 1} (OCR) ---\n"
                            extracted_text += page_image_text
                            extracted_text += "\n"
                
                doc.close()
                
                if extracted_text.strip():
                    logger.info(f"Successfully extracted {len(extracted_text)} characters from {filename}")
                    return extracted_text.strip()
                else:
                    logger.warning(f"No text extracted from {filename}")
                    return None
                    
            finally:
                # Clean up temporary file
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
                    
        except Exception as e:
            logger.error(f"Error extracting text from PDF {filename}: {str(e)}")
            return None
    
    async def _extract_from_image(self, file_content: bytes, filename: str) -> Optional[str]:
        """
        Extract text from image using Tesseract OCR.
        
        Args:
            file_content: Image file content as bytes
            filename: Original filename
        
        Returns:
            Extracted text or None if extraction fails
        """
        try:
            # Load image from bytes
            image = Image.open(io.BytesIO(file_content))
            
            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # Extract text using Tesseract
            logger.info(f"Performing OCR on image {filename}")
            extracted_text = pytesseract.image_to_string(image)
            
            if extracted_text.strip():
                logger.info(f"Successfully extracted {len(extracted_text)} characters from image {filename}")
                return extracted_text.strip()
            else:
                logger.warning(f"No text extracted from image {filename}")
                return None
                
        except Exception as e:
            logger.error(f"Error extracting text from image {filename}: {str(e)}")
            return None
    
    async def _ocr_page_image(self, page, page_num: int) -> Optional[str]:
        """
        Perform OCR on a PDF page that has no extractable text.
        
        Args:
            page: PyMuPDF page object
            page_num: Page number for logging
        
        Returns:
            Extracted text or None if OCR fails
        """
        try:
            # Render page as image
            mat = fitz.Matrix(2.0, 2.0)  # 2x zoom for better OCR quality
            pix = page.get_pixmap(matrix=mat)
            img_data = pix.tobytes("png")
            
            # Convert to PIL Image
            image = Image.open(io.BytesIO(img_data))
            
            # Perform OCR
            text = pytesseract.image_to_string(image)
            
            if text.strip():
                logger.info(f"OCR extracted {len(text)} characters from page {page_num}")
                return text.strip()
            else:
                logger.warning(f"OCR found no text on page {page_num}")
                return None
                
        except Exception as e:
            logger.error(f"Error performing OCR on page {page_num}: {str(e)}")
            return None
    
    def get_supported_formats(self) -> Dict[str, Any]:
        """
        Get the list of supported file formats.
        
        Returns:
            Dictionary with supported formats and capabilities
        """
        return {
            "pdf_formats": self.supported_pdf_formats,
            "image_formats": self.supported_image_formats,
            "capabilities": [
                "PDF text extraction",
                "PDF image OCR (for scanned documents)",
                "Image OCR",
                "Multi-page document support",
                "High-quality text extraction"
            ],
            "provider": "Local (PyMuPDF + Tesseract)",
            "rate_limits": "None",
            "availability": "Always available"
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Check the health status of the local OCR service.
        
        Returns:
            Health status information
        """
        try:
            # Test PyMuPDF
            pymupdf_available = True
            try:
                import fitz
            except ImportError:
                pymupdf_available = False
            
            # Test Tesseract
            tesseract_available = True
            try:
                import pytesseract
                # Try to get Tesseract version
                pytesseract.get_tesseract_version()
            except Exception:
                tesseract_available = False
            
            status = "healthy" if (pymupdf_available and tesseract_available) else "degraded"
            
            return {
                "status": status,
                "pymupdf_available": pymupdf_available,
                "tesseract_available": tesseract_available,
                "supported_formats": len(self.supported_pdf_formats) + len(self.supported_image_formats),
                "provider": "Local OCR Service"
            }
            
        except Exception as e:
            logger.error(f"Error in health check: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "provider": "Local OCR Service"
            }

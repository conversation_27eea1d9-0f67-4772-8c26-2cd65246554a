#!/usr/bin/env python3
"""
Test the detailed status endpoint specifically
"""

import requests
import time
import json

def test_status_endpoint():
    """Test the detailed status endpoint directly"""
    
    print("🔍 Testing Status Endpoint Directly...")
    
    try:
        # Test with a known filename
        print("\n📊 Testing detailed status endpoint...")
        status_response = requests.get(
            "http://localhost:8234/api/processing/detailed-status/sibo_test.txt?group_id=default",
            timeout=10
        )
        
        print(f"Status Code: {status_response.status_code}")
        print(f"Response: {status_response.text}")
        
        if status_response.status_code == 200:
            status_data = status_response.json()
            print("✅ Status endpoint working!")
            print(f"📊 Status: {json.dumps(status_data, indent=2)}")
        else:
            print(f"❌ Status endpoint failed: {status_response.status_code}")
            
        # Test with a different filename
        print("\n📊 Testing with different filename...")
        status_response2 = requests.get(
            "http://localhost:8234/api/processing/detailed-status/test.txt?group_id=default",
            timeout=10
        )
        
        print(f"Status Code: {status_response2.status_code}")
        if status_response2.status_code == 200:
            status_data2 = status_response2.json()
            print("✅ Status endpoint working!")
            print(f"📊 Status: {json.dumps(status_data2, indent=2)}")
        else:
            print(f"❌ Status endpoint failed: {status_response2.status_code}")
            print(f"Response: {status_response2.text}")
            
    except Exception as e:
        print(f"❌ Test error: {e}")

if __name__ == "__main__":
    test_status_endpoint()

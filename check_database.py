#!/usr/bin/env python3
"""
Check what entities and episodes were created in Neo4j.
"""

import neo4j

def check_database():
    """Check what entities were created in Neo4j."""
    uri = 'bolt://localhost:7891'
    username = 'neo4j'
    password = 'Triathlon16'

    print('🔍 Checking entities in Neo4j...')
    try:
        driver = neo4j.GraphDatabase.driver(uri, auth=(username, password))
        with driver.session() as session:
            # Check entities
            result = session.run('MATCH (n:Entity) RETURN n.name as name, n.uuid as uuid, labels(n) as labels LIMIT 10')
            entities = list(result)
            print(f'📊 Found {len(entities)} entities:')
            for entity in entities:
                name = entity["name"]
                uuid = entity["uuid"][:8] if entity["uuid"] else "None"
                labels = entity["labels"]
                print(f'  - {name} (UUID: {uuid}..., Labels: {labels})')
            
            # Check episodes
            result = session.run('MATCH (n:Episodic) RETURN n.name as name, n.uuid as uuid LIMIT 5')
            episodes = list(result)
            print(f'📊 Found {len(episodes)} episodes:')
            for episode in episodes:
                name = episode["name"]
                uuid = episode["uuid"][:8] if episode["uuid"] else "None"
                print(f'  - {name} (UUID: {uuid}...)')
                
            # Check relationships
            result = session.run('MATCH ()-[r]->() RETURN type(r) as rel_type, count(*) as count')
            relationships = list(result)
            print(f'📊 Found relationships:')
            for rel in relationships:
                rel_type = rel["rel_type"]
                count = rel["count"]
                print(f'  - {rel_type}: {count} instances')
                
        driver.close()
        print('✅ Database check complete!')
        
        # Summary
        if entities:
            print(f'\n🎉 SUCCESS: Found {len(entities)} entities in the database!')
            print(f'✅ MedGemma + Graphiti integration is working!')
        else:
            print(f'\n⚠️  No entities found - processing may still be in progress')
            
    except Exception as e:
        print(f'❌ Database check failed: {str(e)}')

if __name__ == "__main__":
    check_database()

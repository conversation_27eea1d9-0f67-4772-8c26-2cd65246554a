#!/usr/bin/env python3
"""
Timeout fix verification test - increased timeouts for MedGemma processing.

This test verifies that the increased timeouts (5 minutes for LLM, 3 minutes for embeddings)
allow MedGemma to complete entity extraction successfully.
"""

import requests
import tempfile
import os
import time
import json

def create_timeout_test_pdf():
    """Create a test PDF for timeout verification."""
    try:
        from reportlab.pdfgen import canvas
        
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
        temp_path = temp_file.name
        temp_file.close()
        
        c = canvas.Canvas(temp_path)
        c.drawString(100, 750, "TIMEOUT FIX VERIFICATION - INCREASED TIMEOUTS")
        c.drawString(100, 720, "Patient: <PERSON>")
        c.drawString(100, 690, "Date: 2024-06-16")
        c.drawString(100, 660, "Diagnosis: Irritable Bowel Syndrome with SIBO")
        c.drawString(100, 630, "Treatment: Rifaximin 550mg twice daily for 14 days")
        c.drawString(100, 600, "Probiotics: Saccharomyces boulardii 250mg daily")
        c.drawString(100, 570, "Follow-up: Hydrogen breath test in 4 weeks")
        c.drawString(100, 540, "Dr. Lisa Chen, MD - Gastroenterology")
        c.save()
        
        return temp_path
    except Exception as e:
        print(f"Error creating PDF: {e}")
        return None

def test_timeout_fix():
    """Test the timeout fix for MedGemma processing."""
    print("🎯 TIMEOUT FIX VERIFICATION - INCREASED TIMEOUTS")
    print("=" * 60)
    print("Testing with:")
    print("  - LLM Timeout: 300 seconds (5 minutes)")
    print("  - Embedder Timeout: 180 seconds (3 minutes)")
    print("=" * 60)
    
    pdf_path = create_timeout_test_pdf()
    if not pdf_path:
        return False
    
    try:
        print("📤 Uploading timeout test document...")
        with open(pdf_path, 'rb') as f:
            files = {'file': ('timeout_test.pdf', f, 'application/pdf')}
            data = {
                'group_id': 'medical_docs',
                'upload_type': 'messages'
            }
            
            response = requests.post(
                'http://127.0.0.1:8234/api/documents/upload',
                files=files,
                data=data,
                timeout=30
            )
            
            print(f"Status: {response.status_code}")
            if response.status_code == 202:
                result = response.json()
                print("✅ Upload successful!")
                print(f"Response: {result}")
                
                print("\n🔧 MONITORING ENTITY EXTRACTION WITH INCREASED TIMEOUTS:")
                print("  ✅ Document Upload API - Working")
                print("  🔄 Mistral OCR Processing - Testing...")
                print("  🔄 Ollama MedGemma Entity Extraction (5min timeout) - Testing...")
                print("  🔄 Ollama Snowflake Embeddings (3min timeout) - Testing...")
                print("  🔄 JSON Parsing Fixes - Testing...")
                print("  🔄 Graphiti Episode Creation - Testing...")
                print("")
                
                print("Expected entities to extract:")
                print("  - Sarah Johnson, Dr. Lisa Chen")
                print("  - Irritable Bowel Syndrome, SIBO")
                print("  - Rifaximin, 550mg, twice daily, 14 days")
                print("  - Saccharomyces boulardii, 250mg, daily")
                print("  - Hydrogen breath test, Gastroenterology")
                print("")
                
                # Extended wait for processing with longer timeouts
                print("⏳ Waiting for MedGemma processing (allowing up to 6 minutes)...")
                for i in range(360):  # 6 minutes total
                    print(f"⏳ Processing... {i+1}/360 seconds ({(i+1)//60}:{(i+1)%60:02d})", end='\r')
                    time.sleep(1)
                    
                    # Check status every 30 seconds
                    if i % 30 == 29:
                        try:
                            status_response = requests.get(
                                f'http://127.0.0.1:8234/api/processing/detailed-status/timeout_test.pdf?group_id=medical_docs',
                                timeout=5
                            )
                            if status_response.status_code == 200:
                                status = status_response.json()
                                processing_status = status.get('processing_status')
                                if processing_status == 'completed':
                                    print(f"\n🎉 Processing completed at {i+1} seconds ({(i+1)//60}:{(i+1)%60:02d})!")
                                    break
                                elif processing_status == 'failed':
                                    print(f"\n❌ Processing failed at {i+1} seconds!")
                                    break
                                else:
                                    entities_count = status.get('entities_count', 0)
                                    entity_status = status.get('entity_extraction_status', 'unknown')
                                    print(f"\n⏳ Status at {(i+1)//60}:{(i+1)%60:02d}: {processing_status} | Entities: {entities_count} | Extraction: {entity_status}")
                        except:
                            pass
                
                print("\n\n🔍 TIMEOUT FIX VERIFICATION RESULTS:")
                print("=" * 50)
                
                status_response = requests.get(
                    f'http://127.0.0.1:8234/api/processing/detailed-status/timeout_test.pdf?group_id=medical_docs',
                    timeout=10
                )
                
                if status_response.status_code == 200:
                    status = status_response.json()
                    
                    processing_status = status.get('processing_status')
                    text_length = status.get('text_length', 0)
                    entities_count = status.get('entities_count', 0)
                    episodes_count = status.get('episodes_count', 0)
                    ocr_status = status.get('ocr_status')
                    entity_extraction_status = status.get('entity_extraction_status')
                    
                    print(f"📋 Processing Status: {processing_status}")
                    print(f"📝 Text Extracted: {text_length} characters")
                    print(f"📚 Episodes Created: {episodes_count}")
                    print(f"🏷️  Entities Extracted: {entities_count}")
                    print(f"👁️  OCR Status: {ocr_status}")
                    print(f"🧠 Entity Extraction: {entity_extraction_status}")
                    
                    # Timeout fix evaluation
                    print("\n🎯 TIMEOUT FIX EVALUATION:")
                    print("-" * 40)
                    
                    timeout_fixes = {
                        'Document Upload': True,  # Already verified
                        'OCR Processing': ocr_status == 'completed',
                        'Text Extraction': text_length > 100,
                        'Entity Extraction Completed': entity_extraction_status == 'completed',
                        'Entities Found': entities_count > 3,
                        'Episode Creation': episodes_count > 0,
                        'No Timeout Errors': processing_status != 'failed',
                        'Overall Success': processing_status == 'completed'
                    }
                    
                    all_working = True
                    for component, working in timeout_fixes.items():
                        status_icon = "✅" if working else "❌"
                        print(f"  {status_icon} {component}: {working}")
                        if not working:
                            all_working = False
                    
                    if all_working:
                        print(f"\n🎉 TIMEOUT FIX SUCCESSFUL!")
                        print("🔧 TIMEOUT ISSUES RESOLVED:")
                        print("  ✅ LLM Timeout: 120s → 300s (5 minutes)")
                        print("  ✅ Embedder Timeout: 60s → 180s (3 minutes)")
                        print("  ✅ MedGemma entity extraction → Working")
                        print("  ✅ No more timeout errors → Fixed")
                        print("  ✅ Complete pipeline → Working")
                        print("")
                        print("🎯 ENTITY EXTRACTION IS NOW WORKING!")
                        return True
                    else:
                        print(f"\n⚠️ TIMEOUT FIX PARTIALLY SUCCESSFUL")
                        print("Some components may still need attention.")
                        if entity_extraction_status != 'completed':
                            print("💡 Entity extraction may need even longer timeout or different approach.")
                        return False
                        
                else:
                    print(f"❌ Status check failed: {status_response.status_code}")
                    return False
                
            else:
                print(f"❌ Upload failed: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        if os.path.exists(pdf_path):
            os.unlink(pdf_path)

if __name__ == "__main__":
    print("🎯 TIMEOUT FIX VERIFICATION TEST")
    print("Testing increased timeouts for MedGemma entity extraction")
    print("=" * 60)
    
    success = test_timeout_fix()
    
    if success:
        print("\n🎉 TIMEOUT FIX VERIFICATION COMPLETE!")
        print("Entity extraction timeouts have been successfully resolved!")
    else:
        print("\n🔧 Additional timeout adjustments may be needed")
        print("Check server logs for detailed processing information")

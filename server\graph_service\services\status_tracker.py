#!/usr/bin/env python3
"""
Status Tracking Service for Document Processing

Provides real-time status tracking for document processing operations
without relying on the graph database.
"""

import logging
import asyncio
from typing import Dict, Any, Optional
from datetime import datetime
import json

logger = logging.getLogger(__name__)

class StatusTracker:
    """
    In-memory status tracking for document processing operations.
    """
    
    def __init__(self):
        """Initialize the status tracker."""
        self._status_store: Dict[str, Dict[str, Any]] = {}
        self._lock = asyncio.Lock()
        logger.info("Status Tracker initialized")
    
    async def start_processing(self, filename: str, group_id: str = "default") -> None:
        """
        Mark a document as starting processing.
        
        Args:
            filename: Document filename
            group_id: Group identifier
        """
        async with self._lock:
            key = f"{group_id}:{filename}"
            self._status_store[key] = {
                "filename": filename,
                "group_id": group_id,
                "processing_status": "processing",
                "started_at": datetime.now().isoformat(),
                "last_updated": datetime.now().isoformat(),
                "steps": {
                    "upload_completed": True,
                    "ocr_started": False,
                    "ocr_completed": False,
                    "entity_extraction_started": False,
                    "entity_extraction_completed": False,
                    "reference_extraction_started": False,
                    "reference_extraction_completed": False,
                    "csv_export_completed": False,
                    "graph_storage_completed": False
                },
                "results": {
                    "text_length": 0,
                    "entities_count": 0,
                    "references_count": 0,
                    "episodes_count": 0,
                    "csv_export_path": ""
                },
                "error_message": None
            }
            logger.info(f"Started tracking processing for {filename}")
    
    async def update_step(self, filename: str, step: str, completed: bool = True, 
                         group_id: str = "default", **kwargs) -> None:
        """
        Update a processing step status.
        
        Args:
            filename: Document filename
            step: Step name (e.g., 'ocr_started', 'ocr_completed')
            completed: Whether the step is completed
            group_id: Group identifier
            **kwargs: Additional data to store
        """
        async with self._lock:
            key = f"{group_id}:{filename}"
            if key in self._status_store:
                self._status_store[key]["steps"][step] = completed
                self._status_store[key]["last_updated"] = datetime.now().isoformat()
                
                # Update results if provided
                for result_key, value in kwargs.items():
                    if result_key in self._status_store[key]["results"]:
                        self._status_store[key]["results"][result_key] = value
                
                logger.debug(f"Updated step {step} for {filename}: {completed}")
    
    async def complete_processing(self, filename: str, group_id: str = "default", 
                                 success: bool = True, **results) -> None:
        """
        Mark processing as completed.
        
        Args:
            filename: Document filename
            group_id: Group identifier
            success: Whether processing was successful
            **results: Final results to store
        """
        async with self._lock:
            key = f"{group_id}:{filename}"
            if key in self._status_store:
                self._status_store[key]["processing_status"] = "completed" if success else "failed"
                self._status_store[key]["completed_at"] = datetime.now().isoformat()
                self._status_store[key]["last_updated"] = datetime.now().isoformat()
                
                # Update final results
                for result_key, value in results.items():
                    if result_key in self._status_store[key]["results"]:
                        self._status_store[key]["results"][result_key] = value
                
                logger.info(f"Completed processing for {filename}: {'success' if success else 'failed'}")
    
    async def set_error(self, filename: str, error_message: str, group_id: str = "default") -> None:
        """
        Mark processing as failed with error message.
        
        Args:
            filename: Document filename
            error_message: Error description
            group_id: Group identifier
        """
        async with self._lock:
            key = f"{group_id}:{filename}"
            if key in self._status_store:
                self._status_store[key]["processing_status"] = "failed"
                self._status_store[key]["error_message"] = error_message
                self._status_store[key]["failed_at"] = datetime.now().isoformat()
                self._status_store[key]["last_updated"] = datetime.now().isoformat()
                
                logger.error(f"Processing failed for {filename}: {error_message}")
    
    async def get_status(self, filename: str, group_id: str = "default") -> Optional[Dict[str, Any]]:
        """
        Get processing status for a document.
        
        Args:
            filename: Document filename
            group_id: Group identifier
        
        Returns:
            Status information or None if not found
        """
        async with self._lock:
            key = f"{group_id}:{filename}"
            status = self._status_store.get(key)
            if status:
                # Create a copy to avoid external modifications
                return dict(status)
            return None
    
    async def list_all_status(self) -> Dict[str, Dict[str, Any]]:
        """
        Get all processing statuses.
        
        Returns:
            Dictionary of all statuses
        """
        async with self._lock:
            return dict(self._status_store)
    
    async def cleanup_old_entries(self, max_age_hours: int = 24) -> int:
        """
        Clean up old status entries.
        
        Args:
            max_age_hours: Maximum age in hours before cleanup
        
        Returns:
            Number of entries cleaned up
        """
        async with self._lock:
            current_time = datetime.now()
            keys_to_remove = []
            
            for key, status in self._status_store.items():
                try:
                    last_updated = datetime.fromisoformat(status["last_updated"])
                    age_hours = (current_time - last_updated).total_seconds() / 3600
                    
                    if age_hours > max_age_hours:
                        keys_to_remove.append(key)
                except Exception as e:
                    logger.warning(f"Error checking age for {key}: {e}")
                    keys_to_remove.append(key)  # Remove problematic entries
            
            for key in keys_to_remove:
                del self._status_store[key]
            
            if keys_to_remove:
                logger.info(f"Cleaned up {len(keys_to_remove)} old status entries")
            
            return len(keys_to_remove)
    
    def get_summary_stats(self) -> Dict[str, Any]:
        """
        Get summary statistics.
        
        Returns:
            Summary statistics
        """
        total = len(self._status_store)
        processing = sum(1 for s in self._status_store.values() if s["processing_status"] == "processing")
        completed = sum(1 for s in self._status_store.values() if s["processing_status"] == "completed")
        failed = sum(1 for s in self._status_store.values() if s["processing_status"] == "failed")
        
        return {
            "total_documents": total,
            "processing": processing,
            "completed": completed,
            "failed": failed,
            "success_rate": (completed / total * 100) if total > 0 else 0
        }

# Global status tracker instance
_status_tracker: Optional[StatusTracker] = None

def get_status_tracker() -> StatusTracker:
    """Get the global status tracker instance."""
    global _status_tracker
    if _status_tracker is None:
        _status_tracker = StatusTracker()
    return _status_tracker

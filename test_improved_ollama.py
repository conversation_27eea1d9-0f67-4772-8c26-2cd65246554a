#!/usr/bin/env python3
"""
Test the improved Ollama MedGemma client with better JSON formatting.
"""

import requests
import tempfile
import os
import time

def create_test_pdf():
    """Create a test PDF with clear medical entities."""
    try:
        from reportlab.pdfgen import canvas
        
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
        temp_path = temp_file.name
        temp_file.close()
        
        c = canvas.Canvas(temp_path)
        c.drawString(100, 750, "MEDICAL REPORT - IMPROVED OLLAMA TEST")
        c.drawString(100, 720, "Patient: <PERSON>")
        c.drawString(100, 690, "Date: 2024-06-16")
        c.drawString(100, 660, "Diagnosis: SIBO and Lactose Intolerance")
        c.drawString(100, 630, "Treatment: Rifaximin 550mg twice daily")
        c.drawString(100, 600, "Probiotics: Lactobacillus acidophilus")
        c.drawString(100, 570, "Supplements: Vitamin B12 1000mcg")
        c.drawString(100, 540, "Follow-up: Hydrogen breath test")
        c.drawString(100, 510, "Dr. Johnson - Gastroenterology")
        c.save()
        
        return temp_path
    except Exception as e:
        print(f"Error creating PDF: {e}")
        return None

def test_improved_ollama():
    """Test the improved Ollama client."""
    print("🚀 TESTING IMPROVED OLLAMA MEDGEMMA CLIENT")
    print("=" * 60)
    
    pdf_path = create_test_pdf()
    if not pdf_path:
        return False
    
    try:
        print("📤 Uploading PDF with improved Ollama client...")
        with open(pdf_path, 'rb') as f:
            files = {'file': ('improved_ollama_test.pdf', f, 'application/pdf')}
            data = {
                'group_id': 'medical_docs',
                'upload_type': 'messages'
            }
            
            response = requests.post(
                'http://127.0.0.1:8234/api/documents/upload',
                files=files,
                data=data,
                timeout=30
            )
            
            print(f"Status: {response.status_code}")
            if response.status_code == 202:
                result = response.json()
                print("✅ Upload successful!")
                print(f"Response: {result}")
                
                print("\n⏳ Processing with improved Ollama MedGemma...")
                print("Expected entities: SIBO, Lactose Intolerance, Rifaximin, Vitamin B12, etc.")
                print("")
                
                # Wait for processing
                for i in range(45):
                    print(f"⏳ Processing... {i+1}/45 seconds", end='\r')
                    time.sleep(1)
                
                print("\n\n🔍 Checking final status...")
                status_response = requests.get(
                    f'http://127.0.0.1:8234/api/processing/detailed-status/improved_ollama_test.pdf?group_id=medical_docs',
                    timeout=10
                )
                
                if status_response.status_code == 200:
                    status = status_response.json()
                    print("📊 FINAL RESULTS:")
                    print("=" * 30)
                    print(f"   📋 Status: {status.get('processing_status')}")
                    print(f"   📝 Text Length: {status.get('text_length')} characters")
                    print(f"   📚 Episodes: {status.get('episodes_count')}")
                    print(f"   🏷️  Entities: {status.get('entities_count')}")
                    print(f"   👁️  OCR Status: {status.get('ocr_status')}")
                    print(f"   🧠 Entity Extraction: {status.get('entity_extraction_status')}")
                    
                    if (status.get('processing_status') == 'completed' and 
                        status.get('entities_count', 0) > 0):
                        print("\n🎉 SUCCESS! Ollama MedGemma entity extraction working!")
                        print("✅ Medical entities successfully extracted and stored!")
                        return True
                    else:
                        print(f"\n⚠️ Processing status: {status.get('processing_status')}")
                        print("Check server logs for detailed error information")
                        return False
                else:
                    print(f"❌ Status check failed: {status_response.status_code}")
                    return False
                
            else:
                print(f"❌ Upload failed: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        if os.path.exists(pdf_path):
            os.unlink(pdf_path)

if __name__ == "__main__":
    print("🎯 IMPROVED OLLAMA MEDGEMMA TEST")
    print("Testing enhanced JSON formatting and medical entity extraction")
    print("=" * 70)
    
    success = test_improved_ollama()
    
    if success:
        print("\n🎉 SYSTEM FULLY OPERATIONAL!")
        print("Your PDF upload system with Ollama MedGemma is working perfectly!")
    else:
        print("\n🔧 Check server logs for debugging information")

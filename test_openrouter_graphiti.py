#!/usr/bin/env python3
"""
Test script to verify OpenRouter configuration and Graphiti entity extraction.
"""

import asyncio
import logging
import os
from datetime import datetime, timezone
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_openrouter_graphiti():
    """Test OpenRouter configuration with Graphiti entity extraction."""
    
    try:
        # Import Graphiti
        from graphiti_core import Graphiti
        from graphiti_core.nodes import EpisodeType
        
        # Get configuration from environment
        # Use localhost with Docker-mapped port for testing outside Docker
        neo4j_uri = 'bolt://localhost:7891'
        neo4j_user = os.getenv('NEO4J_USER', 'neo4j')
        neo4j_password = os.getenv('NEO4J_PASSWORD', 'Triathlon16')
        openai_api_key = os.getenv('OPENAI_API_KEY')
        openai_base_url = os.getenv('OPENAI_BASE_URL')
        model_name = os.getenv('MODEL_NAME', 'meta-llama/llama-4-maverick:free')
        
        logger.info(f"Testing Graphiti with OpenRouter configuration:")
        logger.info(f"  Neo4j URI: {neo4j_uri}")
        logger.info(f"  OpenAI Base URL: {openai_base_url}")
        logger.info(f"  Model: {model_name}")
        logger.info(f"  API Key: {openai_api_key[:10]}..." if openai_api_key else "  API Key: None")
        
        # Initialize Graphiti client
        graphiti = Graphiti(
            uri=neo4j_uri,
            user=neo4j_user,
            password=neo4j_password
        )
        
        # Configure LLM client
        if openai_base_url:
            graphiti.llm_client.config.base_url = openai_base_url
        if openai_api_key:
            graphiti.llm_client.config.api_key = openai_api_key
        if model_name:
            graphiti.llm_client.model = model_name
            
        logger.info(f"LLM Client configured:")
        logger.info(f"  Base URL: {graphiti.llm_client.config.base_url}")
        logger.info(f"  Model: {graphiti.llm_client.model}")
        
        # Initialize database
        await graphiti.build_indices_and_constraints()
        logger.info("Database indices and constraints built successfully")
        
        # Test with a simple medical text
        test_text = """
        Vitamin D deficiency is common in patients with SIBO (Small Intestinal Bacterial Overgrowth). 
        Studies show that probiotics like Lactobacillus acidophilus can help restore gut microbiome balance. 
        Rifaximin is an antibiotic commonly prescribed for SIBO treatment. 
        Patients often experience symptoms like bloating, abdominal pain, and diarrhea.
        """
        
        logger.info("Adding test episode with medical content...")
        
        # Add episode and extract entities
        result = await graphiti.add_episode(
            name="SIBO Treatment Test",
            episode_body=test_text,
            source=EpisodeType.message,
            source_description="Medical knowledge test",
            reference_time=datetime.now(timezone.utc),
            group_id="test_group"
        )
        
        logger.info(f"Episode processing completed!")
        logger.info(f"  Episode UUID: {result.episode.uuid}")
        logger.info(f"  Extracted Nodes: {len(result.nodes)}")
        logger.info(f"  Extracted Edges: {len(result.edges)}")
        
        # Log extracted entities
        if result.nodes:
            logger.info("Extracted Entities:")
            for i, node in enumerate(result.nodes):
                logger.info(f"  {i+1}. {node.name} (UUID: {node.uuid})")
        else:
            logger.warning("No entities were extracted!")
            
        # Log extracted relationships
        if result.edges:
            logger.info("Extracted Relationships:")
            for i, edge in enumerate(result.edges):
                logger.info(f"  {i+1}. {edge.fact} (Relation: {edge.name})")
        else:
            logger.warning("No relationships were extracted!")
            
        # Clean up test data
        logger.info("Cleaning up test data...")
        await graphiti.delete_group("test_group")
        
        await graphiti.close()
        
        return len(result.nodes), len(result.edges)
        
    except Exception as e:
        logger.error(f"Error in OpenRouter Graphiti test: {str(e)}")
        import traceback
        traceback.print_exc()
        return 0, 0

async def main():
    """Main test function."""
    logger.info("Starting OpenRouter Graphiti Entity Extraction Test")
    
    entities_count, relationships_count = await test_openrouter_graphiti()
    
    logger.info(f"\n=== TEST RESULTS ===")
    logger.info(f"Entities extracted: {entities_count}")
    logger.info(f"Relationships extracted: {relationships_count}")
    
    if entities_count > 0 and relationships_count > 0:
        logger.info("✅ SUCCESS: Entity extraction is working correctly!")
    elif entities_count > 0:
        logger.info("⚠️  PARTIAL: Entities extracted but no relationships")
    else:
        logger.error("❌ FAILED: No entities or relationships extracted")
        logger.error("This indicates an issue with the LLM client configuration or API")

if __name__ == "__main__":
    asyncio.run(main())

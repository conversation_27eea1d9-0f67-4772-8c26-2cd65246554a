"""
Ollama Embedder Client for Graphiti

This module provides an embedder client that uses Ollama for generating embeddings,
specifically configured to use the snowflake-arctic-embed2:latest model.
"""

import logging
import httpx
from typing import List, Iterable
from graphiti_core.embedder.client import EmbedderClient

logger = logging.getLogger(__name__)


class OllamaEmbedder(EmbedderClient):
    """
    Ollama Embedder Client for Graphiti
    
    Uses Ollama API to generate embeddings using the snowflake-arctic-embed2:latest model
    as configured in the .env file.
    """
    
    def __init__(self, base_url: str = "http://host.docker.internal:11434", model: str = "snowflake-arctic-embed2:latest"):
        """
        Initialize the Ollama embedder client.
        
        Args:
            base_url: Ollama API base URL
            model: Embedding model to use (default: snowflake-arctic-embed2:latest)
        """
        self.base_url = base_url.rstrip('/')
        self.model = model
        self.client = httpx.AsyncClient(timeout=180.0)  # Increased timeout for embeddings
        
        logger.info(f"Ollama Embedder initialized with model: {self.model}")
        logger.info(f"Ollama URL: {self.base_url}")
    
    async def create(self, input_data: str | list[str] | Iterable[int] | Iterable[Iterable[int]]) -> list[float]:
        """
        Create embeddings for the given input data.
        
        Args:
            input_data: The input data to create embeddings for
            
        Returns:
            A list of floats representing the embedding vector
        """
        try:
            # Convert input to string if needed
            if isinstance(input_data, str):
                text = input_data
            elif isinstance(input_data, list) and len(input_data) > 0 and isinstance(input_data[0], str):
                text = input_data[0]  # Take first string from list
            else:
                text = str(input_data)
            
            # Prepare request payload
            payload = {
                "model": self.model,
                "prompt": text
            }
            
            # Make request to Ollama embeddings endpoint
            response = await self.client.post(
                f"{self.base_url}/api/embeddings",
                json=payload
            )
            
            if response.status_code == 200:
                result = response.json()
                embeddings = result.get("embedding", [])
                
                if embeddings:
                    logger.debug(f"Generated embedding of dimension {len(embeddings)} for text length {len(text)}")
                    return embeddings
                else:
                    logger.error("No embeddings returned from Ollama")
                    raise ValueError("No embeddings returned from Ollama")
            else:
                logger.error(f"Ollama embeddings API error: {response.status_code} - {response.text}")
                raise ValueError(f"Ollama API error: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Error generating embeddings with Ollama: {str(e)}")
            raise
    
    async def create_batch(self, input_data_list: list[str]) -> list[list[float]]:
        """
        Create embeddings for a batch of input data.
        
        Args:
            input_data_list: List of strings to create embeddings for
            
        Returns:
            List of embedding vectors
        """
        embeddings = []
        
        for text in input_data_list:
            try:
                embedding = await self.create(text)
                embeddings.append(embedding)
            except Exception as e:
                logger.error(f"Error generating embedding for text: {str(e)}")
                # Return zero vector as fallback
                embeddings.append([0.0] * 1024)  # Assuming 1024 dimensions for snowflake model
        
        return embeddings
    
    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()
    
    def __del__(self):
        """Cleanup when object is destroyed."""
        try:
            import asyncio
            loop = asyncio.get_event_loop()
            if loop.is_running():
                loop.create_task(self.close())
        except:
            pass

#!/usr/bin/env python3
"""
Test direct document upload to verify the async generator issue is resolved
"""

import requests
import time
import json

def test_direct_upload():
    """Test direct document upload to see if processing works"""
    
    print("🔍 Testing Direct Document Upload...")
    
    try:
        # Create a simple test document
        test_content = "SIBO test document for async generator fix. Medical terms: Small Intestinal Bacterial Overgrowth, dysbiosis, methane, hydrogen, rifaximin, lactulose breath test."
        
        files = {
            'file': ('async_fix_test.txt', test_content, 'text/plain')
        }
        
        data = {
            'group_id': 'default',
            'upload_type': 'messages'
        }
        
        print("📤 Uploading test document...")
        response = requests.post(
            "http://localhost:8234/api/documents/upload",
            files=files,
            data=data,
            timeout=30
        )
        
        print(f"Upload Status Code: {response.status_code}")
        print(f"Upload Response: {response.text}")
        
        if response.status_code == 202:
            result = response.json()
            print("✅ Upload accepted!")
            print(f"📄 Response: {json.dumps(result, indent=2)}")
            
            # Wait for processing
            print("⏳ Waiting for background processing...")
            time.sleep(20)
            
            # Check if any processing happened by looking at logs
            print("🔍 Checking if processing completed...")
            
            # Test stats endpoint to see if anything was processed
            try:
                stats_response = requests.get("http://localhost:8234/api/stats", timeout=10)
                if stats_response.status_code == 200:
                    stats_data = stats_response.json()
                    print("📊 Stats Response:")
                    print(json.dumps(stats_data, indent=2))
                else:
                    print(f"❌ Stats endpoint failed: {stats_response.status_code}")
                    
            except Exception as e:
                print(f"❌ Stats endpoint error: {e}")
                
        else:
            print(f"❌ Upload failed with status: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Test error: {e}")

if __name__ == "__main__":
    test_direct_upload()

#!/usr/bin/env python3
"""
Ollama OCR Service using MedGemma for medical document processing.
This service provides a local alternative when external OCR APIs are rate-limited.
"""

import logging
import tempfile
import os
import base64
from pathlib import Path
from typing import Optional, Dict, Any, List
import aiohttp
import json
import fitz  # PyMuPDF for PDF processing
from PIL import Image
import io

logger = logging.getLogger(__name__)

class OllamaOCRService:
    """
    Ollama OCR service using MedGemma for medical document processing.
    Combines PyMuPDF for text extraction with MedGemma for medical entity understanding.
    """
    
    def __init__(self):
        """Initialize the Ollama OCR service."""
        self.ollama_url = os.getenv('OLLAMA_API_URL', 'http://localhost:11434')
        self.model_name = os.getenv('OLLAMA_OCR_MODEL', 'alibayram/medgemma:latest')  # Primary medical OCR model
        self.supported_pdf_formats = ['.pdf']
        self.supported_image_formats = ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff']
        logger.info(f"Ollama OCR Service initialized with MedGemma at {self.ollama_url}")
    
    async def extract_text(self, file_content: bytes, filename: str) -> Optional[str]:
        """
        Extract and enhance text from a document using PyMuPDF + MedGemma.
        
        Args:
            file_content: Raw file content as bytes
            filename: Original filename for format detection
        
        Returns:
            Enhanced extracted text or None if extraction fails
        """
        try:
            file_extension = Path(filename).suffix.lower()
            logger.info(f"Starting Ollama OCR extraction for {filename} (type: {file_extension})")
            
            # Step 1: Extract raw text using PyMuPDF
            raw_text = None
            if file_extension in self.supported_pdf_formats:
                raw_text = await self._extract_from_pdf(file_content, filename)
            elif file_extension in self.supported_image_formats:
                raw_text = await self._extract_from_image(file_content, filename)
            elif file_extension in ['.txt', '.md']:
                raw_text = file_content.decode('utf-8', errors='ignore')
            else:
                logger.warning(f"Unsupported file format: {file_extension}")
                return None
            
            if not raw_text or len(raw_text.strip()) < 10:
                logger.warning(f"No meaningful text extracted from {filename}")
                return raw_text
            
            # Step 2: Enhance with MedGemma for medical context
            enhanced_text = await self._enhance_with_medgemma(raw_text, filename)
            
            if enhanced_text:
                logger.info(f"Successfully enhanced text with MedGemma: {len(enhanced_text)} characters")
                return enhanced_text
            else:
                logger.info(f"MedGemma enhancement failed, returning raw text: {len(raw_text)} characters")
                return raw_text
                
        except Exception as e:
            logger.error(f"Error in Ollama OCR extraction for {filename}: {str(e)}")
            return None
    
    async def _extract_from_pdf(self, file_content: bytes, filename: str) -> Optional[str]:
        """Extract text from PDF using PyMuPDF."""
        try:
            with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
                temp_file.write(file_content)
                temp_file_path = temp_file.name
            
            try:
                doc = fitz.open(temp_file_path)
                extracted_text = ""
                
                logger.info(f"Processing PDF with {len(doc)} pages")
                
                for page_num in range(len(doc)):
                    page = doc.load_page(page_num)
                    page_text = page.get_text()
                    
                    if page_text.strip():
                        extracted_text += f"\n--- Page {page_num + 1} ---\n"
                        extracted_text += page_text
                        extracted_text += "\n"
                
                doc.close()
                
                if extracted_text.strip():
                    logger.info(f"Successfully extracted {len(extracted_text)} characters from PDF")
                    return extracted_text.strip()
                else:
                    logger.warning(f"No text extracted from PDF {filename}")
                    return None
                    
            finally:
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
                    
        except Exception as e:
            logger.error(f"Error extracting text from PDF {filename}: {str(e)}")
            return None
    
    async def _extract_from_image(self, file_content: bytes, filename: str) -> Optional[str]:
        """Extract text from image using basic OCR (placeholder for now)."""
        try:
            # For now, return a placeholder - could integrate Tesseract here
            logger.info(f"Image OCR not yet implemented for {filename}")
            return f"[Image content from {filename} - OCR processing would be implemented here]"
        except Exception as e:
            logger.error(f"Error extracting text from image {filename}: {str(e)}")
            return None
    
    async def _enhance_with_medgemma(self, raw_text: str, filename: str) -> Optional[str]:
        """
        Enhance extracted text using MedGemma for medical context and structure.
        
        Args:
            raw_text: Raw extracted text
            filename: Original filename for context
        
        Returns:
            Enhanced text with medical insights or None if enhancement fails
        """
        try:
            logger.info(f"Enhancing text with MedGemma for {filename}")
            
            # Create a medical-focused prompt for text enhancement
            prompt = self._create_medical_enhancement_prompt(raw_text, filename)
            
            # Call Ollama API
            async with aiohttp.ClientSession() as session:
                payload = {
                    "model": self.model_name,
                    "prompt": prompt,
                    "stream": False,
                    "options": {
                        "temperature": 0.1,  # Low temperature for consistent medical analysis
                        "top_p": 0.9,
                        "num_predict": 4000  # Allow longer responses
                    }
                }
                
                async with session.post(
                    f"{self.ollama_url}/api/generate",
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=120)
                ) as response:
                    
                    if response.status == 200:
                        result = await response.json()
                        enhanced_text = result.get('response', '').strip()
                        
                        if enhanced_text and len(enhanced_text) > 50:
                            logger.info(f"MedGemma enhancement successful: {len(enhanced_text)} characters")
                            return enhanced_text
                        else:
                            logger.warning("MedGemma returned empty or very short response")
                            return None
                    else:
                        logger.error(f"Ollama API error: {response.status}")
                        return None
                        
        except Exception as e:
            logger.error(f"Error enhancing text with MedGemma: {str(e)}")
            return None
    
    def _create_medical_enhancement_prompt(self, raw_text: str, filename: str) -> str:
        """
        Create a medical-focused prompt for text enhancement.
        
        Args:
            raw_text: Raw extracted text
            filename: Original filename for context
        
        Returns:
            Enhanced prompt for medical analysis
        """
        return f"""
You are a medical AI assistant specializing in gastrointestinal disorders, particularly SIBO (Small Intestinal Bacterial Overgrowth). 

Please analyze and enhance the following medical document text. Your task is to:

1. **Clean and Structure**: Organize the text with proper formatting, headings, and sections
2. **Medical Context**: Identify and highlight key medical concepts, treatments, and protocols
3. **SIBO Focus**: Pay special attention to SIBO-related content including:
   - Symptoms and diagnostic criteria
   - Treatment protocols (antibiotics, herbal antimicrobials)
   - Dietary interventions (Low FODMAP, SCD, elemental diet)
   - Prokinetic agents and motility enhancers
   - Probiotics and microbiome restoration
   - Testing methods (breath tests, stool analysis)

4. **Preserve Information**: Maintain all original medical information while improving readability
5. **Add Structure**: Use clear headings, bullet points, and logical organization

Document: {filename}

Raw Text:
{raw_text}

Please provide the enhanced, well-structured medical document:
"""
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Check the health status of the Ollama OCR service.
        
        Returns:
            Health status information
        """
        try:
            async with aiohttp.ClientSession() as session:
                # Test Ollama connectivity
                async with session.get(
                    f"{self.ollama_url}/api/tags",
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    
                    if response.status == 200:
                        models = await response.json()
                        model_names = [model['name'] for model in models.get('models', [])]
                        
                        medgemma_available = any('medgemma' in name.lower() or self.model_name in model_names for name in model_names)
                        
                        return {
                            "status": "healthy" if medgemma_available else "degraded",
                            "ollama_available": True,
                            "medgemma_available": medgemma_available,
                            "available_models": model_names,
                            "model_in_use": self.model_name,
                            "provider": "Ollama + MedGemma"
                        }
                    else:
                        return {
                            "status": "error",
                            "ollama_available": False,
                            "error": f"Ollama API returned {response.status}",
                            "provider": "Ollama + MedGemma"
                        }
                        
        except Exception as e:
            logger.error(f"Error in Ollama health check: {str(e)}")
            return {
                "status": "error",
                "ollama_available": False,
                "error": str(e),
                "provider": "Ollama + MedGemma"
            }
    
    def get_supported_formats(self) -> Dict[str, Any]:
        """
        Get the list of supported file formats.
        
        Returns:
            Dictionary with supported formats and capabilities
        """
        return {
            "pdf_formats": self.supported_pdf_formats,
            "image_formats": self.supported_image_formats,
            "text_formats": [".txt", ".md"],
            "capabilities": [
                "PDF text extraction",
                "Medical text enhancement",
                "SIBO-focused analysis",
                "Local processing (no rate limits)",
                "Medical context understanding",
                "Text structuring and formatting"
            ],
            "provider": "Ollama + MedGemma",
            "model": self.model_name,
            "rate_limits": "None",
            "availability": "Always available (local)"
        }

#!/usr/bin/env python3
"""
Test Mistral OCR integration for PDF processing.
"""

import os
import sys
import asyncio
import logging
import tempfile
import base64
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "server"))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_test_pdf():
    """Create a test PDF with medical content."""
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter
        
        # Create temporary PDF
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
        temp_path = temp_file.name
        temp_file.close()
        
        # Create PDF with medical content
        c = canvas.Canvas(temp_path, pagesize=letter)
        
        # Add medical content
        c.drawString(100, 750, "MEDICAL REPORT")
        c.drawString(100, 720, "Patient: <PERSON>")
        c.drawString(100, 690, "Date: 2024-06-13")
        c.drawString(100, 660, "Diagnosis: SIBO (Small Intestinal Bacterial Overgrowth)")
        c.drawString(100, 630, "Treatment: Rifaximin 550mg twice daily for 14 days")
        c.drawString(100, 600, "Probiotics: Lactobacillus acidophilus 10 billion CFU")
        c.drawString(100, 570, "Diet: Low-FODMAP diet with reduced fermentable carbs")
        c.drawString(100, 540, "Supplements: Vitamin B12 1000mcg, Vitamin D3 2000 IU")
        c.drawString(100, 510, "Follow-up: Breath test in 4 weeks to assess response")
        c.drawString(100, 480, "Dr. Sarah Johnson, MD - Gastroenterology Department")
        
        c.save()
        logger.info(f"Created test PDF: {temp_path}")
        return temp_path
        
    except ImportError:
        logger.error("reportlab not available")
        return None
    except Exception as e:
        logger.error(f"Error creating PDF: {str(e)}")
        return None

async def test_mistral_ocr_service():
    """Test Mistral OCR service directly."""
    try:
        from graph_service.services.mistral_ocr_service import MistralOCRService
        
        logger.info("🔄 Testing Mistral OCR service...")
        
        # Initialize service
        mistral_ocr = MistralOCRService()
        
        # Check availability
        if not mistral_ocr.is_available():
            logger.error("❌ Mistral OCR service not available - check API key")
            return False
        
        # Test connection
        connection_ok = await mistral_ocr.test_connection()
        if not connection_ok:
            logger.error("❌ Mistral OCR connection test failed")
            return False
        
        logger.info("✅ Mistral OCR service available and connected")
        return True
        
    except Exception as e:
        logger.error(f"❌ Mistral OCR service test failed: {str(e)}")
        return False

async def test_mistral_pdf_extraction():
    """Test Mistral OCR PDF extraction."""
    try:
        from graph_service.services.mistral_ocr_service import MistralOCRService
        
        logger.info("🔄 Testing Mistral OCR PDF extraction...")
        
        # Create test PDF
        pdf_path = create_test_pdf()
        if not pdf_path:
            logger.error("❌ Could not create test PDF")
            return False
        
        try:
            # Read PDF content
            with open(pdf_path, 'rb') as f:
                pdf_content = f.read()
            
            # Initialize Mistral OCR
            mistral_ocr = MistralOCRService()
            
            # Convert to base64 data URL
            pdf_base64 = base64.b64encode(pdf_content).decode('utf-8')
            document_url = f"data:application/pdf;base64,{pdf_base64}"
            
            # Extract text
            extracted_text = await mistral_ocr.extract_text(document_url, "document_url")
            
            if extracted_text and len(extracted_text.strip()) > 50:
                logger.info("✅ Mistral OCR PDF extraction successful!")
                logger.info(f"📝 Extracted text ({len(extracted_text)} chars):")
                logger.info(f"'{extracted_text[:200]}...'")
                
                # Check for medical terms
                medical_terms = ['SIBO', 'Rifaximin', 'Lactobacillus', 'Vitamin B12', 'Dr. Johnson']
                found_terms = [term for term in medical_terms if term.lower() in extracted_text.lower()]
                
                logger.info(f"🔍 Found medical terms: {found_terms}")
                return True
            else:
                logger.warning(f"⚠️ Mistral OCR returned minimal text: '{extracted_text[:100] if extracted_text else 'None'}'")
                return False
                
        finally:
            # Clean up
            if os.path.exists(pdf_path):
                os.unlink(pdf_path)
                
    except Exception as e:
        logger.error(f"❌ Mistral OCR PDF extraction test failed: {str(e)}")
        return False

async def test_upload_endpoint_with_mistral():
    """Test the upload endpoint with Mistral OCR."""
    try:
        from graph_service.routers.document_upload import extract_text_only
        
        logger.info("🔄 Testing upload endpoint with Mistral OCR...")
        
        # Create test PDF
        pdf_path = create_test_pdf()
        if not pdf_path:
            logger.error("❌ Could not create test PDF")
            return False
        
        try:
            # Read PDF content
            with open(pdf_path, 'rb') as f:
                pdf_content = f.read()
            
            # Test extraction function
            extracted_text = await extract_text_only(pdf_content, "test_medical_report.pdf")
            
            if extracted_text and len(extracted_text.strip()) > 50:
                logger.info("✅ Upload endpoint with Mistral OCR successful!")
                logger.info(f"📝 Extracted text ({len(extracted_text)} chars):")
                logger.info(f"'{extracted_text[:200]}...'")
                
                # Check for medical terms
                medical_terms = ['SIBO', 'Rifaximin', 'Patient', 'Medical']
                found_terms = [term for term in medical_terms if term.lower() in extracted_text.lower()]
                
                logger.info(f"🔍 Found medical terms: {found_terms}")
                return True
            else:
                logger.warning(f"⚠️ Upload endpoint returned minimal text: '{extracted_text[:100] if extracted_text else 'None'}'")
                return False
                
        finally:
            # Clean up
            if os.path.exists(pdf_path):
                os.unlink(pdf_path)
                
    except Exception as e:
        logger.error(f"❌ Upload endpoint test failed: {str(e)}")
        return False

async def main():
    """Run Mistral OCR integration tests."""
    logger.info("🚀 Testing Mistral OCR Integration")
    logger.info("Vision-based PDF Processing")
    logger.info("=" * 50)
    
    # Test Mistral OCR service
    service_ok = await test_mistral_ocr_service()
    logger.info("")
    
    # Test Mistral PDF extraction
    pdf_ok = await test_mistral_pdf_extraction()
    logger.info("")
    
    # Test upload endpoint
    upload_ok = await test_upload_endpoint_with_mistral()
    logger.info("")
    
    # Summary
    logger.info("📋 Mistral OCR Integration Test Results:")
    logger.info(f"   Service Available: {'✅ YES' if service_ok else '❌ NO'}")
    logger.info(f"   PDF Extraction: {'✅ WORKING' if pdf_ok else '❌ FAILED'}")
    logger.info(f"   Upload Endpoint: {'✅ WORKING' if upload_ok else '❌ FAILED'}")
    
    if service_ok and (pdf_ok or upload_ok):
        logger.info("")
        logger.info("🎉 MISTRAL OCR INTEGRATION SUCCESSFUL!")
        logger.info("✅ Vision-based PDF processing ready")
        logger.info("✅ Medical document OCR enabled")
        logger.info("✅ Frontend uploads will use Mistral OCR")
        logger.info("")
        logger.info("🚀 Ready to process PDFs with Mistral Vision!")
        return True
    else:
        logger.error("❌ Mistral OCR integration failed")
        logger.info("")
        logger.info("💡 To fix:")
        logger.info("   1. Check MISTRAL_API_KEY in .env file")
        logger.info("   2. Verify Mistral API key is valid and not expired")
        logger.info("   3. Check network connectivity to Mistral API")
        return False

if __name__ == "__main__":
    asyncio.run(main())

#!/usr/bin/env python3
"""
Test MedGemma model directly with Ollama to verify memory and JSON format compliance.
"""

import requests
import json
import time

def test_medgemma_memory():
    """Test if MedGemma model works with available memory."""
    print("🧪 TESTING MEDGEMMA MEMORY COMPATIBILITY")
    print("=" * 60)
    
    base_url = "http://localhost:11434"
    model_name = "alibayram/medgemma:latest"
    
    try:
        # Simple test first
        print(f"📤 Testing basic response from {model_name}...")
        
        payload = {
            "model": model_name,
            "prompt": "Hello, respond with just 'OK' if you can understand this.",
            "stream": False,
            "options": {
                "temperature": 0.1,
                "num_predict": 10
            }
        }
        
        start_time = time.time()
        response = requests.post(f"{base_url}/api/generate", json=payload, timeout=60)
        end_time = time.time()
        
        if response.status_code == 200:
            result = response.json()
            response_text = result.get('response', '').strip()
            duration = end_time - start_time
            
            print(f"   ✅ MedGemma responded successfully!")
            print(f"   📝 Response: '{response_text}'")
            print(f"   ⏱️  Duration: {duration:.2f} seconds")
            print(f"   🧠 Load duration: {result.get('load_duration', 0) / 1e9:.2f}s")
            
            return True
        else:
            print(f"   ❌ Model request failed: {response.status_code}")
            print(f"   📄 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error testing MedGemma: {str(e)}")
        return False

def test_medgemma_json():
    """Test MedGemma JSON generation for Graphiti compliance."""
    print(f"\n📋 TESTING MEDGEMMA JSON GENERATION")
    print("=" * 60)
    
    base_url = "http://localhost:11434"
    model_name = "alibayram/medgemma:latest"
    
    try:
        # Test medical entity extraction with JSON
        test_prompt = """You are a medical AI assistant. Extract medical entities from this text and respond with ONLY valid JSON.

Text: "Patient has SIBO. Treatment: Rifaximin 550mg twice daily."

Respond with JSON in this EXACT format:
{
  "extracted_entities": [
    {"name": "SIBO", "entity_type_id": 0},
    {"name": "Rifaximin", "entity_type_id": 0}
  ]
}

CRITICAL: Respond with ONLY the JSON - NO other text before or after:"""
        
        payload = {
            "model": model_name,
            "prompt": test_prompt,
            "stream": False,
            "options": {
                "temperature": 0.1,
                "num_predict": 300
            }
        }
        
        print(f"📤 Testing JSON generation...")
        
        start_time = time.time()
        response = requests.post(f"{base_url}/api/generate", json=payload, timeout=120)
        end_time = time.time()
        
        if response.status_code == 200:
            result = response.json()
            response_text = result.get('response', '').strip()
            duration = end_time - start_time
            
            print(f"   ✅ MedGemma responded!")
            print(f"   ⏱️  Duration: {duration:.2f} seconds")
            print(f"   📝 Raw response:")
            print(f"   {response_text}")
            print()
            
            # Try to parse as JSON
            try:
                # Clean the response (remove any extra text)
                json_start = response_text.find('{')
                json_end = response_text.rfind('}') + 1
                
                if json_start >= 0 and json_end > json_start:
                    json_text = response_text[json_start:json_end]
                    parsed_json = json.loads(json_text)
                    
                    print(f"   ✅ Valid JSON extracted!")
                    print(f"   📊 Parsed: {json.dumps(parsed_json, indent=2)}")
                    
                    # Check Graphiti compliance
                    if 'extracted_entities' in parsed_json:
                        entities = parsed_json['extracted_entities']
                        print(f"   🎯 Graphiti format detected!")
                        print(f"   📦 Found {len(entities)} entities")
                        
                        # Validate entity structure
                        valid_entities = True
                        for entity in entities:
                            if not isinstance(entity, dict) or 'name' not in entity or 'entity_type_id' not in entity:
                                valid_entities = False
                                break
                        
                        if valid_entities:
                            print(f"   ✅ All entities have correct structure!")
                            return True
                        else:
                            print(f"   ⚠️  Some entities missing required fields")
                            return False
                    else:
                        print(f"   ⚠️  JSON valid but not Graphiti format")
                        return False
                else:
                    print(f"   ❌ No valid JSON found in response")
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"   ❌ Invalid JSON: {str(e)}")
                return False
                
        else:
            print(f"   ❌ Request failed: {response.status_code}")
            print(f"   📄 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error testing JSON generation: {str(e)}")
        return False

def test_medgemma_with_our_client():
    """Test MedGemma using our Ollama LLM client logic."""
    print(f"\n🔧 TESTING WITH OUR CLIENT LOGIC")
    print("=" * 60)
    
    try:
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), 'server'))
        
        from graph_service.services.ollama_llm_client import OllamaLLMClient
        from graph_service.core.llm_client import LLMConfig
        
        # Create client with MedGemma
        config = LLMConfig(
            model='alibayram/medgemma:latest',
            temperature=0.1,
            max_tokens=300
        )
        
        client = OllamaLLMClient(config)
        print(f"   ✅ Ollama client created with model: {client.model_name}")
        
        # Test simple generation
        test_prompt = "Extract medical entities from: 'Patient has SIBO. Treatment: Rifaximin.'"
        
        print(f"   📤 Testing client generation...")
        
        # This would normally use response_model, but let's test without it first
        response = client.generate_response(
            messages=[{"role": "user", "content": test_prompt}],
            response_model=None,
            max_tokens=300
        )
        
        print(f"   ✅ Client response received!")
        print(f"   📝 Response: {response}")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ Could not import client: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Client test failed: {str(e)}")
        return False

def main():
    """Main test function."""
    print("🔧 MEDGEMMA DIRECT TESTING")
    print("🎯 Goal: Verify MedGemma works with available memory and produces correct JSON")
    print("💾 Available memory should be sufficient for MedGemma (2.4GB vs 4.7GB for llama3.1:8b)")
    print()
    
    # Test 1: Basic memory compatibility
    memory_ok = test_medgemma_memory()
    
    if not memory_ok:
        print(f"\n❌ MedGemma memory test failed")
        print(f"💡 Check if Ollama service is running: 'ollama serve'")
        return False
    
    # Test 2: JSON generation
    json_ok = test_medgemma_json()
    
    # Test 3: Our client logic
    client_ok = test_medgemma_with_our_client()
    
    print(f"\n🎉 MEDGEMMA TEST RESULTS:")
    print(f"✅ Memory compatibility: {'PASS' if memory_ok else 'FAIL'}")
    print(f"{'✅' if json_ok else '❌'} JSON generation: {'PASS' if json_ok else 'FAIL'}")
    print(f"{'✅' if client_ok else '❌'} Client integration: {'PASS' if client_ok else 'FAIL'}")
    
    if memory_ok and json_ok:
        print(f"\n🎯 CONCLUSION:")
        print(f"✅ MedGemma model works with available memory!")
        print(f"✅ Can generate Graphiti-compatible JSON!")
        print(f"🔧 The issue was using llama3.1:8b which needs too much memory")
        print(f"🚀 Ready to test full Graphiti integration with MedGemma!")
        return True
    else:
        print(f"\n❌ Some tests failed - check Ollama setup")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n✅ MedGemma is ready for Graphiti integration!")
    else:
        print(f"\n❌ Fix MedGemma issues before proceeding")

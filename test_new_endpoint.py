#!/usr/bin/env python3
"""
Test the new status endpoint
"""

import requests
import time
import json

def test_new_endpoint():
    """Test the new status endpoint"""
    
    print("🔍 Testing New Status Endpoint...")
    
    try:
        # Test with a known filename
        print("\n📊 Testing new status endpoint...")
        status_response = requests.get(
            "http://localhost:8234/api/processing/status-test/async_fix_test.txt?group_id=default",
            timeout=10
        )
        
        print(f"Status Code: {status_response.status_code}")
        print(f"Response: {status_response.text}")
        
        if status_response.status_code == 200:
            status_data = status_response.json()
            print("✅ New status endpoint working!")
            print(f"📊 Status: {json.dumps(status_data, indent=2)}")
        else:
            print(f"❌ New status endpoint failed: {status_response.status_code}")
            
    except Exception as e:
        print(f"❌ Test error: {e}")

if __name__ == "__main__":
    test_new_endpoint()

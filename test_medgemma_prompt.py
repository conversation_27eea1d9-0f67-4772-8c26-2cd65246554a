#!/usr/bin/env python3
"""
Test the exact prompt being sent to MedGemma to debug the empty response issue.
"""

import requests
import json
import sys
import os

# Add server path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'server'))

def test_medgemma_with_exact_prompt():
    """Test MedGemma with the exact prompt format used by our client."""
    print("🔧 TESTING MEDGEMMA WITH EXACT CLIENT PROMPT")
    print("=" * 60)
    
    # Simulate the exact prompt that would be sent
    text_content = "Test: SIBO \n\nPatient has SIBO.\nTreatment: Rifaximin."
    
    # This is similar to what <PERSON><PERSON><PERSON><PERSON> would send
    system_message = """You are a medical AI assistant specialized in extracting medical entities from clinical text.

Extract medical entities from the provided text and return them in the specified JSON format.

Focus on:
- Medical conditions (diseases, symptoms, syndromes)
- Medications and treatments
- Medical procedures
- Anatomical terms
- Laboratory values and measurements

CRITICAL: You MUST respond with ONLY valid JSON matching <PERSON><PERSON><PERSON><PERSON>'s ExtractedEntities schema.

Required JSON Schema:
{
  "type": "object",
  "properties": {
    "extracted_entities": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "name": {
            "type": "string"
          },
          "entity_type_id": {
            "type": "integer"
          }
        },
        "required": ["name", "entity_type_id"]
      }
    }
  },
  "required": ["extracted_entities"]
}

EXACT Example Response (copy this format exactly):
{
  "extracted_entities": [
    {"name": "SIBO", "entity_type_id": 0},
    {"name": "Rifaximin", "entity_type_id": 0},
    {"name": "Vitamin D", "entity_type_id": 0}
  ]
}

MANDATORY RULES:
1. Response must be COMPLETE, VALID JSON only - NO other text
2. NO explanatory text before or after JSON
3. NO truncated responses - complete the entire structure
4. Use EXACT field names: ['extracted_entities']
5. For ExtractedEntities: each entity MUST have "name" (string) and "entity_type_id" (integer, use 0 for default)
6. Start response with { and end with }
7. Ensure all brackets and braces are properly closed

RESPOND WITH ONLY THE JSON - NO OTHER TEXT:"""

    user_message = f"Extract medical entities from this text:\n\n{text_content}"
    
    # Convert to single prompt like our client does
    prompt = f"System: {system_message}\n\nUser: {user_message}"
    
    print(f"📝 Prompt length: {len(prompt)} characters")
    print(f"📝 First 200 chars: {prompt[:200]}...")
    print(f"📝 Last 200 chars: ...{prompt[-200:]}")
    print()
    
    # Test with the exact payload our client would send
    base_url = "http://localhost:11434"
    model_name = "alibayram/medgemma:latest"
    
    payload = {
        "model": model_name,
        "prompt": prompt,
        "stream": False,
        "options": {
            "temperature": 0.1,
            "top_p": 0.9,
            "num_predict": 500,
            "stop": ["```", "---", "END"],  # Same stop tokens as our client
            "repeat_penalty": 1.1
        }
    }
    
    print(f"📤 Testing with exact client payload...")
    print(f"   Model: {model_name}")
    print(f"   Temperature: {payload['options']['temperature']}")
    print(f"   Max tokens: {payload['options']['num_predict']}")
    print(f"   Stop tokens: {payload['options']['stop']}")
    print()
    
    try:
        response = requests.post(f"{base_url}/api/generate", json=payload, timeout=180)
        
        if response.status_code == 200:
            result = response.json()
            response_text = result.get('response', '').strip()
            
            print(f"✅ Response received!")
            print(f"📊 Response length: {len(response_text)} characters")
            print(f"📝 Raw response:")
            print(f"'{response_text}'")
            print()
            
            if response_text:
                # Try to parse as JSON
                try:
                    json_start = response_text.find('{')
                    json_end = response_text.rfind('}') + 1
                    
                    if json_start >= 0 and json_end > json_start:
                        json_text = response_text[json_start:json_end]
                        parsed_json = json.loads(json_text)
                        
                        print(f"✅ Valid JSON extracted!")
                        print(f"📊 Parsed: {json.dumps(parsed_json, indent=2)}")
                        return True
                    else:
                        print(f"❌ No valid JSON structure found")
                        return False
                        
                except json.JSONDecodeError as e:
                    print(f"❌ Invalid JSON: {str(e)}")
                    return False
            else:
                print(f"❌ Empty response - this is the issue!")
                print(f"📊 Full result: {result}")
                return False
                
        else:
            print(f"❌ Request failed: {response.status_code}")
            print(f"📄 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def test_medgemma_without_stop_tokens():
    """Test MedGemma without stop tokens to see if they're causing the issue."""
    print(f"\n🔧 TESTING MEDGEMMA WITHOUT STOP TOKENS")
    print("=" * 60)
    
    text_content = "Test: SIBO \n\nPatient has SIBO.\nTreatment: Rifaximin."
    
    # Simplified prompt
    prompt = f"""Extract medical entities from this text and respond with JSON:

Text: {text_content}

Respond with JSON in this format:
{{
  "extracted_entities": [
    {{"name": "SIBO", "entity_type_id": 0}},
    {{"name": "Rifaximin", "entity_type_id": 0}}
  ]
}}

JSON only:"""
    
    base_url = "http://localhost:11434"
    model_name = "alibayram/medgemma:latest"
    
    payload = {
        "model": model_name,
        "prompt": prompt,
        "stream": False,
        "options": {
            "temperature": 0.1,
            "num_predict": 300
            # NO stop tokens
        }
    }
    
    print(f"📤 Testing without stop tokens...")
    
    try:
        response = requests.post(f"{base_url}/api/generate", json=payload, timeout=180)
        
        if response.status_code == 200:
            result = response.json()
            response_text = result.get('response', '').strip()
            
            print(f"✅ Response received!")
            print(f"📊 Response length: {len(response_text)} characters")
            print(f"📝 Raw response:")
            print(f"'{response_text}'")
            
            if response_text:
                return True
            else:
                print(f"❌ Still empty response")
                return False
                
        else:
            print(f"❌ Request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def main():
    """Main test function."""
    print("🔧 MEDGEMMA PROMPT DEBUGGING")
    print("🎯 Goal: Find why MedGemma returns empty responses in our client")
    print()
    
    # Test 1: Exact client prompt
    exact_ok = test_medgemma_with_exact_prompt()
    
    # Test 2: Without stop tokens
    no_stop_ok = test_medgemma_without_stop_tokens()
    
    print(f"\n🎉 DEBUGGING RESULTS:")
    print(f"{'✅' if exact_ok else '❌'} Exact client prompt: {'PASS' if exact_ok else 'FAIL'}")
    print(f"{'✅' if no_stop_ok else '❌'} Without stop tokens: {'PASS' if no_stop_ok else 'FAIL'}")
    
    if not exact_ok and no_stop_ok:
        print(f"\n🎯 CONCLUSION: Stop tokens are causing the issue!")
        print(f"💡 Solution: Remove or modify stop tokens for MedGemma")
    elif not exact_ok and not no_stop_ok:
        print(f"\n🎯 CONCLUSION: Issue is with prompt format or model state")
        print(f"💡 Solution: Simplify prompt or check model availability")
    elif exact_ok:
        print(f"\n🎯 CONCLUSION: Client prompt works fine!")
        print(f"💡 Issue might be elsewhere in the pipeline")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Complete test for document upload processing with all fixes applied.

This test verifies:
1. OpenRouter embeddings issue is resolved (using Ollama embeddings)
2. Ollama API connection is working
3. MedGemma entity extraction is working
4. JSON parsing fixes are applied
5. Full end-to-end document processing
"""

import requests
import tempfile
import os
import time
import json

def create_comprehensive_test_pdf():
    """Create a comprehensive test PDF with rich medical content."""
    try:
        from reportlab.pdfgen import canvas
        
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
        temp_path = temp_file.name
        temp_file.close()
        
        c = canvas.Canvas(temp_path)
        c.drawString(100, 750, "COMPREHENSIVE MEDICAL REPORT - COMPLETE SYSTEM TEST")
        c.drawString(100, 720, "Patient: Dr. <PERSON>")
        c.drawString(100, 690, "Date: 2024-06-16")
        c.drawString(100, 660, "Primary Diagnosis: Inflammatory Bowel Disease (IBD)")
        c.drawString(100, 630, "Secondary: SIBO, Lactose Intolerance, Iron Deficiency Anemia")
        c.drawString(100, 600, "Medications:")
        c.drawString(120, 580, "- Mesalamine 800mg three times daily")
        c.drawString(120, 560, "- Rifaximin 550mg twice daily for 14 days")
        c.drawString(120, 540, "- Iron sulfate 325mg once daily")
        c.drawString(100, 510, "Supplements:")
        c.drawString(120, 490, "- Lactobacillus acidophilus 50 billion CFU")
        c.drawString(120, 470, "- Vitamin B12 1000mcg sublingual")
        c.drawString(120, 450, "- Vitamin D3 4000 IU daily")
        c.drawString(120, 430, "- Omega-3 fatty acids 2000mg")
        c.drawString(100, 400, "Laboratory Results:")
        c.drawString(120, 380, "- Hemoglobin: 9.2 g/dL (Low)")
        c.drawString(120, 360, "- Ferritin: 8 ng/mL (Low)")
        c.drawString(120, 340, "- CRP: 12.5 mg/L (Elevated)")
        c.drawString(120, 320, "- Calprotectin: 450 μg/g (Elevated)")
        c.drawString(100, 290, "Follow-up Plan:")
        c.drawString(120, 270, "- Hydrogen breath test in 4 weeks")
        c.drawString(120, 250, "- Complete blood count in 6 weeks")
        c.drawString(120, 230, "- Colonoscopy in 3 months")
        c.drawString(100, 200, "Provider: Dr. Sarah Johnson, MD")
        c.drawString(100, 180, "Department: Gastroenterology and Hepatology")
        c.drawString(100, 160, "Institution: University Medical Center")
        c.save()
        
        return temp_path
    except Exception as e:
        print(f"Error creating PDF: {e}")
        return None

def test_complete_system():
    """Test the complete document upload system with all fixes."""
    print("🚀 COMPREHENSIVE SYSTEM TEST - ALL FIXES APPLIED")
    print("=" * 80)
    
    pdf_path = create_comprehensive_test_pdf()
    if not pdf_path:
        return False
    
    try:
        print("📤 Uploading comprehensive medical document...")
        with open(pdf_path, 'rb') as f:
            files = {'file': ('complete_system_test.pdf', f, 'application/pdf')}
            data = {
                'group_id': 'medical_docs',
                'upload_type': 'messages'
            }
            
            response = requests.post(
                'http://127.0.0.1:8234/api/documents/upload',
                files=files,
                data=data,
                timeout=30
            )
            
            print(f"Status: {response.status_code}")
            if response.status_code == 202:
                result = response.json()
                print("✅ Upload successful!")
                print(f"Response: {result}")
                
                print("\n🔧 TESTING ALL SYSTEM COMPONENTS:")
                print("  ✅ Document Upload API")
                print("  🔄 Mistral OCR Processing")
                print("  🔄 Ollama MedGemma Entity Extraction")
                print("  🔄 Ollama Snowflake Embeddings")
                print("  🔄 JSON Parsing Fixes")
                print("  🔄 Graphiti Episode Creation")
                print("")
                
                print("Expected entities to extract:")
                print("  - Dr. Alexandra Rodriguez, Dr. Sarah Johnson")
                print("  - IBD, SIBO, Lactose Intolerance, Iron Deficiency Anemia")
                print("  - Mesalamine, Rifaximin, Iron sulfate")
                print("  - 800mg, 550mg, 325mg, 1000mcg, 4000 IU")
                print("  - Lactobacillus acidophilus, Vitamin B12, Vitamin D3")
                print("  - Hemoglobin, Ferritin, CRP, Calprotectin")
                print("  - Hydrogen breath test, Colonoscopy")
                print("  - Gastroenterology, University Medical Center")
                print("")
                
                # Wait for processing with detailed progress
                for i in range(60):
                    print(f"⏳ Processing... {i+1}/60 seconds", end='\r')
                    time.sleep(1)
                    
                    # Check status every 10 seconds
                    if i % 10 == 9:
                        try:
                            status_response = requests.get(
                                f'http://127.0.0.1:8234/api/processing/detailed-status/complete_system_test.pdf?group_id=medical_docs',
                                timeout=5
                            )
                            if status_response.status_code == 200:
                                status = status_response.json()
                                processing_status = status.get('processing_status')
                                if processing_status == 'completed':
                                    print(f"\n🎉 Processing completed early at {i+1} seconds!")
                                    break
                                elif processing_status == 'failed':
                                    print(f"\n❌ Processing failed at {i+1} seconds!")
                                    break
                        except:
                            pass
                
                print("\n\n🔍 FINAL SYSTEM TEST RESULTS:")
                print("=" * 60)
                
                status_response = requests.get(
                    f'http://127.0.0.1:8234/api/processing/detailed-status/complete_system_test.pdf?group_id=medical_docs',
                    timeout=10
                )
                
                if status_response.status_code == 200:
                    status = status_response.json()
                    
                    processing_status = status.get('processing_status')
                    text_length = status.get('text_length', 0)
                    entities_count = status.get('entities_count', 0)
                    episodes_count = status.get('episodes_count', 0)
                    ocr_status = status.get('ocr_status')
                    entity_extraction_status = status.get('entity_extraction_status')
                    
                    print(f"📋 Processing Status: {processing_status}")
                    print(f"📝 Text Extracted: {text_length} characters")
                    print(f"📚 Episodes Created: {episodes_count}")
                    print(f"🏷️  Entities Extracted: {entities_count}")
                    print(f"👁️  OCR Status: {ocr_status}")
                    print(f"🧠 Entity Extraction: {entity_extraction_status}")
                    
                    # Evaluate results
                    success_criteria = {
                        'processing_completed': processing_status == 'completed',
                        'text_extracted': text_length > 500,  # Should have substantial text
                        'entities_found': entities_count > 10,  # Should find many medical entities
                        'episodes_created': episodes_count > 0,
                        'ocr_successful': ocr_status == 'completed',
                        'entity_extraction_successful': entity_extraction_status == 'completed'
                    }
                    
                    print("\n🎯 SUCCESS CRITERIA EVALUATION:")
                    print("-" * 40)
                    all_passed = True
                    for criterion, passed in success_criteria.items():
                        status_icon = "✅" if passed else "❌"
                        print(f"  {status_icon} {criterion.replace('_', ' ').title()}: {passed}")
                        if not passed:
                            all_passed = False
                    
                    if all_passed:
                        print(f"\n🎉 COMPLETE SYSTEM TEST: SUCCESS!")
                        print("🔧 All fixes have been successfully applied:")
                        print("  ✅ OpenRouter embeddings issue resolved")
                        print("  ✅ Ollama embeddings working")
                        print("  ✅ MedGemma entity extraction working")
                        print("  ✅ JSON parsing fixes applied")
                        print("  ✅ Full document processing pipeline working")
                        return True
                    else:
                        print(f"\n⚠️ SYSTEM TEST: PARTIAL SUCCESS")
                        print("Some components are working, but issues remain.")
                        return False
                        
                else:
                    print(f"❌ Status check failed: {status_response.status_code}")
                    return False
                
            else:
                print(f"❌ Upload failed: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        if os.path.exists(pdf_path):
            os.unlink(pdf_path)

if __name__ == "__main__":
    print("🎯 COMPLETE SYSTEM TEST - DOCUMENT UPLOAD PROCESSING")
    print("Testing all components with comprehensive medical document")
    print("=" * 80)
    
    success = test_complete_system()
    
    if success:
        print("\n🎉 SYSTEM FULLY OPERATIONAL!")
        print("Document upload processing is working correctly with all fixes applied!")
    else:
        print("\n🔧 Check server logs for detailed processing information")
        print("Some components may need additional debugging")

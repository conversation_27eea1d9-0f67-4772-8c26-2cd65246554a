#!/usr/bin/env python3
"""
Test MedGemma OCR service for medical document vision processing.
"""

import os
import sys
import asyncio
import logging
import base64
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_medical_test_image():
    """Create a test image with medical content."""
    try:
        from PIL import Image, ImageDraw, ImageFont
        import io
        
        # Create a medical document image
        img = Image.new('RGB', (600, 400), color='white')
        draw = ImageDraw.Draw(img)
        
        # Add medical content
        medical_text = [
            "MEDICAL REPORT",
            "",
            "Patient: John Doe",
            "Date: 2024-06-13",
            "",
            "Diagnosis: SIBO (Small Intestinal Bacterial Overgrowth)",
            "",
            "Treatment Plan:",
            "- Rifaximin 550mg twice daily for 14 days",
            "- Probiotics: Lactobacillus acidophilus",
            "- Low-FODMAP diet",
            "",
            "Follow-up: Breath test in 4 weeks",
            "",
            "Dr. Smith, MD"
        ]
        
        try:
            font = ImageFont.load_default()
        except:
            font = None
        
        y_position = 20
        for line in medical_text:
            draw.text((20, y_position), line, fill='black', font=font)
            y_position += 25
        
        # Convert to bytes
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        return buffer.getvalue()
        
    except ImportError:
        logger.warning("PIL not available for test image creation")
        return None
    except Exception as e:
        logger.error(f"Error creating test image: {str(e)}")
        return None

async def test_google_gemini_vision():
    """Test Google Gemini Vision API directly."""
    try:
        import google.generativeai as genai
        from PIL import Image
        import io
        
        api_key = os.getenv('GOOGLE_API_KEY')
        if not api_key:
            logger.error("❌ GOOGLE_API_KEY not found")
            return False
        
        logger.info("🔄 Testing Google Gemini Vision API...")
        
        # Configure API
        genai.configure(api_key=api_key)
        
        # Create test image
        test_image_data = create_medical_test_image()
        if not test_image_data:
            logger.warning("⚠️ Could not create test image")
            return False
        
        # Convert to PIL Image
        image = Image.open(io.BytesIO(test_image_data))
        
        # Use Gemini Pro Vision
        model = genai.GenerativeModel('gemini-pro-vision')
        
        prompt = """
You are a medical document OCR specialist. Extract all text from this medical document image.

Focus on accurate extraction of:
- Medical terminology and drug names
- Dosages, measurements, and lab values
- Patient symptoms and conditions
- Maintain formatting and structure

Extract all visible text:
"""
        
        # Generate response
        response = model.generate_content([prompt, image])
        
        if response.text:
            logger.info("✅ Google Gemini Vision API successful!")
            logger.info(f"📝 Extracted text ({len(response.text)} chars):")
            logger.info(f"'{response.text[:200]}...'")
            return True
        else:
            logger.warning("⚠️ Google API returned empty response")
            return False
            
    except ImportError:
        logger.error("❌ google-generativeai not installed")
        return False
    except Exception as e:
        logger.error(f"❌ Google Gemini Vision test failed: {str(e)}")
        return False

async def test_medgemma_ocr_service():
    """Test the MedGemma OCR service."""
    try:
        from server.graph_service.services.medgemma_ocr_service import MedGemmaOCRService
        
        logger.info("🔄 Testing MedGemma OCR service...")
        
        # Initialize service
        service = MedGemmaOCRService()
        
        # Test health check
        health = await service.health_check()
        logger.info(f"   Service status: {health.get('status')}")
        logger.info(f"   Google API available: {health.get('google_api_available')}")
        logger.info(f"   Ollama available: {health.get('ollama_available')}")
        
        # Test connection
        connection_ok = await service.test_connection()
        if not connection_ok:
            logger.warning("⚠️ MedGemma service connection failed")
            return False
        
        # Test OCR extraction
        test_image_data = create_medical_test_image()
        if not test_image_data:
            logger.warning("⚠️ Could not create test image for OCR")
            return False
        
        logger.info("🔄 Testing OCR extraction...")
        extracted_text = await service.extract_text_from_image(test_image_data, "test_medical_document.png")
        
        if extracted_text:
            logger.info("✅ MedGemma OCR extraction successful!")
            logger.info(f"📝 Extracted text ({len(extracted_text)} chars):")
            logger.info(f"'{extracted_text[:200]}...'")
            return True
        else:
            logger.warning("⚠️ MedGemma OCR extraction failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ MedGemma OCR service test failed: {str(e)}")
        return False

async def test_ollama_status():
    """Check Ollama status and available models."""
    try:
        import aiohttp
        
        ollama_url = os.getenv('OLLAMA_API_URL', 'http://host.docker.internal:11434')
        
        logger.info(f"🔄 Checking Ollama status at {ollama_url}")
        
        async with aiohttp.ClientSession() as session:
            try:
                async with session.get(f"{ollama_url}/api/tags") as response:
                    if response.status == 200:
                        models = await response.json()
                        model_names = [model['name'] for model in models.get('models', [])]
                        
                        logger.info(f"✅ Ollama is running with {len(model_names)} models")
                        
                        # Check for MedGemma
                        medgemma_models = [name for name in model_names if 'medgemma' in name.lower()]
                        if medgemma_models:
                            logger.info(f"✅ MedGemma models found: {medgemma_models}")
                            return True
                        else:
                            logger.warning("⚠️ MedGemma model not found")
                            logger.info(f"Available models: {model_names}")
                            return False
                    else:
                        logger.error(f"❌ Ollama API error: {response.status}")
                        return False
            except Exception as e:
                logger.error(f"❌ Cannot connect to Ollama: {str(e)}")
                return False
                
    except ImportError:
        logger.error("❌ aiohttp not available")
        return False

async def main():
    """Run MedGemma OCR tests."""
    logger.info("🚀 MedGemma OCR Testing")
    logger.info("Medical Document Vision Processing")
    logger.info("=" * 50)
    
    # Test Ollama status
    ollama_ok = await test_ollama_status()
    logger.info("")
    
    # Test Google Gemini Vision
    google_ok = await test_google_gemini_vision()
    logger.info("")
    
    # Test MedGemma OCR service
    service_ok = await test_medgemma_ocr_service()
    logger.info("")
    
    # Summary
    logger.info("📋 MedGemma OCR Test Results:")
    logger.info(f"   Ollama MedGemma: {'✅ Available' if ollama_ok else '❌ Unavailable'}")
    logger.info(f"   Google Gemini Vision: {'✅ Working' if google_ok else '❌ Failed'}")
    logger.info(f"   MedGemma OCR Service: {'✅ Working' if service_ok else '❌ Failed'}")
    
    if google_ok or service_ok:
        logger.info("")
        logger.info("🎉 MedGemma Vision OCR is operational!")
        logger.info("✅ Medical document processing ready")
        logger.info("✅ Vision-based text extraction enabled")
        
        if google_ok:
            logger.info("🔧 Primary: Google Gemini Vision API")
        if ollama_ok:
            logger.info("🔧 Fallback: Ollama MedGemma")
        
        return True
    else:
        logger.warning("⚠️ MedGemma OCR needs attention")
        logger.info("")
        logger.info("💡 To fix:")
        logger.info("   1. Verify Google API key is valid")
        logger.info("   2. Install: pip install google-generativeai")
        logger.info("   3. For Ollama: ollama pull alibayram/medgemma:latest")
        return False

if __name__ == "__main__":
    asyncio.run(main())

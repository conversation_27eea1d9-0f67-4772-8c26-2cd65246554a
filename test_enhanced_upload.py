#!/usr/bin/env python3
"""
Test document upload to verify enhanced UI feedback
"""

import requests
import time
import json

def test_upload():
    """Test uploading the enhanced test document"""
    
    # Wait for service to be ready
    print("🔄 Waiting for service to be ready...")
    for i in range(10):
        try:
            response = requests.get("http://localhost:8234/healthcheck", timeout=5)
            if response.status_code == 200:
                print("✅ Service is ready!")
                break
        except:
            print(f"⏳ Waiting... ({i+1}/10)")
            time.sleep(2)
    else:
        print("❌ Service not ready after 20 seconds")
        return
    
    # Upload the test document
    print("\n📤 Uploading test document...")
    
    try:
        with open('test_document_enhanced.txt', 'r') as f:
            file_content = f.read()
        
        files = {
            'file': ('test_document_enhanced.txt', file_content, 'text/plain')
        }
        
        data = {
            'group_id': 'default',
            'upload_type': 'document'
        }
        
        response = requests.post(
            "http://localhost:8234/api/documents/upload",
            files=files,
            data=data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Upload successful!")
            print(f"📄 Response: {json.dumps(result, indent=2)}")
            
            # Test the detailed status endpoint
            print("\n🔍 Testing detailed status endpoint...")
            time.sleep(2)
            
            status_response = requests.get(
                "http://localhost:8234/api/processing/detailed-status/test_document_enhanced.txt?group_id=default",
                timeout=10
            )
            
            if status_response.status_code == 200:
                status_data = status_response.json()
                print("✅ Status endpoint working!")
                print(f"📊 Status: {json.dumps(status_data, indent=2)}")
            else:
                print(f"⚠️ Status endpoint returned: {status_response.status_code}")
                print(f"Response: {status_response.text}")
                
        else:
            print(f"❌ Upload failed with status: {response.status_code}")
            print(f"Response: {response.text}")
            
    except FileNotFoundError:
        print("❌ Test document not found. Please upload through the web UI instead.")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_upload()

#!/usr/bin/env python3
"""
Test: PDF Upload Fix Verification
Test that PDF uploads now complete successfully with the fixed OCR configuration
"""

import requests
import time
import json

def test_service_status():
    """Test the service status to verify OCR configuration"""
    
    print("🔍 TESTING SERVICE STATUS WITH FIXED OCR CONFIGURATION")
    print("=" * 60)
    
    try:
        # Wait for service to start
        time.sleep(15)
        
        response = requests.get("http://localhost:8234/api/processing/service-status", timeout=15)
        
        if response.status_code == 200:
            status_data = response.json()
            print("✅ Service status retrieved!")
            print(f"📊 OCR Service:")
            print(f"  - Available: {status_data.get('ocr_service', {}).get('available', False)}")
            print(f"  - Provider: {status_data.get('ocr_service', {}).get('provider', 'Unknown')}")
            print(f"  - Configuration: {status_data.get('ocr_service', {}).get('configuration', 'Unknown')}")
            
            print(f"📊 Entity Extraction:")
            print(f"  - Available: {status_data.get('entity_extraction', {}).get('available', False)}")
            print(f"  - Provider: {status_data.get('entity_extraction', {}).get('provider', 'Unknown')}")
            
            print(f"📊 Reference Extraction:")
            print(f"  - Available: {status_data.get('reference_extraction', {}).get('available', False)}")
            print(f"  - Provider: {status_data.get('reference_extraction', {}).get('provider', 'Unknown')}")
            
            print(f"📊 Pipeline Status: {status_data.get('pipeline_status', 'Unknown')}")
            
            # Check if OCR is properly configured
            ocr_config = status_data.get('ocr_service', {}).get('configuration', '')
            if 'mistral' in ocr_config.lower():
                print("✅ OCR Configuration: Mistral OCR with fallbacks (CORRECT)")
                return True
            elif 'ollama' in ocr_config.lower():
                print("⚠️ OCR Configuration: Still using Ollama (needs Ollama running)")
                return False
            else:
                print("❌ OCR Configuration: Unknown configuration")
                return False
                
        else:
            print(f"❌ Service status failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Service status error: {e}")
        return False

def test_pdf_upload():
    """Test PDF upload with the fixed configuration"""
    
    print("\n📤 TESTING PDF UPLOAD WITH FIXED OCR")
    print("=" * 50)
    
    try:
        # Create a simple PDF-like content (we'll simulate a PDF)
        # In reality, this would be actual PDF bytes, but for testing we'll use text
        pdf_content = """
        Medical Research Document
        
        This is a test document to verify that PDF processing is working correctly
        with the new Mistral OCR configuration.
        
        Key Medical Terms:
        - Hypertension
        - Diabetes mellitus
        - Cardiovascular disease
        - Pharmacotherapy
        - Clinical trials
        
        Sample Reference:
        1. Smith, J. et al. (2024). "Advances in Medical AI". Journal of Medicine, 45(2), 123-145.
        
        This document should be processed successfully through the complete pipeline:
        1. OCR text extraction (Mistral OCR)
        2. Entity extraction (OpenRouter)
        3. Reference extraction (OpenRouter)
        4. Knowledge graph storage (Neo4j)
        """
        
        files = {
            'file': ('test_medical_document.pdf', pdf_content.encode('utf-8'), 'application/pdf')
        }
        
        data = {
            'group_id': 'default',
            'upload_type': 'messages'
        }
        
        print("📤 Uploading test PDF document...")
        upload_response = requests.post(
            "http://localhost:8234/api/documents/upload",
            files=files,
            data=data,
            timeout=30
        )
        
        print(f"Upload Status: {upload_response.status_code}")
        
        if upload_response.status_code == 202:
            print("✅ Upload successful!")
            
            # Monitor processing with detailed logging
            print("\n⏳ Monitoring PDF processing pipeline...")
            
            for i in range(12):  # Monitor for 3 minutes
                time.sleep(15)
                
                try:
                    status_response = requests.get(
                        "http://localhost:8234/api/processing/detailed-status/test_medical_document.pdf?group_id=default",
                        timeout=15
                    )
                    
                    if status_response.status_code == 200:
                        status_data = status_response.json()
                        processing_status = status_data.get('processing_status', 'unknown')
                        
                        print(f"Check {i+1}: Processing Status: {processing_status}")
                        
                        # Show detailed progress
                        if 'processing_steps' in status_data:
                            steps = status_data['processing_steps']
                            print(f"  - OCR: {'✅' if steps.get('ocr_completed') else '⏳'}")
                            print(f"  - Entities: {'✅' if steps.get('entity_extraction_completed') else '⏳'}")
                            print(f"  - References: {'✅' if steps.get('reference_extraction_completed') else '⏳'}")
                            print(f"  - CSV Export: {'✅' if steps.get('csv_export_completed') else '⏳'}")
                        
                        if processing_status == 'completed':
                            print("🎉 PDF PROCESSING SUCCESS!")
                            print(f"📊 Final Results:")
                            print(f"  - Text Length: {status_data.get('text_length', 0)} characters")
                            print(f"  - Entities: {status_data.get('entities_count', 0)}")
                            print(f"  - References: {status_data.get('references_count', 0)}")
                            print(f"  - Episodes: {status_data.get('episodes_count', 0)}")
                            
                            # Analyze results
                            text_length = status_data.get('text_length', 0)
                            entities_count = status_data.get('entities_count', 0)
                            references_count = status_data.get('references_count', 0)
                            episodes_count = status_data.get('episodes_count', 0)
                            
                            print(f"\n🎯 PDF PROCESSING ANALYSIS:")
                            
                            if text_length > 0:
                                print("✅ OCR Processing: SUCCESS")
                                print(f"  - Extracted {text_length} characters")
                                if text_length >= 500:
                                    print("  - Excellent text extraction!")
                                else:
                                    print("  - Good text extraction")
                            else:
                                print("❌ OCR Processing: FAILED")
                            
                            if episodes_count > 0:
                                print("✅ Knowledge Graph Storage: SUCCESS")
                                print(f"  - Created {episodes_count} episode(s)")
                            else:
                                print("❌ Knowledge Graph Storage: FAILED")
                            
                            if entities_count > 0:
                                print("✅ Entity Extraction: SUCCESS")
                                print(f"  - Extracted {entities_count} entities")
                            else:
                                print("⚠️ Entity Extraction: No entities extracted")
                            
                            if references_count > 0:
                                print("✅ Reference Extraction: SUCCESS")
                                print(f"  - Extracted {references_count} references")
                            else:
                                print("⚠️ Reference Extraction: No references extracted")
                            
                            # Overall success assessment
                            core_success = text_length > 0 and episodes_count > 0
                            
                            if core_success:
                                print(f"\n🏆 PDF UPLOAD FIX: SUCCESS!")
                                print("✅ Core PDF processing pipeline is working!")
                                return True
                            else:
                                print(f"\n❌ PDF UPLOAD FIX: PARTIAL")
                                print("❌ Core PDF processing still has issues")
                                return False
                                
                        elif processing_status == 'failed':
                            error_msg = status_data.get('error_message', 'Unknown error')
                            print(f"❌ Processing failed: {error_msg}")
                            
                            # Check if it's still an OCR issue
                            if "ollama" in error_msg.lower() or "11434" in error_msg:
                                print("❌ Still hitting Ollama connection issues")
                                print("❌ OCR configuration not properly applied")
                            elif "mistral" in error_msg.lower():
                                print("⚠️ Mistral OCR issue - may need API key check")
                            
                            return False
                            
                        elif processing_status in ['processing', 'pending']:
                            print(f"⏳ Still processing... (attempt {i+1}/12)")
                        else:
                            print(f"❓ Unknown status: {processing_status}")
                    else:
                        print(f"❌ Status check failed: {status_response.status_code}")
                        
                except Exception as e:
                    print(f"❌ Status check error: {e}")
            
            print("⏰ Processing timeout - checking final status...")
            
        else:
            print(f"❌ Upload failed: {upload_response.status_code}")
            print(f"Error: {upload_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False
    
    return False

if __name__ == "__main__":
    print("🔧 TESTING PDF UPLOAD FIX")
    print("📚 Verifying OCR Configuration and PDF Processing")
    print("=" * 60)
    
    # Test service status first
    service_ok = test_service_status()
    
    # Test PDF upload
    upload_ok = test_pdf_upload()
    
    print("\n" + "=" * 60)
    print("📊 PDF UPLOAD FIX ASSESSMENT:")
    
    if service_ok and upload_ok:
        print("🎉 PDF UPLOAD FIX: COMPLETE SUCCESS!")
        print("✅ OCR configuration is correct")
        print("✅ PDF processing pipeline is working")
        print("✅ Upload issues are resolved")
        
    elif service_ok:
        print("⚠️ PDF UPLOAD FIX: PARTIAL SUCCESS")
        print("✅ OCR configuration is correct")
        print("❌ PDF processing still needs debugging")
        
    elif upload_ok:
        print("⚠️ PDF UPLOAD FIX: FUNCTIONAL")
        print("❌ Service status reporting issues")
        print("✅ PDF processing is working")
        
    else:
        print("❌ PDF UPLOAD FIX: NEEDS MORE WORK")
        print("❌ OCR configuration or processing issues remain")
    
    print("\n" + "=" * 60)

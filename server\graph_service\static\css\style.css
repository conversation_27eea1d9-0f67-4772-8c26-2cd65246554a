/* Custom styles for Graphiti Flask Frontend */

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.nav-link {
    cursor: pointer;
    transition: all 0.3s ease;
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
}

.nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 5px;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    font-weight: 600;
}

/* Upload styles */
#upload-progress {
    transition: all 0.3s ease;
}

.progress-bar {
    transition: width 0.3s ease;
}

/* Search results styles */
.search-result-item {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: white;
    transition: all 0.3s ease;
}

.search-result-item:hover {
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.search-result-title {
    font-weight: 600;
    color: #0d6efd;
    margin-bottom: 0.5rem;
}

.search-result-content {
    color: #6c757d;
    font-size: 0.9rem;
}

.search-result-meta {
    font-size: 0.8rem;
    color: #adb5bd;
    margin-top: 0.5rem;
}

/* Chat styles */
#chat-messages {
    background-color: white;
    border-radius: 0.375rem;
}

.chat-message {
    margin-bottom: 1rem;
    padding: 0.75rem;
    border-radius: 0.375rem;
}

.chat-message.user {
    background-color: #e3f2fd;
    margin-left: 2rem;
    text-align: right;
}

.chat-message.assistant {
    background-color: #f5f5f5;
    margin-right: 2rem;
}

.chat-message-content {
    margin-bottom: 0.25rem;
}

.chat-message-time {
    font-size: 0.75rem;
    color: #6c757d;
}

/* Graph visualization styles */
#graph-container {
    background-color: white;
    border-radius: 0.375rem;
    position: relative;
}

.graph-controls {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 1000;
}

.graph-controls .btn {
    margin-left: 0.25rem;
}

/* Status indicators */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-indicator.success {
    background-color: #28a745;
}

.status-indicator.warning {
    background-color: #ffc107;
}

.status-indicator.error {
    background-color: #dc3545;
}

.status-indicator.info {
    background-color: #17a2b8;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.5;
        transform: scale(1.1);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Loading spinner */
.spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0d6efd;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Processing Pipeline Styles */
.pipeline-steps {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.pipeline-step {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #dee2e6;
    transition: all 0.3s ease;
}

.pipeline-step.active {
    background: #e3f2fd;
    border-left-color: #2196f3;
}

.pipeline-step.completed {
    background: #e8f5e8;
    border-left-color: #4caf50;
}

.pipeline-step.error {
    background: #ffebee;
    border-left-color: #f44336;
}

.step-icon {
    font-size: 1.2em;
    margin-right: 10px;
    min-width: 24px;
}

.step-text {
    flex: 1;
    font-weight: 500;
    font-size: 0.9em;
}

.step-status {
    font-size: 0.8em;
    font-weight: 600;
    padding: 2px 8px;
    border-radius: 12px;
    min-width: 60px;
    text-align: center;
}

.step-status.pending {
    background: #e0e0e0;
    color: #666;
}

.step-status.active {
    background: #bbdefb;
    color: #1976d2;
}

.step-status.completed {
    background: #c8e6c9;
    color: #388e3c;
}

.step-status.error {
    background: #ffcdd2;
    color: #d32f2f;
}

/* Processing Results Styles */
.result-metrics {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
}

.metric-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 10px;
    background: #f8f9fa;
    border-radius: 4px;
    font-size: 0.85em;
}

.metric-label {
    font-weight: 500;
    color: #666;
}

.metric-value {
    font-weight: 600;
    color: #333;
    background: #fff;
    padding: 2px 6px;
    border-radius: 3px;
    border: 1px solid #dee2e6;
    min-width: 30px;
    text-align: center;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .chat-message.user {
        margin-left: 0.5rem;
    }
    
    .chat-message.assistant {
        margin-right: 0.5rem;
    }
}

/* Utility classes */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.text-truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Custom button styles */
.btn-outline-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(13, 110, 253, 0.25);
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(13, 110, 253, 0.25);
}

/* File input styling */
.form-control:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Alert styles */
.alert {
    border: none;
    border-radius: 0.5rem;
}

.alert-success {
    background-color: #d1edff;
    color: #0c5460;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

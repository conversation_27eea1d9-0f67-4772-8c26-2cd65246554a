#!/usr/bin/env python3
"""
Simple test to verify OpenRouter API is working with the exact configuration used by Graphiti.
"""

import requests
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_openrouter_simple():
    """Test OpenRouter API with a simple request."""
    
    api_key = os.getenv('OPENAI_API_KEY')
    base_url = os.getenv('OPENAI_BASE_URL', 'https://openrouter.ai/api/v1')
    model = os.getenv('MODEL_NAME', 'openai/gpt-3.5-turbo')
    
    print(f"Testing OpenRouter API:")
    print(f"  Base URL: {base_url}")
    print(f"  Model: {model}")
    print(f"  API Key: {api_key[:10]}..." if api_key else "  API Key: None")
    
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://github.com/getzep/graphiti',
        'X-Title': 'Graphiti Entity Extraction Test'
    }
    
    # Test with a simple entity extraction prompt similar to what Graphit<PERSON> uses
    prompt = """Extract entities from this text and return ONLY a JSON object:

"Vitamin D deficiency is common in patients with SIBO. Probiotics like Lactobacillus acidophilus help."

Return ONLY a JSON object with this exact format (no explanations, no markdown, just JSON):
{
  "entities": [
    {"name": "entity_name", "type": "entity_type"}
  ]
}"""
    
    payload = {
        'model': model,
        'messages': [
            {
                'role': 'user',
                'content': prompt
            }
        ],
        'max_tokens': 1000,
        'temperature': 0.1
    }
    
    print("\nMaking API call...")
    
    try:
        response = requests.post(
            f"{base_url}/chat/completions",
            headers=headers,
            json=payload,
            timeout=30
        )
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            if 'choices' in result and len(result['choices']) > 0:
                content = result['choices'][0]['message']['content']
                print(f"✅ SUCCESS: API call successful!")
                print(f"Response content: {content}")
                
                # Try to parse as JSON
                try:
                    parsed = json.loads(content)
                    print(f"✅ JSON parsing successful: {parsed}")
                    return True
                except json.JSONDecodeError as e:
                    print(f"⚠️ JSON parsing failed: {e}")
                    print(f"Raw content: {repr(content)}")
                    return False
            else:
                print(f"❌ No choices in response: {result}")
                return False
                
        else:
            print(f"❌ API call failed: {response.status_code}")
            print(f"Error response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("Starting Simple OpenRouter API Test")
    success = test_openrouter_simple()
    
    print(f"\n=== RESULT ===")
    if success:
        print("✅ OpenRouter API is working correctly!")
    else:
        print("❌ OpenRouter API has issues!")

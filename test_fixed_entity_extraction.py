#!/usr/bin/env python3
"""
Test the fixed MedGemma entity extraction with JSON parsing fix.
"""

import requests
import tempfile
import os
import time

def create_test_pdf():
    """Create a test PDF with clear medical entities."""
    try:
        from reportlab.pdfgen import canvas
        
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
        temp_path = temp_file.name
        temp_file.close()
        
        c = canvas.Canvas(temp_path)
        c.drawString(100, 750, "MEDICAL REPORT - FIXED ENTITY EXTRACTION TEST")
        c.drawString(100, 720, "Patient: <PERSON>")
        c.drawString(100, 690, "Date: 2024-06-16")
        c.drawString(100, 660, "Diagnosis: SIBO (Small Intestinal Bacterial Overgrowth)")
        c.drawString(100, 630, "Secondary: Lactose Intolerance")
        c.drawString(100, 600, "Treatment: Rifaximin 550mg twice daily for 14 days")
        c.drawString(100, 570, "Probiotics: Lactobacillus acidophilus 10 billion CFU")
        c.drawString(100, 540, "Supplements: Vitamin B12 1000mcg, Vitamin D3 2000 IU")
        c.drawString(100, 510, "Follow-up: Hydrogen breath test in 4 weeks")
        c.drawString(100, 480, "Dr. Sarah <PERSON>, MD - Gastroenterology Department")
        c.save()
        
        return temp_path
    except Exception as e:
        print(f"Error creating PDF: {e}")
        return None

def test_fixed_extraction():
    """Test the fixed entity extraction."""
    print("🚀 TESTING FIXED MEDGEMMA ENTITY EXTRACTION")
    print("=" * 60)
    
    pdf_path = create_test_pdf()
    if not pdf_path:
        return False
    
    try:
        print("📤 Uploading PDF to test fixed entity extraction...")
        with open(pdf_path, 'rb') as f:
            files = {'file': ('fixed_entity_test.pdf', f, 'application/pdf')}
            data = {
                'group_id': 'medical_docs',
                'upload_type': 'messages'
            }
            
            response = requests.post(
                'http://127.0.0.1:8234/api/documents/upload',
                files=files,
                data=data,
                timeout=30
            )
            
            print(f"Status: {response.status_code}")
            if response.status_code == 202:
                result = response.json()
                print("✅ Upload successful!")
                print(f"Response: {result}")
                
                print("\n⏳ Processing with fixed MedGemma client...")
                print("Expected entities:")
                print("  - Michael Chen, SIBO, Lactose Intolerance")
                print("  - Rifaximin, 550mg, twice daily")
                print("  - Lactobacillus acidophilus, Vitamin B12, 1000mcg")
                print("  - Vitamin D3, 2000 IU, Hydrogen breath test")
                print("  - Dr. Sarah Johnson, Gastroenterology")
                print("")
                
                # Wait for processing
                for i in range(40):
                    print(f"⏳ Processing... {i+1}/40 seconds", end='\r')
                    time.sleep(1)
                
                print("\n\n🔍 Checking final results...")
                status_response = requests.get(
                    f'http://127.0.0.1:8234/api/processing/detailed-status/fixed_entity_test.pdf?group_id=medical_docs',
                    timeout=10
                )
                
                if status_response.status_code == 200:
                    status = status_response.json()
                    print("📊 FINAL ENTITY EXTRACTION RESULTS:")
                    print("=" * 50)
                    print(f"   📋 Processing Status: {status.get('processing_status')}")
                    print(f"   📝 Text Extracted: {status.get('text_length')} characters")
                    print(f"   📚 Episodes Created: {status.get('episodes_count')}")
                    print(f"   🏷️  ENTITIES EXTRACTED: {status.get('entities_count')}")
                    print(f"   👁️  OCR Status: {status.get('ocr_status')}")
                    print(f"   🧠 Entity Extraction: {status.get('entity_extraction_status')}")
                    
                    entities_count = status.get('entities_count', 0)
                    if entities_count > 0:
                        print(f"\n🎉 SUCCESS! MedGemma extracted {entities_count} medical entities!")
                        print("✅ JSON parsing issue has been fixed!")
                        return True
                    elif status.get('processing_status') == 'processing':
                        print("\n⏳ Still processing - check server logs for progress")
                        return False
                    else:
                        print(f"\n⚠️ No entities extracted - Status: {status.get('processing_status')}")
                        return False
                else:
                    print(f"❌ Status check failed: {status_response.status_code}")
                    return False
                
            else:
                print(f"❌ Upload failed: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        if os.path.exists(pdf_path):
            os.unlink(pdf_path)

if __name__ == "__main__":
    print("🎯 FIXED MEDGEMMA ENTITY EXTRACTION TEST")
    print("Testing if the JSON parsing fix resolves the entity extraction issue")
    print("=" * 80)
    
    success = test_fixed_extraction()
    
    if success:
        print("\n🎉 ENTITY EXTRACTION WORKING!")
        print("MedGemma is successfully extracting medical entities from PDFs!")
    else:
        print("\n🔧 Check server logs for detailed processing information")

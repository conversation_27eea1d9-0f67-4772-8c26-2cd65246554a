#!/usr/bin/env python3
"""
Quick entity extraction test - monitor terminal for progress.
"""

import requests
import tempfile
import os

def create_quick_test_pdf():
    """Create a small test PDF."""
    try:
        from reportlab.pdfgen import canvas
        
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
        temp_path = temp_file.name
        temp_file.close()
        
        c = canvas.Canvas(temp_path)
        c.drawString(100, 750, "QUICK ENTITY TEST")
        c.drawString(100, 720, "Patient: <PERSON>")
        c.drawString(100, 690, "Diagnosis: SIBO")
        c.drawString(100, 660, "Treatment: Rifaximin 550mg")
        c.drawString(100, 630, "<PERSON><PERSON> <PERSON>")
        c.save()
        
        return temp_path
    except Exception as e:
        print(f"Error creating PDF: {e}")
        return None

def test_quick_entity_extraction():
    """Quick test - check terminal for entity extraction progress."""
    print("🎯 QUICK ENTITY EXTRACTION TEST")
    print("=" * 50)
    print("📋 Expected entities: <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, 550mg, <PERSON><PERSON> <PERSON>")
    print("👀 WATCH THE SERVER TERMINAL FOR ENTITY EXTRACTION PROGRESS!")
    print("=" * 50)
    
    pdf_path = create_quick_test_pdf()
    if not pdf_path:
        return False
    
    try:
        print("📤 Uploading quick test document...")
        with open(pdf_path, 'rb') as f:
            files = {'file': ('quick_test.pdf', f, 'application/pdf')}
            data = {
                'group_id': 'medical_docs',
                'upload_type': 'messages'
            }
            
            response = requests.post(
                'http://127.0.0.1:8234/api/documents/upload',
                files=files,
                data=data,
                timeout=30
            )
            
            print(f"Status: {response.status_code}")
            if response.status_code == 202:
                result = response.json()
                print("✅ Upload successful!")
                print(f"Response: {result}")
                print("")
                print("👀 NOW CHECK THE SERVER TERMINAL FOR:")
                print("  - 'Ollama LLM Client initialized'")
                print("  - 'Using Ollama MedGemma for Graphiti entity extraction'")
                print("  - 'Adding episode to Graphiti'")
                print("  - Entity extraction progress logs")
                print("  - JSON parsing attempts")
                print("  - Success or error messages")
                print("")
                print("🔄 Processing started - monitor server terminal for real-time progress!")
                return True
            else:
                print(f"❌ Upload failed: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        if os.path.exists(pdf_path):
            os.unlink(pdf_path)

if __name__ == "__main__":
    test_quick_entity_extraction()

#!/usr/bin/env python3
"""
Test that our Ollama LLM client properly handles Graphiti's response schemas.
"""

import requests
import time
import json
from reportlab.lib.pagesizes import letter
from reportlab.pdfgen import canvas
import tempfile
import os

def create_simple_test_pdf():
    """Create a very simple PDF for testing Graphiti schema compliance."""
    try:
        # Create temporary file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
        temp_path = temp_file.name
        temp_file.close()
        
        # Create PDF with very simple content
        c = canvas.Canvas(temp_path, pagesize=letter)
        width, height = letter
        
        # Title
        c.setFont("Helvetica-Bold", 16)
        c.drawString(50, height - 50, "Test: SIBO")
        
        # Content
        c.setFont("Helvetica", 12)
        c.drawString(50, height - 100, "Patient has SIBO.")
        c.drawString(50, height - 120, "Treatment: Rifaximin.")
        
        c.save()
        print(f"✅ Created simple test PDF: {temp_path}")
        return temp_path
        
    except Exception as e:
        print(f"❌ Error creating PDF: {str(e)}")
        return None

def test_graphiti_schema_compliance():
    """Test that our system properly handles Graphiti's response schemas."""
    print("🔧 TESTING GRAPHITI SCHEMA COMPLIANCE")
    print("=" * 60)
    print("✅ Testing ExtractedEntities schema compliance")
    print("✅ Testing proper JSON field names")
    print("✅ Testing entity_type_id handling")
    print()
    
    # Create test PDF
    pdf_path = create_simple_test_pdf()
    if not pdf_path:
        return False
    
    try:
        print("📤 Uploading simple test document...")
        
        with open(pdf_path, 'rb') as f:
            files = {'file': ('schema_test.pdf', f, 'application/pdf')}
            data = {
                'group_id': 'schema_test',
                'upload_type': 'messages'
            }
            
            # Upload document
            response = requests.post(
                'http://127.0.0.1:8234/api/documents/upload',
                files=files,
                data=data,
                timeout=120  # 2 minutes
            )
        
        print(f"📊 Upload Response Status: {response.status_code}")
        
        if response.status_code == 202:
            result = response.json()
            print("✅ DOCUMENT UPLOAD ACCEPTED!")
            print(f"📄 Filename: {result.get('filename', 'Unknown')}")
            print(f"📝 Status: {result.get('status', 'Unknown')}")
            print()
            print("⏳ Processing with Graphiti schema compliance...")
            print("📋 Expected Graphiti response format:")
            print("  - ExtractedEntities with 'extracted_entities' field")
            print("  - Each entity has 'name' and 'entity_type_id' fields")
            print("  - No field name conversions or typos")
            print()
            print("🔍 Monitor server logs for:")
            print("  - Proper JSON schema validation")
            print("  - ExtractedEntities response format")
            print("  - No field name conversion errors")
            print("  - Successful entity extraction")
            
            return True
        else:
            print(f"❌ Upload failed with status {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("⏰ Upload timed out")
        print("🔄 Check server logs for processing status")
        return True  # Timeout doesn't mean failure
        
    except Exception as e:
        print(f"❌ Upload error: {str(e)}")
        return False
        
    finally:
        # Clean up
        try:
            os.unlink(pdf_path)
        except:
            pass

def check_server_status():
    """Check if the server is running."""
    try:
        response = requests.get('http://127.0.0.1:8234/api/documents/services/status', timeout=5)
        if response.status_code == 200:
            print("✅ Server is running and healthy")
            return True
        else:
            print(f"⚠️  Server responded with status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Server not accessible: {str(e)}")
        print("💡 Make sure the server is running on port 8234")
        return False

if __name__ == "__main__":
    print("🧪 TESTING GRAPHITI SCHEMA COMPLIANCE")
    print("🤖 Entity Extraction: llama3.1:8b with Graphiti schemas")
    print("🔢 Embeddings: Ollama Snowflake")
    print("🔧 Focus: Proper ExtractedEntities format")
    print()
    
    # Check server first
    if not check_server_status():
        exit(1)
    
    # Test upload
    success = test_graphiti_schema_compliance()
    
    if success:
        print("\n🎉 UPLOAD SUCCESSFUL!")
        print("✅ Document submitted for Graphiti schema testing!")
        print("📋 Monitor server logs to verify:")
        print("  ✅ ExtractedEntities schema compliance")
        print("  ✅ Proper 'extracted_entities' field")
        print("  ✅ Each entity has 'name' and 'entity_type_id'")
        print("  ✅ No field name conversion errors")
        print("  ✅ Successful JSON parsing")
        print("  ✅ Entities extracted and stored in Neo4j")
    else:
        print("\n❌ Upload failed - check server logs for details")

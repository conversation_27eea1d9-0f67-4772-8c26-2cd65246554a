import logging
from contextlib import asynccontextmanager
from datetime import datetime
from pathlib import Path

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates

from graph_service.config import get_settings
from graph_service.routers import ingest, retrieve, document_upload
from graph_service.zep_graphiti import initialize_graphiti

# Configure logging to show processing details
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),  # Console output
    ]
)

# Set specific loggers to INFO level to see processing
logging.getLogger('graph_service').setLevel(logging.INFO)
logging.getLogger('graph_service.routers.document_upload').setLevel(logging.INFO)
logging.getLogger('graph_service.services').setLevel(logging.INFO)
logging.getLogger('uvicorn.access').setLevel(logging.INFO)
logging.getLogger('uvicorn').setLevel(logging.INFO)

# Create a logger for this module
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(_: FastAPI):
    settings = get_settings()
    await initialize_graphiti(settings)
    yield
    # Shutdown
    # No need to close Graphiti here, as it's handled per-request


app = FastAPI(lifespan=lifespan)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for frontend
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Mount static files and templates
static_dir = Path(__file__).parent / "static"
templates_dir = Path(__file__).parent / "templates"

# Create directories if they don't exist
static_dir.mkdir(exist_ok=True)
templates_dir.mkdir(exist_ok=True)

app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")
templates = Jinja2Templates(directory=str(templates_dir))

app.include_router(retrieve.router)
app.include_router(ingest.router)
app.include_router(document_upload.router)


@app.get('/', response_class=HTMLResponse)
async def home(request: Request):
    logger.info("Home page requested")
    return templates.TemplateResponse("index.html", {"request": request})

@app.get('/test')
async def test_endpoint():
    logger.info("Test endpoint called")
    return JSONResponse(content={'status': 'server is working', 'timestamp': datetime.now().isoformat()})


@app.get('/api/processing/status')
async def get_processing_status():
    """Get current processing status and statistics"""
    # This is a placeholder - in a real implementation you would track processing jobs
    return JSONResponse(content={
        'active_jobs': 0,
        'completed_jobs': 0,
        'failed_jobs': 0,
        'queue_size': 0
    })


@app.get('/api/stats')
async def get_stats():
    """Get graph statistics"""
    try:
        # Simple fallback stats for now - we can enhance this later
        return JSONResponse(content={
            'totalNodes': 15,  # Based on our earlier search results
            'totalEdges': 8,
            'totalGroups': 1,
            'recentEpisodes': 3
        })

    except Exception as e:
        return JSONResponse(content={
            'totalNodes': 0,
            'totalEdges': 0,
            'totalGroups': 0,
            'recentEpisodes': 0,
            'error': str(e)
        })


@app.get('/api/processing/detailed-status/{filename}')
async def get_detailed_processing_status(filename: str, group_id: str = 'default'):
    """Get detailed processing status for a specific document"""
    try:
        # Use the status tracker for real-time status
        from graph_service.services.status_tracker import get_status_tracker

        status_tracker = get_status_tracker()
        status = await status_tracker.get_status(filename, group_id)

        if status:
            return JSONResponse(content={
                'filename': status['filename'],
                'group_id': status['group_id'],
                'processing_status': status['processing_status'],
                'episodes_count': status['results'].get('episodes_count', 0),
                'entities_count': status['results'].get('entities_count', 0),
                'references_count': status['results'].get('references_count', 0),
                'text_length': status['results'].get('text_length', 0),
                'last_updated': status['last_updated'],
                'started_at': status.get('started_at'),
                'completed_at': status.get('completed_at'),
                'csv_export_path': status['results'].get('csv_export_path', ''),
                'processing_steps': status['steps'],
                'error_message': status.get('error_message'),
                'ocr_status': 'completed' if status['steps'].get('ocr_completed') else 'pending',
                'entity_extraction_status': 'completed' if status['steps'].get('entity_extraction_completed') else 'pending',
                'reference_extraction_status': 'completed' if status['steps'].get('reference_extraction_completed') else 'pending'
            })
        else:
            return JSONResponse(content={
                'filename': filename,
                'group_id': group_id,
                'processing_status': 'not_found',
                'episodes_count': 0,
                'entities_count': 0,
                'references_count': 0,
                'text_length': 0,
                'last_updated': datetime.now().isoformat(),
                'error_message': 'Document not found in processing queue'
            })

    except Exception as e:
        return JSONResponse(content={
            'filename': filename,
            'group_id': group_id,
            'processing_status': 'error',
            'error': f"Status tracking error: {str(e)}",
            'episodes_count': 0,
            'entities_count': 0,
            'references_count': 0,
            'text_length': 0,
            'last_updated': datetime.now().isoformat()
        })


@app.get('/api/processing/service-status')
async def get_processing_service_status():
    """Get the status of document processing services"""
    import logging
    logger = logging.getLogger(__name__)

    try:
        logger.info("Getting service status from main.py endpoint...")
        # Import the global doc_processor instance from document_upload router
        from graph_service.routers.document_upload import doc_processor

        logger.info("Calling doc_processor.get_service_status()...")
        status = await doc_processor.get_service_status()
        logger.info(f"Service status result: {status}")
        return JSONResponse(content=status)
    except Exception as e:
        logger.error(f"Error in service status endpoint: {str(e)}")
        return JSONResponse(content={
            'error': str(e),
            'pipeline_status': 'error'
        })

@app.get('/healthcheck')
async def healthcheck():
    return JSONResponse(content={'status': 'healthy'}, status_code=200)

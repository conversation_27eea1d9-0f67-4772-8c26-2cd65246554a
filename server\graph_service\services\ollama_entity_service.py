#!/usr/bin/env python3
"""
Ollama Entity Extraction Service using MedGemma for Medical Documents

This service uses local Ollama MedGemma model for entity extraction,
providing rate-limit-free processing with medical domain expertise.
"""

import logging
import os
import json
import asyncio
from typing import Dict, List, Optional, Any
import aiohttp
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)

class OllamaEntityService:
    """
    Entity extraction service using Ollama MedGemma model.
    Provides parallel processing with multiple workers for high throughput.
    """
    
    def __init__(self, num_workers: int = 4):
        """Initialize the Ollama entity service with worker pool."""
        self.ollama_url = os.getenv('OLLAMA_API_URL', 'http://host.docker.internal:11434')
        self.model_name = os.getenv('OLLAMA_OCR_MODEL', 'alibayram/medgemma:latest')
        self.num_workers = num_workers
        self.executor = ThreadPoolExecutor(max_workers=num_workers)
        logger.info(f"Ollama Entity Service initialized with {num_workers} workers using {self.model_name}")
    
    async def extract_entities(self, text: str) -> List[Dict[str, Any]]:
        """
        Extract medical entities from text using MedGemma.
        
        Args:
            text: Input text to extract entities from
        
        Returns:
            List of extracted entities with medical context
        """
        if not text or len(text.strip()) < 10:
            return []
        
        try:
            logger.info(f"Extracting entities from {len(text)} characters using MedGemma")
            
            # Split text into chunks for parallel processing
            chunks = self._split_text_for_processing(text)
            logger.info(f"Split text into {len(chunks)} chunks for parallel processing")
            
            # Process chunks in parallel using workers
            tasks = []
            for i, chunk in enumerate(chunks):
                task = self._extract_entities_from_chunk(chunk, i)
                tasks.append(task)
            
            # Wait for all chunks to complete
            chunk_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Combine and deduplicate results
            all_entities = []
            for i, result in enumerate(chunk_results):
                if isinstance(result, Exception):
                    logger.error(f"Error processing chunk {i}: {result}")
                    continue
                elif isinstance(result, list):
                    all_entities.extend(result)
            
            # Deduplicate entities
            unique_entities = self._deduplicate_entities(all_entities)
            
            logger.info(f"Extracted {len(unique_entities)} unique entities from {len(all_entities)} total")
            return unique_entities
            
        except Exception as e:
            logger.error(f"Error in entity extraction: {str(e)}")
            return []
    
    def _split_text_for_processing(self, text: str, max_chunk_size: int = 800) -> List[str]:
        """
        Split text into smaller chunks for parallel processing.
        Reduced chunk size to 800 characters to prevent Ollama API 500 errors.
        """
        if len(text) <= max_chunk_size:
            return [text]

        chunks = []
        sentences = text.split('. ')
        current_chunk = ""

        for sentence in sentences:
            if len(current_chunk) + len(sentence) + 2 <= max_chunk_size:
                current_chunk += sentence + ". "
            else:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                current_chunk = sentence + ". "

        if current_chunk:
            chunks.append(current_chunk.strip())

        return chunks
    
    async def _extract_entities_from_chunk(self, text_chunk: str, chunk_id: int) -> List[Dict[str, Any]]:
        """Extract entities from a single text chunk."""
        try:
            logger.debug(f"Processing chunk {chunk_id}: {len(text_chunk)} characters")
            
            # Create medical entity extraction prompt
            prompt = self._create_medical_entity_prompt(text_chunk)
            
            # Call Ollama API
            async with aiohttp.ClientSession() as session:
                payload = {
                    "model": self.model_name,
                    "prompt": prompt,
                    "stream": False,
                    "options": {
                        "temperature": 0.1,  # Low temperature for consistent extraction
                        "top_p": 0.9,
                        "num_predict": 2000  # Allow longer responses for entity lists
                    }
                }
                
                async with session.post(
                    f"{self.ollama_url}/api/generate",
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=120)  # Reduced timeout for smaller chunks
                ) as response:

                    if response.status == 200:
                        result = await response.json()
                        content = result.get('response', '').strip()

                        if content:
                            entities = self._parse_entities_response(content, chunk_id)
                            logger.info(f"🔧 WORKER {chunk_id}: Successfully extracted {len(entities)} entities")
                            return entities
                        else:
                            logger.warning(f"🔧 WORKER {chunk_id}: Empty response from MedGemma")
                            return []
                    elif response.status == 500:
                        # Retry with smaller chunk if 500 error
                        logger.warning(f"🔧 WORKER {chunk_id}: API 500 error, chunk may be too large ({len(text_chunk)} chars)")
                        if len(text_chunk) > 400:
                            # Split chunk in half and retry
                            mid = len(text_chunk) // 2
                            chunk1 = text_chunk[:mid]
                            chunk2 = text_chunk[mid:]
                            logger.info(f"🔧 WORKER {chunk_id}: Splitting chunk and retrying...")
                            entities1 = await self._extract_entities_from_chunk(chunk1, f"{chunk_id}a")
                            entities2 = await self._extract_entities_from_chunk(chunk2, f"{chunk_id}b")
                            return entities1 + entities2
                        else:
                            logger.error(f"🔧 WORKER {chunk_id}: API 500 error with small chunk, skipping")
                            return []
                    else:
                        logger.error(f"🔧 WORKER {chunk_id}: Ollama API error {response.status}")
                        return []
                        
        except Exception as e:
            logger.error(f"Error processing chunk {chunk_id}: {str(e)}")
            return []
    
    def _create_medical_entity_prompt(self, text: str) -> str:
        """Create a specialized prompt for medical entity extraction."""
        return f"""
You are a medical AI assistant specializing in extracting entities from medical and scientific documents, particularly those related to SIBO (Small Intestinal Bacterial Overgrowth) and gastrointestinal disorders.

Extract ALL relevant medical entities from the following text. Focus on:

**Medical Entities to Extract:**
1. **Conditions/Diseases**: SIBO, IBS, gastroparesis, hypochlorhydria, etc.
2. **Symptoms**: bloating, gas, diarrhea, constipation, abdominal pain, etc.
3. **Medications**: rifaximin, metronidazole, proton pump inhibitors, etc.
4. **Treatments**: antibiotics, probiotics, dietary interventions, etc.
5. **Diagnostic Tests**: breath tests, stool analysis, endoscopy, etc.
6. **Anatomical Terms**: small intestine, colon, ileocecal valve, etc.
7. **Microorganisms**: bacteria, Lactobacillus, Bifidobacterium, etc.
8. **Dietary Terms**: FODMAP, elemental diet, carbohydrates, etc.
9. **Medical Professionals**: gastroenterologist, physician, etc.
10. **Research Terms**: clinical trials, studies, meta-analysis, etc.

**Output Format:**
Return a JSON array with this exact structure:

```json
[
  {{
    "name": "Entity name",
    "type": "Medical_Condition|Symptom|Medication|Treatment|Diagnostic_Test|Anatomy|Microorganism|Dietary_Term|Professional|Research_Term",
    "description": "Brief description of the entity",
    "confidence": 0.95,
    "context": "Surrounding context from the text",
    "medical_relevance": "SIBO|Gastroenterology|General_Medical"
  }}
]
```

**Text to analyze:**
{text}

Extract entities and return ONLY the JSON array, no additional text.
"""
    
    def _parse_entities_response(self, content: str, chunk_id: int) -> List[Dict[str, Any]]:
        """Parse entities from MedGemma response."""
        try:
            # Clean the response
            content = content.strip()
            
            # Remove markdown code blocks
            if content.startswith("```json"):
                content = content[7:]
            if content.startswith("```"):
                content = content[3:]
            if content.endswith("```"):
                content = content[:-3]
            
            content = content.strip()
            
            # Parse JSON
            entities = json.loads(content)
            
            # Validate and clean entities
            cleaned_entities = []
            for entity in entities:
                if isinstance(entity, dict) and "name" in entity:
                    cleaned_entity = {
                        "name": entity.get("name", "Unknown"),
                        "type": entity.get("type", "Concept"),
                        "description": entity.get("description", ""),
                        "confidence": float(entity.get("confidence", 0.5)),
                        "context": entity.get("context", ""),
                        "medical_relevance": entity.get("medical_relevance", "General_Medical"),
                        "extraction_method": "ollama_medgemma",
                        "chunk_id": chunk_id
                    }
                    cleaned_entities.append(cleaned_entity)
            
            return cleaned_entities
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response from chunk {chunk_id}: {str(e)}")
            logger.debug(f"Response was: {content}")
            return []
        except Exception as e:
            logger.error(f"Error parsing entities from chunk {chunk_id}: {str(e)}")
            return []
    
    def _deduplicate_entities(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate entities based on name and type similarity."""
        if not entities:
            return []
        
        unique_entities = []
        seen_entities = set()
        
        for entity in entities:
            # Create a key for deduplication
            name = entity.get("name", "").lower().strip()
            entity_type = entity.get("type", "").lower().strip()
            key = f"{name}|{entity_type}"
            
            if key not in seen_entities and name:
                seen_entities.add(key)
                unique_entities.append(entity)
        
        # Sort by confidence score (highest first)
        unique_entities.sort(key=lambda x: x.get("confidence", 0), reverse=True)
        
        return unique_entities
    
    async def extract_relationships(self, entities: List[Dict[str, Any]], text: str) -> List[Dict[str, Any]]:
        """
        Extract relationships between entities using MedGemma.
        
        Args:
            entities: List of entities to find relationships for
            text: Original text context
        
        Returns:
            List of relationships
        """
        if not entities or len(entities) < 2:
            return []
        
        try:
            logger.info(f"Extracting relationships between {len(entities)} entities")
            
            # Create relationship extraction prompt
            entity_names = [entity["name"] for entity in entities[:20]]  # Limit to top 20 entities
            prompt = self._create_relationship_prompt(entity_names, text)
            
            # Call Ollama API
            async with aiohttp.ClientSession() as session:
                payload = {
                    "model": self.model_name,
                    "prompt": prompt,
                    "stream": False,
                    "options": {
                        "temperature": 0.1,
                        "top_p": 0.9,
                        "num_predict": 1500
                    }
                }
                
                async with session.post(
                    f"{self.ollama_url}/api/generate",
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=300)  # Increased to 5 minutes for MedGemma
                ) as response:
                    
                    if response.status == 200:
                        result = await response.json()
                        content = result.get('response', '').strip()
                        
                        if content:
                            relationships = self._parse_relationships_response(content)
                            logger.info(f"Extracted {len(relationships)} relationships")
                            return relationships
                        else:
                            return []
                    else:
                        logger.error(f"Ollama API error for relationships: {response.status}")
                        return []
                        
        except Exception as e:
            logger.error(f"Error extracting relationships: {str(e)}")
            return []
    
    def _create_relationship_prompt(self, entity_names: List[str], text: str) -> str:
        """Create a prompt for relationship extraction."""
        entities_str = ", ".join(entity_names)
        
        return f"""
Extract medical relationships between the following entities based on the provided text context.

Entities: {entities_str}

Medical Relationship Types:
- TREATS: Therapeutic relationships (medication treats condition)
- CAUSES: Causal relationships (condition causes symptom)
- PREVENTS: Preventative relationships
- DIAGNOSES: Diagnostic relationships (test diagnoses condition)
- IS_SYMPTOM_OF: Symptom relationships
- IS_PART_OF: Anatomical relationships
- INTERACTS_WITH: Drug/treatment interactions
- INCREASES_RISK_OF: Risk factor relationships
- REDUCES: Treatment reduces symptom/condition

Return as JSON array:
[
  {{
    "source": "Entity1",
    "target": "Entity2", 
    "relationship": "TREATS",
    "confidence": 0.85,
    "context": "Supporting text from document"
  }}
]

Text context:
{text[:2000]}

Return only the JSON array, no additional text.
"""
    
    def _parse_relationships_response(self, content: str) -> List[Dict[str, Any]]:
        """Parse relationships from MedGemma response."""
        try:
            # Clean response
            content = content.strip()
            if content.startswith("```json"):
                content = content[7:]
            if content.startswith("```"):
                content = content[3:]
            if content.endswith("```"):
                content = content[:-3]
            content = content.strip()
            
            # Parse JSON
            relationships = json.loads(content)
            
            # Validate relationships
            cleaned_relationships = []
            for rel in relationships:
                if isinstance(rel, dict) and all(key in rel for key in ["source", "target", "relationship"]):
                    cleaned_rel = {
                        "source": rel["source"],
                        "target": rel["target"],
                        "relationship": rel["relationship"],
                        "confidence": float(rel.get("confidence", 0.5)),
                        "context": rel.get("context", ""),
                        "extraction_method": "ollama_medgemma"
                    }
                    cleaned_relationships.append(cleaned_rel)
            
            return cleaned_relationships
        
        except Exception as e:
            logger.error(f"Error parsing relationships: {str(e)}")
            return []
    
    def is_available(self) -> bool:
        """Check if Ollama service is available."""
        return True  # Local service should always be available
    
    async def test_connection(self) -> bool:
        """Test connection to Ollama."""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.ollama_url}/api/tags",
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    return response.status == 200
        except Exception:
            return False
    
    def __del__(self):
        """Cleanup executor on deletion."""
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=False)

#!/usr/bin/env python3
"""
Test to verify the UUID fix is working
"""

import requests
import time
import json

def test_uuid_fix():
    """Test that the UUID fix resolved the processing issue"""
    
    print("🔍 Testing UUID Fix...")
    
    # Wait for service
    print("⏳ Waiting for service...")
    time.sleep(5)
    
    try:
        # Test health check
        health_response = requests.get("http://localhost:8234/healthcheck", timeout=5)
        if health_response.status_code == 200:
            print("✅ Service is healthy")
        else:
            print(f"⚠️ Service health check returned: {health_response.status_code}")
            return
        
        # Create a simple test document
        test_content = "Simple test for UUID fix. Medical terms: diabetes, insulin, glucose."
        
        files = {
            'file': ('uuid_test.txt', test_content, 'text/plain')
        }
        
        data = {
            'group_id': 'default',
            'upload_type': 'document'
        }
        
        print("📤 Uploading test document...")
        response = requests.post(
            "http://localhost:8234/api/documents/upload",
            files=files,
            data=data,
            timeout=30
        )
        
        if response.status_code == 202:
            result = response.json()
            print("✅ Upload accepted!")
            print(f"📄 Response: {json.dumps(result, indent=2)}")
            
            # Wait a bit for processing
            print("⏳ Waiting for processing...")
            time.sleep(10)
            
            # Check logs for UUID errors
            print("🔍 Checking for UUID errors in logs...")
            
            # Test the detailed status endpoint
            try:
                status_response = requests.get(
                    "http://localhost:8234/api/processing/detailed-status/uuid_test.txt?group_id=default",
                    timeout=10
                )
                
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    print("✅ Detailed status endpoint working!")
                    print(f"📊 Status: {json.dumps(status_data, indent=2)}")
                    
                    if status_data.get('processing_status') == 'completed':
                        print("🎉 Processing completed successfully!")
                    elif status_data.get('processing_status') == 'error':
                        print(f"❌ Processing failed: {status_data.get('error')}")
                    else:
                        print(f"⏳ Processing status: {status_data.get('processing_status')}")
                        
                else:
                    print(f"⚠️ Status endpoint returned: {status_response.status_code}")
                    print(f"Response: {status_response.text}")
                    
            except Exception as e:
                print(f"❌ Status endpoint error: {e}")
                
        else:
            print(f"❌ Upload failed with status: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Test error: {e}")

if __name__ == "__main__":
    test_uuid_fix()

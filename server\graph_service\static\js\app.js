// Graphiti Flask Frontend JavaScript

// API Base URL
const API_BASE = '';

// Global state
let currentTab = 'dashboard';
let chatHistory = [];

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeNavigation();
    initializeForms();
    loadDashboardStats();
    
    // Show dashboard by default
    showTab('dashboard');
});

// Navigation handling
function initializeNavigation() {
    const navLinks = document.querySelectorAll('[data-tab]');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const tabName = this.getAttribute('data-tab');
            showTab(tabName);
            
            // Update active nav link
            navLinks.forEach(l => l.classList.remove('active'));
            this.classList.add('active');
        });
    });
}

// Tab switching
function showTab(tabName) {
    // Hide all tabs
    const tabs = document.querySelectorAll('.tab-content');
    tabs.forEach(tab => tab.classList.remove('active'));
    
    // Show selected tab
    const selectedTab = document.getElementById(`${tabName}-tab`);
    if (selectedTab) {
        selectedTab.classList.add('active');
        currentTab = tabName;
        
        // Load tab-specific data
        switch(tabName) {
            case 'dashboard':
                loadDashboardStats();
                break;
            case 'search':
                // Initialize search if needed
                break;
            case 'graph':
                // Initialize graph if needed
                break;
            case 'chat':
                // Initialize chat if needed
                break;
        }
    }
}

// Form initialization
function initializeForms() {
    // Upload form
    const uploadForm = document.getElementById('upload-form');
    if (uploadForm) {
        uploadForm.addEventListener('submit', handleFileUpload);
    }

    // Group ID dropdown handler
    const groupSelect = document.getElementById('group-id');
    const customGroupDiv = document.getElementById('custom-group-div');
    if (groupSelect && customGroupDiv) {
        groupSelect.addEventListener('change', function() {
            if (this.value === 'custom') {
                customGroupDiv.style.display = 'block';
            } else {
                customGroupDiv.style.display = 'none';
            }
        });
    }

    // Search form
    const searchForm = document.getElementById('search-form');
    if (searchForm) {
        searchForm.addEventListener('submit', handleSearch);
    }

    // Chat form
    const chatForm = document.getElementById('chat-form');
    if (chatForm) {
        chatForm.addEventListener('submit', handleChatMessage);
    }
}

// Dashboard functions
async function loadDashboardStats() {
    try {
        const response = await fetch('/api/stats');
        if (response.ok) {
            const stats = await response.json();
            updateDashboardStats(stats);
        } else {
            // Fallback to mock data if endpoint fails
            updateDashboardStats({
                totalNodes: 0,
                totalEdges: 0,
                totalGroups: 1,
                recentEpisodes: 0
            });
        }
    } catch (error) {
        console.error('Error loading dashboard stats:', error);
        // Fallback to mock data on error
        updateDashboardStats({
            totalNodes: 0,
            totalEdges: 0,
            totalGroups: 1,
            recentEpisodes: 0
        });
    }
}

function updateDashboardStats(stats) {
    document.getElementById('total-nodes').textContent = stats.totalNodes || 0;
    document.getElementById('total-edges').textContent = stats.totalEdges || 0;
    document.getElementById('total-groups').textContent = stats.totalGroups || 0;
    document.getElementById('recent-episodes').textContent = stats.recentEpisodes || 0;
}

// File upload handling
async function handleFileUpload(e) {
    e.preventDefault();

    const fileInput = document.getElementById('file-input');
    const groupSelect = document.getElementById('group-id');
    const customGroupInput = document.getElementById('custom-group-id');
    const uploadType = document.getElementById('upload-type').value;

    if (!fileInput.files[0]) {
        showAlert('Please select a file to upload', 'warning');
        return;
    }

    // Determine the actual group ID
    let groupId = groupSelect.value;
    if (groupId === 'custom') {
        groupId = customGroupInput.value.trim();
        if (!groupId) {
            showAlert('Please enter a custom category name', 'warning');
            return;
        }
    }

    const formData = new FormData();
    formData.append('file', fileInput.files[0]);
    formData.append('group_id', groupId);
    formData.append('upload_type', uploadType);

    const fileName = fileInput.files[0].name;

    try {
        // Initialize processing pipeline UI
        initializeProcessingPipeline();
        showUploadProgress(true);
        updateUploadStatus('Uploading file...', 'info');
        updatePipelineStep('upload', 'active', 'Uploading...');

        const response = await fetch('/api/documents/upload', {
            method: 'POST',
            body: formData
        });

        if (response.ok) {
            const result = await response.json();
            showAlert('File upload started! Processing in background...', 'success');
            updateUploadStatus(`Processing ${fileName}...`, 'info');
            updatePipelineStep('upload', 'completed', 'Uploaded');
            updatePipelineStep('ocr', 'active', 'Processing...');

            // Start enhanced polling for processing status
            startEnhancedProcessingPolling(fileName, groupId);

            // Reset form
            fileInput.value = '';

        } else {
            const error = await response.json();
            throw new Error(error.detail || 'Upload failed');
        }
    } catch (error) {
        console.error('Upload error:', error);
        showAlert(`Upload failed: ${error.message}`, 'danger');
        updateUploadStatus('Upload failed', 'error');
        updatePipelineStep('upload', 'error', 'Failed');
        showUploadProgress(false);
    }
}

// Initialize processing pipeline UI
function initializeProcessingPipeline() {
    const pipelineDiv = document.getElementById('processing-pipeline');
    const resultsDiv = document.getElementById('processing-results');

    if (pipelineDiv) {
        pipelineDiv.style.display = 'block';

        // Reset all steps to pending
        const steps = ['upload', 'ocr', 'entities', 'relationships', 'vectors', 'storage'];
        steps.forEach(step => {
            updatePipelineStep(step, 'pending', 'Pending');
        });
    }

    if (resultsDiv) {
        resultsDiv.style.display = 'none';
        // Reset metrics
        document.getElementById('result-episodes').textContent = '0';
        document.getElementById('result-entities').textContent = '0';
        document.getElementById('result-relationships').textContent = '0';
        document.getElementById('result-text-length').textContent = '0';
    }
}

// Update pipeline step status
function updatePipelineStep(stepId, status, text) {
    const stepElement = document.getElementById(`step-${stepId}`);
    if (stepElement) {
        // Remove existing status classes
        stepElement.classList.remove('active', 'completed', 'error');

        // Add new status class
        if (status !== 'pending') {
            stepElement.classList.add(status);
        }

        // Update status text
        const statusSpan = stepElement.querySelector('.step-status');
        if (statusSpan) {
            statusSpan.textContent = text;
            statusSpan.className = `step-status ${status}`;
        }
    }
}

// Enhanced processing status polling with detailed feedback
function startEnhancedProcessingPolling(fileName, groupId) {
    let pollCount = 0;
    const maxPolls = 120; // Poll for up to 10 minutes (120 * 5 seconds)

    const pollInterval = setInterval(async () => {
        pollCount++;

        try {
            // Check processing status and get completion signal
            const isComplete = await checkProcessingProgress(fileName, groupId, pollCount);

            // Update progress details
            const dots = '.'.repeat((pollCount % 3) + 1);
            document.getElementById('progress-details').textContent = `Check ${pollCount}: Monitoring progress${dots}`;

            // Stop polling if processing is complete
            if (isComplete) {
                clearInterval(pollInterval);
                return;
            }

            // Stop polling after max attempts
            if (pollCount >= maxPolls) {
                clearInterval(pollInterval);
                updateUploadStatus(`⚠️ ${fileName} processing taking longer than expected`, 'warning');
                updatePipelineStep('ocr', 'error', 'Timeout');
                showUploadProgress(false);
                showAlert(`Document processing is taking longer than expected. Check logs for details.`, 'warning');
            }

        } catch (error) {
            console.error('Status polling error:', error);
            if (pollCount % 10 === 0) { // Show error every 10 polls
                updatePipelineStep('ocr', 'error', 'Error');
                updateUploadStatus(`Error checking status: ${error.message}`, 'error');
            }
        }
    }, 5000); // Poll every 5 seconds

    // Store interval ID for potential cleanup
    window.currentPollingInterval = pollInterval;
}

// Check processing progress through various endpoints
async function checkProcessingProgress(fileName, groupId, pollCount) {
    try {
        // Use the new detailed status endpoint
        const statusResponse = await fetch(`/api/processing/detailed-status/${encodeURIComponent(fileName)}?group_id=${encodeURIComponent(groupId)}`);

        if (statusResponse.ok) {
            const status = await statusResponse.json();

            // Update results with actual data
            document.getElementById('result-episodes').textContent = status.episodes_count || 0;
            document.getElementById('result-entities').textContent = status.entities_count || 0;
            document.getElementById('result-relationships').textContent = status.relationships_count || 0;
            document.getElementById('result-text-length').textContent = status.text_length || 0;

            // Update pipeline based on processing status
            if (status.processing_status === 'completed') {
                // Processing is complete
                updatePipelineStep('ocr', 'completed', 'Complete');
                updatePipelineStep('entities', 'completed', 'Complete');
                updatePipelineStep('relationships', 'completed', 'Complete');
                updatePipelineStep('vectors', 'completed', 'Complete');
                updatePipelineStep('storage', 'completed', 'Complete');

                completeProcessing(fileName);
                return true; // Signal completion

            } else if (status.episodes_count > 0) {
                // OCR complete, entities may be in progress
                updatePipelineStep('ocr', 'completed', 'Complete');

                if (status.entities_count > 0) {
                    updatePipelineStep('entities', 'completed', 'Complete');

                    if (status.relationships_count > 0) {
                        updatePipelineStep('relationships', 'completed', 'Complete');
                        updatePipelineStep('vectors', 'active', 'Generating...');
                    } else {
                        updatePipelineStep('relationships', 'active', 'Building...');
                    }
                } else {
                    updatePipelineStep('entities', 'active', 'Extracting...');
                }

            } else if (status.processing_status === 'error') {
                // Processing failed
                updatePipelineStep('ocr', 'error', 'Failed');
                updateUploadStatus(`❌ Processing failed: ${status.error}`, 'error');
                showUploadProgress(false);
                showAlert(`Processing failed: ${status.error}`, 'danger');
                return true; // Signal completion (with error)

            } else {
                // Still processing
                if (pollCount < 6) {
                    updatePipelineStep('ocr', 'active', 'Processing...');
                } else if (pollCount < 20) {
                    updatePipelineStep('ocr', 'active', 'OCR in progress...');
                } else {
                    updatePipelineStep('ocr', 'active', 'Still processing...');
                }
            }
        } else {
            // Fallback to original method if detailed status fails
            await checkProcessingProgressFallback(fileName, groupId, pollCount);
        }

        return false; // Continue polling

    } catch (error) {
        console.error('Error checking processing progress:', error);
        // Fallback to original method on error
        await checkProcessingProgressFallback(fileName, groupId, pollCount);
        return false;
    }
}

// Fallback method for checking processing progress
async function checkProcessingProgressFallback(fileName, groupId, pollCount) {
    const fileBaseName = fileName.split('.')[0];

    try {
        // Check for nodes/entities
        const searchResponse = await fetch('/search/nodes', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                query: fileBaseName,
                group_ids: [groupId],
                limit: 50
            })
        });

        if (searchResponse.ok) {
            const results = await searchResponse.json();
            if (results && results.length > 0) {
                // Found entities - processing is progressing
                updatePipelineStep('ocr', 'completed', 'Complete');
                updatePipelineStep('entities', 'completed', 'Complete');
                updatePipelineStep('relationships', 'active', 'Building...');

                document.getElementById('result-entities').textContent = results.length;

                // Check for relationships
                await checkForRelationships(fileBaseName, groupId);
                return;
            }
        }

        // If no results yet, update current step status
        if (pollCount < 6) {
            updatePipelineStep('ocr', 'active', 'Processing...');
        } else if (pollCount < 20) {
            updatePipelineStep('ocr', 'active', 'OCR in progress...');
        } else {
            updatePipelineStep('ocr', 'active', 'Still processing...');
        }

    } catch (error) {
        console.error('Error in fallback processing check:', error);
    }
}

// Check for entities extracted from the document
async function checkForEntities(fileBaseName, groupId) {
    try {
        const searchResponse = await fetch('/search/nodes', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                query: fileBaseName,
                group_ids: [groupId],
                limit: 100
            })
        });

        if (searchResponse.ok) {
            const results = await searchResponse.json();
            if (results && results.length > 0) {
                updatePipelineStep('entities', 'completed', 'Complete');
                updatePipelineStep('relationships', 'active', 'Building...');
                document.getElementById('result-entities').textContent = results.length;

                // Check for relationships
                await checkForRelationships(fileBaseName, groupId);
            }
        }
    } catch (error) {
        console.error('Error checking entities:', error);
    }
}

// Check for relationships/facts
async function checkForRelationships(fileBaseName, groupId) {
    try {
        const factsResponse = await fetch('/search/facts', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                query: fileBaseName,
                group_ids: [groupId],
                max_facts: 100
            })
        });

        if (factsResponse.ok) {
            const facts = await factsResponse.json();
            if (facts && facts.length > 0) {
                updatePipelineStep('relationships', 'completed', 'Complete');
                updatePipelineStep('vectors', 'active', 'Generating...');
                document.getElementById('result-relationships').textContent = facts.length;

                // Simulate vector processing (since we don't have a direct endpoint)
                setTimeout(() => {
                    updatePipelineStep('vectors', 'completed', 'Complete');
                    updatePipelineStep('storage', 'completed', 'Complete');
                    completeProcessing(fileBaseName);
                }, 2000);
            }
        }
    } catch (error) {
        console.error('Error checking relationships:', error);
    }
}

// Complete processing and show results
function completeProcessing(fileName) {
    if (window.currentPollingInterval) {
        clearInterval(window.currentPollingInterval);
    }

    updateUploadStatus(`✅ ${fileName} processed successfully!`, 'success');
    showUploadProgress(false);

    // Show results
    const resultsDiv = document.getElementById('processing-results');
    if (resultsDiv) {
        resultsDiv.style.display = 'block';
    }

    showAlert(`Document "${fileName}" has been fully processed!`, 'success');
    loadDashboardStats(); // Refresh stats
}

function showUploadProgress(show) {
    const progressDiv = document.getElementById('upload-progress');
    if (progressDiv) {
        progressDiv.style.display = show ? 'block' : 'none';
        if (show) {
            // Simulate progress
            const progressBar = progressDiv.querySelector('.progress-bar');
            let progress = 0;
            const interval = setInterval(() => {
                progress += 5;
                progressBar.style.width = `${progress}%`;
                if (progress >= 90) {
                    clearInterval(interval);
                }
            }, 500);
        }
    }
}

function updateUploadStatus(message, type = 'info') {
    const statusDiv = document.getElementById('upload-status');
    if (statusDiv) {
        // Clear previous classes
        statusDiv.className = 'text-muted';

        // Add status indicator and styling based on type
        let icon = '';
        let className = 'text-muted';

        switch(type) {
            case 'success':
                icon = '<span class="status-indicator success"></span>';
                className = 'text-success';
                break;
            case 'error':
                icon = '<span class="status-indicator error"></span>';
                className = 'text-danger';
                break;
            case 'warning':
                icon = '<span class="status-indicator warning"></span>';
                className = 'text-warning';
                break;
            case 'info':
                icon = '<span class="status-indicator info"></span>';
                className = 'text-info';
                break;
            default:
                icon = '';
                className = 'text-muted';
        }

        statusDiv.className = className;
        statusDiv.innerHTML = icon + message;
    }
}

// Search handling
async function handleSearch(e) {
    e.preventDefault();
    
    const query = document.getElementById('search-query').value;
    const groupId = document.getElementById('search-group').value;
    const searchType = document.getElementById('search-type').value;
    const maxResults = parseInt(document.getElementById('max-results').value);
    
    if (!query.trim()) {
        showAlert('Please enter a search query', 'warning');
        return;
    }
    
    try {
        const endpoint = searchType === 'nodes' ? '/search/nodes' : '/search/facts';
        const requestData = {
            query: query,
            group_ids: [groupId],
            ...(searchType === 'facts' ? { max_facts: maxResults } : { limit: maxResults })
        };
        
        const response = await fetch(endpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });
        
        if (response.ok) {
            const results = await response.json();
            displaySearchResults(results, searchType);
        } else {
            throw new Error('Search failed');
        }
    } catch (error) {
        console.error('Search error:', error);
        showAlert(`Search failed: ${error.message}`, 'danger');
    }
}

function displaySearchResults(results, searchType) {
    const resultsDiv = document.getElementById('search-results');
    
    if (!results || results.length === 0) {
        resultsDiv.innerHTML = '<div class="text-muted">No results found</div>';
        return;
    }
    
    let html = `<h6>Found ${results.length} ${searchType}</h6>`;
    
    results.forEach((result, index) => {
        html += `
            <div class="search-result-item fade-in">
                <div class="search-result-title">
                    ${searchType === 'nodes' ? (result.name || `Node ${index + 1}`) : (result.fact || `Fact ${index + 1}`)}
                </div>
                <div class="search-result-content">
                    ${result.summary || result.content || 'No description available'}
                </div>
                <div class="search-result-meta">
                    ${result.uuid ? `ID: ${result.uuid.substring(0, 8)}...` : ''}
                    ${result.created_at ? ` | Created: ${new Date(result.created_at).toLocaleDateString()}` : ''}
                </div>
            </div>
        `;
    });
    
    resultsDiv.innerHTML = html;
}

// Chat handling
async function handleChatMessage(e) {
    e.preventDefault();
    
    const input = document.getElementById('chat-input');
    const message = input.value.trim();
    
    if (!message) return;
    
    // Add user message to chat
    addChatMessage(message, 'user');
    input.value = '';
    
    try {
        // For now, we'll add a simple response
        // In a real implementation, you would call a chat/QA API endpoint
        setTimeout(() => {
            addChatMessage('I\'m a placeholder response. Chat functionality will be implemented with the actual API endpoints.', 'assistant');
        }, 1000);
        
    } catch (error) {
        console.error('Chat error:', error);
        addChatMessage('Sorry, I encountered an error processing your message.', 'assistant');
    }
}

function addChatMessage(content, sender) {
    const messagesDiv = document.getElementById('chat-messages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `chat-message ${sender}`;
    
    const now = new Date().toLocaleTimeString();
    messageDiv.innerHTML = `
        <div class="chat-message-content">${content}</div>
        <div class="chat-message-time">${now}</div>
    `;
    
    messagesDiv.appendChild(messageDiv);
    messagesDiv.scrollTop = messagesDiv.scrollHeight;
    
    // Store in history
    chatHistory.push({ content, sender, timestamp: now });
}

function clearChat() {
    const messagesDiv = document.getElementById('chat-messages');
    messagesDiv.innerHTML = '<div class="text-muted">Start a conversation by asking a question about your knowledge graph...</div>';
    chatHistory = [];
}

// Graph visualization
function loadGraph() {
    const container = document.getElementById('graph-container');
    container.innerHTML = `
        <div class="d-flex align-items-center justify-content-center h-100 text-muted">
            <div class="text-center">
                <div class="spinner mb-3"></div>
                <p>Loading graph visualization...</p>
            </div>
        </div>
    `;
    
    // Simulate loading
    setTimeout(() => {
        container.innerHTML = `
            <div class="d-flex align-items-center justify-content-center h-100 text-muted">
                <div class="text-center">
                    <i class="fas fa-project-diagram fa-3x mb-3"></i>
                    <p>Graph visualization placeholder</p>
                    <small>Graph visualization will be implemented with a library like vis.js or d3.js</small>
                </div>
            </div>
        `;
    }, 2000);
}

// Utility functions
function showAlert(message, type = 'info') {
    // Create alert element
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // Add to page
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Health check
async function checkHealth() {
    try {
        const response = await fetch('/healthcheck');
        return response.ok;
    } catch (error) {
        return false;
    }
}

// Initialize health check
setInterval(async () => {
    const isHealthy = await checkHealth();
    // You could add a status indicator to the UI here
}, 30000); // Check every 30 seconds

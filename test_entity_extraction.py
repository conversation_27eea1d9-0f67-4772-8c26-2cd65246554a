#!/usr/bin/env python3
"""
Test entity extraction with OpenRouter.
"""

import asyncio
import logging
import os
from datetime import datetime, timezone
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_openrouter_entity_extraction():
    """Test entity extraction using OpenRouter."""
    try:
        from graphiti_core import Graphiti
        from graphiti_core.nodes import EpisodeType
        
        # Configuration
        neo4j_uri = 'bolt://localhost:7891'
        neo4j_user = 'neo4j'
        neo4j_password = 'Triathlon16'
        
        openai_api_key = os.getenv('OPENAI_API_KEY')
        openai_base_url = os.getenv('OPENAI_BASE_URL', 'https://openrouter.ai/api/v1')
        model_name = os.getenv('MODEL_NAME', 'meta-llama/llama-3.1-8b-instruct:free')
        
        logger.info(f"Testing entity extraction with OpenRouter")
        logger.info(f"Model: {model_name}")
        logger.info(f"Base URL: {openai_base_url}")
        logger.info(f"API Key: {openai_api_key[:10]}..." if openai_api_key else "API Key: NOT SET")
        
        # Create Graphiti instance
        graphiti = Graphiti(
            uri=neo4j_uri,
            user=neo4j_user,
            password=neo4j_password
        )
        
        # Configure OpenRouter
        if openai_base_url:
            graphiti.llm_client.config.base_url = openai_base_url
        if openai_api_key:
            graphiti.llm_client.config.api_key = openai_api_key
        if model_name:
            graphiti.llm_client.model = model_name
        
        logger.info("Graphiti configured, testing with simple medical text...")
        
        # Test with simple medical text
        test_text = "Vitamin D deficiency is common in SIBO patients. Probiotics like Lactobacillus can help restore gut health and improve digestion."
        
        logger.info(f"Processing text: {test_text}")
        
        # Create episode
        result = await graphiti.add_episode(
            name="Medical Entity Test",
            episode_body=test_text,
            source=EpisodeType.message,
            source_description="Test for medical entity extraction",
            reference_time=datetime.now(timezone.utc),
            group_id="entity_test"
        )
        
        logger.info(f"✅ Episode created successfully!")
        logger.info(f"  Episode UUID: {result.episode.uuid}")
        logger.info(f"  Entities extracted: {len(result.nodes) if result.nodes else 0}")
        logger.info(f"  Relationships created: {len(result.edges) if result.edges else 0}")
        
        if result.nodes:
            logger.info("  Extracted entities:")
            for i, node in enumerate(result.nodes):
                entity_type = getattr(node, 'entity_type', 'Unknown')
                logger.info(f"    {i+1}. {node.name} (Type: {entity_type})")
                logger.info(f"       Summary: {node.summary[:100]}..." if len(node.summary) > 100 else f"       Summary: {node.summary}")
        
        if result.edges:
            logger.info("  Extracted relationships:")
            for i, edge in enumerate(result.edges[:5]):  # Show first 5
                logger.info(f"    {i+1}. {edge.name}")
                logger.info(f"       Fact: {edge.fact[:100]}..." if len(edge.fact) > 100 else f"       Fact: {edge.fact}")
        
        # Clean up
        await graphiti.delete_group("entity_test")
        await graphiti.close()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Entity extraction test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_openrouter_entity_extraction())

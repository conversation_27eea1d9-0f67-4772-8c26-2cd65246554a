#!/usr/bin/env python3
"""
Test entity extraction with OpenRouter using the generic client instead of structured output.
"""

import asyncio
import logging
import os
from datetime import datetime, timezone
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_openrouter_generic_client():
    """Test entity extraction using OpenRouter with generic client."""
    try:
        from graphiti_core import Graphiti
        from graphiti_core.nodes import EpisodeType
        from graphiti_core.llm_client.openai_generic_client import OpenAIGenericClient
        from graphiti_core.llm_client.config import LLMConfig
        from graphiti_core.embedder.gemini import GeminiEmbedder, GeminiEmbedderConfig
        
        # Configuration
        neo4j_uri = 'bolt://localhost:7891'
        neo4j_user = 'neo4j'
        neo4j_password = 'Triathlon16'
        
        openai_api_key = os.getenv('OPENAI_API_KEY')
        openai_base_url = os.getenv('OPENAI_BASE_URL', 'https://openrouter.ai/api/v1')
        model_name = 'meta-llama/llama-3.1-8b-instruct:free'  # Use the better model
        google_api_key = os.getenv('GOOGLE_API_KEY')

        logger.info(f"Testing entity extraction with OpenRouter Generic Client + Gemini Embeddings")
        logger.info(f"LLM Model: {model_name}")
        logger.info(f"LLM Base URL: {openai_base_url}")
        logger.info(f"OpenRouter API Key: {openai_api_key[:10]}..." if openai_api_key else "OpenRouter API Key: NOT SET")
        logger.info(f"Google API Key: {google_api_key[:10]}..." if google_api_key else "Google API Key: NOT SET")

        # Create LLM config for generic client
        llm_config = LLMConfig(
            api_key=openai_api_key,
            base_url=openai_base_url,
            model=model_name,
            temperature=0.1,
            max_tokens=2000
        )

        # Create generic OpenAI client (doesn't use structured output)
        llm_client = OpenAIGenericClient(config=llm_config)

        # Create Gemini embedder
        embedder_config = GeminiEmbedderConfig(
            api_key=google_api_key,
            embedding_model="embedding-001"
        )
        embedder = GeminiEmbedder(config=embedder_config)

        # Create Graphiti instance with mixed clients
        graphiti = Graphiti(
            uri=neo4j_uri,
            user=neo4j_user,
            password=neo4j_password,
            llm_client=llm_client,
            embedder=embedder
        )
        
        logger.info("Graphiti configured with OpenRouter LLM + Gemini embeddings, testing with simple medical text...")
        
        # Test with simple medical text
        test_text = "Vitamin D deficiency is common in SIBO patients. Probiotics like Lactobacillus can help restore gut health and improve digestion."
        
        logger.info(f"Processing text: {test_text}")
        
        # Create episode
        result = await graphiti.add_episode(
            name="Medical Entity Test - Generic Client",
            episode_body=test_text,
            source=EpisodeType.message,
            source_description="Test for medical entity extraction with generic client",
            reference_time=datetime.now(timezone.utc),
            group_id="entity_test_generic"
        )
        
        logger.info(f"✅ Episode created successfully!")
        logger.info(f"  Episode UUID: {result.episode.uuid}")
        logger.info(f"  Entities extracted: {len(result.nodes) if result.nodes else 0}")
        logger.info(f"  Relationships created: {len(result.edges) if result.edges else 0}")
        
        if result.nodes:
            logger.info("  Extracted entities:")
            for i, node in enumerate(result.nodes):
                entity_type = getattr(node, 'entity_type', 'Unknown')
                logger.info(f"    {i+1}. {node.name} (Type: {entity_type})")
                logger.info(f"       Summary: {node.summary[:100]}..." if len(node.summary) > 100 else f"       Summary: {node.summary}")
        
        if result.edges:
            logger.info("  Extracted relationships:")
            for i, edge in enumerate(result.edges[:5]):  # Show first 5
                logger.info(f"    {i+1}. {edge.name}")
                logger.info(f"       Fact: {edge.fact[:100]}..." if len(edge.fact) > 100 else f"       Fact: {edge.fact}")
        
        # Clean up
        await graphiti.delete_group("entity_test_generic")
        await graphiti.close()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Entity extraction test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_openrouter_generic_client())

#!/usr/bin/env python3
"""
Test the fixed Ollama LLM client with improved JSON handling and anti-truncation measures.
"""

import asyncio
import logging
import os
import sys
from datetime import datetime, timezone
from dotenv import load_dotenv

# Add the server directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'server'))

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_fixed_ollama_llm():
    """Test the fixed Ollama LLM client with improved JSON handling."""
    try:
        from graphiti_core import Graphiti
        from graphiti_core.nodes import EpisodeType
        from server.graph_service.services.ollama_llm_client import OllamaLLMClient
        from server.graph_service.services.ollama_embedder import OllamaEmbedder
        from graphiti_core.llm_client.config import LLMConfig
        
        # Configuration
        neo4j_uri = 'bolt://localhost:7891'
        neo4j_user = 'neo4j'
        neo4j_password = 'Triathlon16'
        ollama_url = 'http://localhost:11434'
        
        logger.info(f"🔧 TESTING FIXED OLLAMA LLM CLIENT")
        logger.info(f"✅ Entity Extraction: Ollama MedGemma with improved JSON handling")
        logger.info(f"✅ Embeddings: Ollama Snowflake")
        logger.info(f"✅ Anti-truncation measures: Enabled")
        logger.info(f"✅ Spelling fixes: 'misssed_entities' -> 'missed_entities'")
        
        # Create Ollama LLM client with improved configuration
        llm_config = LLMConfig(
            model="alibayram/medgemma:latest",
            temperature=0.1,
            max_tokens=3000  # Increased for complex medical documents
        )
        llm_client = OllamaLLMClient(config=llm_config)
        
        # Create Ollama embedder
        embedder = OllamaEmbedder(
            base_url=ollama_url,
            model="snowflake-arctic-embed2:latest"
        )
        
        # Create Graphiti instance
        graphiti = Graphiti(
            uri=neo4j_uri,
            user=neo4j_user,
            password=neo4j_password,
            llm_client=llm_client,
            embedder=embedder
        )
        
        logger.info("🚀 Processing medical document with fixed LLM client...")
        
        # Medical document content that previously caused truncation issues
        document_text = """
        Patient presents with Small Intestinal Bacterial Overgrowth (SIBO) and vitamin D deficiency.
        
        Clinical Findings:
        - Serum 25-hydroxyvitamin D level: 18 ng/mL (deficient)
        - Hydrogen breath test positive for SIBO
        - Symptoms: bloating, abdominal pain, malabsorption
        
        Treatment Protocol:
        1. Antimicrobial therapy with rifaximin 550mg twice daily for 14 days
        2. Vitamin D3 supplementation 4000 IU daily
        3. Probiotic therapy with Lactobacillus acidophilus and Bifidobacterium bifidum
        4. Low FODMAP diet implementation
        
        Pathophysiology:
        SIBO disrupts the intestinal microbiome, leading to malabsorption of fat-soluble vitamins
        including vitamin D. The bacterial overgrowth damages intestinal villi, reducing
        absorption capacity. Probiotics help restore beneficial bacteria and improve
        intestinal barrier function.
        
        Follow-up:
        Repeat vitamin D levels in 8 weeks. Monitor SIBO symptoms and consider
        repeat breath testing if symptoms persist.
        """
        
        logger.info(f"📄 Document content: {len(document_text)} characters")
        
        # Process as episode
        result = await graphiti.add_episode(
            name="Medical Case: SIBO and Vitamin D - Fixed LLM Test",
            episode_body=document_text,
            source=EpisodeType.message,
            source_description="Test with fixed Ollama LLM client and improved JSON handling",
            reference_time=datetime.now(timezone.utc),
            group_id="fixed_llm_test"
        )
        
        logger.info(f"✅ DOCUMENT PROCESSED SUCCESSFULLY!")
        logger.info(f"📊 Results:")
        logger.info(f"  📝 Episode UUID: {result.episode.uuid}")
        logger.info(f"  🏷️  Entities extracted: {len(result.nodes) if result.nodes else 0}")
        logger.info(f"  🔗 Relationships created: {len(result.edges) if result.edges else 0}")
        
        if result.nodes:
            logger.info(f"🏷️  MEDICAL ENTITIES EXTRACTED:")
            for i, node in enumerate(result.nodes):
                entity_type = getattr(node, 'entity_type', 'Unknown')
                logger.info(f"    {i+1}. {node.name} (Type: {entity_type})")
                if hasattr(node, 'summary') and node.summary:
                    summary = node.summary[:80] + "..." if len(node.summary) > 80 else node.summary
                    logger.info(f"       Summary: {summary}")
        
        if result.edges:
            logger.info(f"🔗 MEDICAL RELATIONSHIPS EXTRACTED:")
            for i, edge in enumerate(result.edges[:5]):  # Show first 5
                logger.info(f"    {i+1}. {edge.name}")
                if hasattr(edge, 'fact') and edge.fact:
                    fact = edge.fact[:80] + "..." if len(edge.fact) > 80 else edge.fact
                    logger.info(f"       Fact: {fact}")
        
        # Clean up
        await graphiti.delete_group("fixed_llm_test")
        await graphiti.close()
        
        logger.info(f"🎉 SUCCESS: Fixed Ollama LLM client is working!")
        logger.info(f"💡 JSON truncation issues resolved!")
        logger.info(f"💡 Spelling issues fixed!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Fixed LLM test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run the fixed LLM test."""
    logger.info("=== TESTING FIXED OLLAMA LLM CLIENT ===")
    
    success = await test_fixed_ollama_llm()
    
    if success:
        logger.info("\n🎉 SUCCESS: Fixed Ollama LLM client is working!")
        logger.info("📋 Improvements implemented:")
        logger.info("  ✅ Anti-truncation measures")
        logger.info("  ✅ JSON completion for incomplete responses")
        logger.info("  ✅ Spelling fixes for 'misssed_entities'")
        logger.info("  ✅ Better error handling and recovery")
        logger.info("  ✅ Medical context optimization")
    else:
        logger.error("\n❌ FAILED: Fixed LLM client still has issues")

if __name__ == "__main__":
    asyncio.run(main())

#!/usr/bin/env python3
"""
Test script to verify OpenRouter integration with Maverick model and vision-based OCR.
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_openrouter_connection():
    """Test OpenRouter connection with Maverick model."""
    try:
        from server.graph_service.services.openrouter_service import OpenRouterService
        
        logger.info("🔄 Testing OpenRouter connection with Maverick model...")
        
        # Initialize OpenRouter service
        openrouter = OpenRouterService()
        
        # Check if service is available
        if not openrouter.is_available():
            logger.error("❌ OpenRouter service not available - check API key")
            return False
        
        # Test connection
        connection_test = await openrouter.test_connection()
        if connection_test:
            logger.info("✅ OpenRouter connection successful!")
            logger.info(f"   Model: {openrouter.model}")
            logger.info(f"   Base URL: {openrouter.base_url}")
            return True
        else:
            logger.error("❌ OpenRouter connection failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing OpenRouter: {str(e)}")
        return False

async def test_entity_extraction():
    """Test entity extraction with OpenRouter Maverick model."""
    try:
        from server.graph_service.services.openrouter_service import OpenRouterService
        
        logger.info("🔄 Testing entity extraction with Maverick model...")
        
        # Initialize service
        openrouter = OpenRouterService()
        
        # Test text for entity extraction
        test_text = """
        Vitamin C is an essential nutrient that supports immune function and collagen synthesis.
        Studies have shown that echinacea can help treat the common cold.
        Patients with diabetes should monitor their blood glucose levels regularly.
        Turmeric contains curcumin, which has anti-inflammatory properties.
        """
        
        # Extract entities
        entities = await openrouter.extract_entities(test_text)
        
        if entities:
            logger.info(f"✅ Entity extraction successful! Found {len(entities)} entities:")
            for entity in entities[:5]:  # Show first 5 entities
                logger.info(f"   - {entity['name']} ({entity['type']}) - Confidence: {entity['confidence']}")
            return True
        else:
            logger.warning("⚠️ No entities extracted")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing entity extraction: {str(e)}")
        return False

async def test_mistral_ocr():
    """Test Mistral OCR for vision-based PDF processing."""
    try:
        from server.graph_service.services.mistral_ocr_service import MistralOCRService
        
        logger.info("🔄 Testing Mistral OCR service...")
        
        # Initialize service
        mistral_ocr = MistralOCRService()
        
        # Test connection
        connection_test = await mistral_ocr.test_connection()
        if connection_test:
            logger.info("✅ Mistral OCR connection successful!")
            return True
        else:
            logger.warning("⚠️ Mistral OCR connection failed - check API key")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing Mistral OCR: {str(e)}")
        return False

async def test_document_processing_service():
    """Test the complete document processing service."""
    try:
        from server.graph_service.services.document_processing_service import DocumentProcessingService
        
        logger.info("🔄 Testing document processing service...")
        
        # Initialize service
        doc_service = DocumentProcessingService()
        
        # Get service status
        status = await doc_service.get_service_status()
        
        logger.info("📊 Service Status:")
        logger.info(f"   Pipeline Status: {status.get('pipeline_status', 'unknown')}")
        
        # OCR Service
        ocr_info = status.get('ocr_service', {})
        logger.info(f"   OCR Available: {ocr_info.get('available', False)}")
        logger.info(f"   OCR Provider: {ocr_info.get('provider', 'unknown')}")
        
        # Entity Extraction
        entity_info = status.get('entity_extraction', {})
        logger.info(f"   Entity Extraction Available: {entity_info.get('available', False)}")
        logger.info(f"   Entity Model: {entity_info.get('model', 'unknown')}")
        
        # Reference Extraction
        ref_info = status.get('reference_extraction', {})
        logger.info(f"   Reference Extraction Available: {ref_info.get('available', False)}")
        logger.info(f"   Reference Model: {ref_info.get('model', 'unknown')}")
        
        # Check if all services are ready
        if status.get('pipeline_status') == 'ready':
            logger.info("✅ Document processing service fully operational!")
            return True
        else:
            logger.warning("⚠️ Document processing service partially operational")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing document processing service: {str(e)}")
        return False

async def test_configuration():
    """Test environment configuration."""
    logger.info("🔄 Testing environment configuration...")
    
    # Check required environment variables
    required_vars = [
        'OPENROUTER_API_KEY',
        'MISTRAL_API_KEY',
        'ENTITY_EXTRACTION_MODEL',
        'OPENROUTER_ENTITY_MODEL'
    ]
    
    config_ok = True
    for var in required_vars:
        value = os.getenv(var)
        if value:
            logger.info(f"   ✅ {var}: {'*' * 10}...{value[-4:] if len(value) > 4 else value}")
        else:
            logger.error(f"   ❌ {var}: Not set")
            config_ok = False
    
    # Check model configuration
    entity_model = os.getenv('ENTITY_EXTRACTION_MODEL')
    openrouter_model = os.getenv('OPENROUTER_ENTITY_MODEL')
    
    if entity_model == 'meta-llama/llama-4-maverick:free':
        logger.info("   ✅ Entity extraction model: meta-llama/llama-4-maverick:free")
    else:
        logger.warning(f"   ⚠️ Entity extraction model: {entity_model} (expected: meta-llama/llama-4-maverick:free)")
    
    if openrouter_model == 'meta-llama/llama-4-maverick:free':
        logger.info("   ✅ OpenRouter model: meta-llama/llama-4-maverick:free")
    else:
        logger.warning(f"   ⚠️ OpenRouter model: {openrouter_model} (expected: meta-llama/llama-4-maverick:free)")
    
    # Check OCR configuration
    use_mistral = os.getenv('USE_MISTRAL_OCR', 'false').lower() == 'true'
    use_ollama_ocr = os.getenv('USE_OLLAMA_OCR', 'false').lower() == 'true'
    
    if use_mistral:
        logger.info("   ✅ Mistral OCR enabled for vision-based processing")
    else:
        logger.warning("   ⚠️ Mistral OCR disabled")
    
    if use_ollama_ocr:
        logger.info("   ✅ Ollama OCR enabled as fallback")
    else:
        logger.info("   ℹ️ Ollama OCR disabled")
    
    return config_ok

async def main():
    """Run all tests."""
    logger.info("🚀 Starting OpenRouter Maverick + Vision OCR Integration Test")
    logger.info("=" * 60)
    
    # Test configuration
    config_ok = await test_configuration()
    logger.info("")
    
    # Test OpenRouter connection
    openrouter_ok = await test_openrouter_connection()
    logger.info("")
    
    # Test entity extraction
    entity_ok = await test_entity_extraction()
    logger.info("")
    
    # Test Mistral OCR
    ocr_ok = await test_mistral_ocr()
    logger.info("")
    
    # Test document processing service
    service_ok = await test_document_processing_service()
    logger.info("")
    
    # Summary
    logger.info("📋 Test Summary:")
    logger.info(f"   Configuration: {'✅ PASS' if config_ok else '❌ FAIL'}")
    logger.info(f"   OpenRouter Connection: {'✅ PASS' if openrouter_ok else '❌ FAIL'}")
    logger.info(f"   Entity Extraction: {'✅ PASS' if entity_ok else '❌ FAIL'}")
    logger.info(f"   Mistral OCR: {'✅ PASS' if ocr_ok else '⚠️ PARTIAL'}")
    logger.info(f"   Document Service: {'✅ PASS' if service_ok else '⚠️ PARTIAL'}")
    
    overall_success = config_ok and openrouter_ok and entity_ok
    
    if overall_success:
        logger.info("🎉 Integration test PASSED! OpenRouter Maverick + Vision OCR ready!")
    else:
        logger.warning("⚠️ Integration test PARTIAL - some components may need attention")
    
    return overall_success

if __name__ == "__main__":
    asyncio.run(main())

#!/usr/bin/env python3
"""
Test Ollama Meditron as Primary OCR for SIBO Document Processing
"""

import requests
import time
import json

def test_ollama_primary_sibo():
    """Test Ollama Meditron as primary OCR for SIBO documents"""
    
    print("🔬 Testing Ollama Meditron as PRIMARY OCR for SIBO Documents...")
    
    # Wait for service to start
    print("\n⏳ Waiting for service to start...")
    time.sleep(10)
    
    # Step 1: Upload a comprehensive SIBO document
    try:
        print("\n📤 Step 1: Uploading comprehensive SIBO document...")
        
        # Create a detailed SIBO treatment protocol
        sibo_content = """
        COMPREHENSIVE SIBO TREATMENT PROTOCOL
        
        Patient Information:
        Name: <PERSON>
        Age: 42
        Date: June 11, 2024
        Physician: Dr. <PERSON>, MD
        
        CLINICAL PRESENTATION:
        Chief <PERSON><PERSON>laint: 
        "I've had severe bloating and gas for over a year. It's affecting my quality of life."
        
        History of Present Illness:
        Patient reports chronic abdominal bloating that worsens after meals, particularly those containing:
        - High FODMAP foods (onions, garlic, beans)
        - Dairy products
        - Wheat-based products
        - Certain fruits (apples, pears)
        
        Associated Symptoms:
        - Excessive gas production (hydrogen-dominant)
        - Alternating diarrhea and constipation
        - Postprandial abdominal cramping (7/10 severity)
        - Early satiety and food intolerances
        - Chronic fatigue, especially after meals
        - Brain fog and difficulty concentrating
        - Unintentional weight loss (8 lbs over 4 months)
        - Skin issues (mild rosacea)
        
        DIAGNOSTIC WORKUP:
        
        Lactulose Breath Test Results:
        - Baseline hydrogen: 3 ppm
        - 60-minute hydrogen: 28 ppm
        - 90-minute hydrogen: 52 ppm
        - 120-minute hydrogen: 48 ppm
        - Methane levels: 6 ppm (all timepoints)
        - Interpretation: Positive for hydrogen-dominant SIBO
        
        Laboratory Studies:
        - Complete Blood Count: Normal
        - Comprehensive Metabolic Panel: Normal
        - Vitamin B12: 245 pg/mL (low normal, 200-900)
        - Folate: 8.2 ng/mL (normal)
        - Iron studies: Ferritin 18 ng/mL (low, normal 15-150)
        - Vitamin D: 24 ng/mL (insufficient, optimal >30)
        - Inflammatory markers: CRP 1.2 mg/L (normal)
        
        Stool Analysis:
        - Comprehensive digestive stool analysis
        - Low beneficial bacteria (Lactobacillus, Bifidobacterium)
        - Elevated opportunistic bacteria
        - No parasites or pathogenic bacteria detected
        - Pancreatic elastase: Normal
        
        TREATMENT PROTOCOL:
        
        Phase 1: Antimicrobial Therapy (14-day course)
        
        Primary Option:
        - Rifaximin (Xifaxan) 550mg three times daily x 14 days
        - Total daily dose: 1650mg
        - Take with food to enhance absorption
        
        Alternative Herbal Protocol (if insurance denial):
        - Oregano oil (standardized to 70% carvacrol): 200mg twice daily
        - Berberine complex: 500mg three times daily
        - Allicin (stabilized garlic extract): 450mg twice daily
        - Neem extract: 300mg twice daily
        - Duration: 14-21 days
        
        Phase 2: Dietary Intervention (6-8 weeks)
        
        Low FODMAP Diet Implementation:
        Week 1-2: Strict elimination phase
        - Eliminate all high FODMAP foods
        - Focus on low FODMAP proteins, vegetables, and grains
        - Portion control to minimize fermentation substrate
        
        Week 3-6: Maintenance phase
        - Continue low FODMAP approach
        - Monitor symptom response
        - Consider elemental diet if symptoms persist
        
        Week 7-8: Systematic reintroduction
        - Gradual reintroduction of FODMAP groups
        - Identify specific trigger foods
        - Establish long-term dietary plan
        
        Approved Foods (Low FODMAP):
        - Proteins: Chicken, fish, eggs, tofu (firm)
        - Vegetables: Carrots, spinach, zucchini, bell peppers
        - Grains: Rice, quinoa, oats (small portions)
        - Fruits: Bananas, oranges, grapes (limited portions)
        - Fats: Olive oil, coconut oil, nuts (limited)
        
        Phase 3: Motility Enhancement
        
        Prokinetic Therapy:
        Primary: Motilium (domperidone) 10mg four times daily
        Alternative options:
        - Low-dose erythromycin: 50mg at bedtime
        - Prucalopride: 2mg daily (if available)
        
        Natural Prokinetics:
        - Ginger extract: 250mg twice daily
        - 5-HTP: 100mg at bedtime
        - Magnesium glycinate: 400mg at bedtime
        
        Phase 4: Microbiome Restoration (Start after antimicrobial phase)
        
        Targeted Probiotics:
        - Lactobacillus plantarum 299v: 10 billion CFU daily
        - Bifidobacterium infantis 35624: 1 billion CFU daily
        - Saccharomyces boulardii: 250mg twice daily
        - Soil-based organisms: Bacillus coagulans, B. subtilis
        
        Prebiotic Support (introduce gradually):
        - Partially hydrolyzed guar gum (PHGG): Start 5g daily
        - Acacia fiber: 5-10g daily
        - Resistant starch: Begin after 4 weeks (small amounts)
        
        Phase 5: Nutritional Support
        
        Digestive Support:
        - Comprehensive digestive enzymes with meals
        - Betaine HCl with pepsin (if hypochlorhydria suspected)
        - Ox bile extract (if fat malabsorption)
        
        Nutritional Supplementation:
        - Vitamin B12: 1000mcg sublingual daily
        - Iron bisglycinate: 25mg daily (with vitamin C)
        - Vitamin D3: 2000 IU daily
        - Omega-3 fatty acids: 1000mg daily
        
        Gut Healing Support:
        - L-glutamine: 5g twice daily
        - Zinc carnosine: 75mg twice daily
        - Slippery elm: 400mg before meals
        - Marshmallow root: 500mg twice daily
        
        MONITORING AND FOLLOW-UP:
        
        Week 2: Antimicrobial tolerance assessment
        - Review side effects
        - Assess symptom improvement
        - Adjust dosing if needed
        
        Week 4: Mid-treatment evaluation
        - Symptom severity scoring
        - Dietary compliance assessment
        - Nutritional status review
        
        Week 8: Comprehensive follow-up
        - Symptom improvement assessment
        - Weight and nutritional status
        - Consider repeat breath testing
        
        Week 12: Long-term management planning
        - Establish maintenance protocol
        - Address any persistent symptoms
        - Plan for recurrence prevention
        
        SUCCESS METRICS:
        - >70% reduction in bloating severity
        - Improved bowel movement consistency
        - Increased food tolerance
        - Weight stabilization or gain
        - Improved energy levels
        - Normalized breath test results
        
        PATIENT EDUCATION:
        
        Key Points Discussed:
        1. SIBO pathophysiology and contributing factors
        2. Importance of treatment compliance
        3. Dietary modification strategies
        4. Symptom tracking and monitoring
        5. When to contact healthcare provider
        6. Realistic expectations for treatment timeline
        7. Recurrence prevention strategies
        
        Warning Signs to Report:
        - Severe abdominal pain
        - Persistent vomiting
        - Signs of dehydration
        - Worsening symptoms during treatment
        - Allergic reactions to medications
        
        PROGNOSIS:
        Good with adherence to comprehensive treatment protocol.
        Expected improvement: 70-80% symptom reduction within 8 weeks.
        Recurrence risk: 20-30% within 12 months.
        Long-term management may be required for optimal outcomes.
        
        FOLLOW-UP PLAN:
        - Phone check-in: Week 1
        - Office visit: Week 4
        - Comprehensive evaluation: Week 8
        - Maintenance planning: Week 12
        - Annual breath test: Consider if symptoms recur
        
        Dr. Michael Chen, MD
        Gastroenterology Associates
        Board Certified in Gastroenterology
        License #: MD12345
        
        Patient signature: _________________ Date: ___________
        Physician signature: _________________ Date: ___________
        """
        
        files = {
            'file': ('comprehensive_sibo_protocol.txt', sibo_content, 'text/plain')
        }
        
        data = {
            'group_id': 'default',
            'upload_type': 'messages'
        }
        
        upload_response = requests.post(
            "http://localhost:8234/api/documents/upload",
            files=files,
            data=data,
            timeout=30
        )
        
        print(f"Upload Status: {upload_response.status_code}")
        print(f"Upload Response: {upload_response.text}")
        
        if upload_response.status_code == 202:
            print("✅ Upload successful!")
            
            # Step 2: Monitor processing progress
            print("\n⏳ Step 2: Monitoring Ollama Meditron processing...")
            
            for i in range(20):  # Check for 3+ minutes
                time.sleep(10)
                
                try:
                    status_response = requests.get(
                        "http://localhost:8234/api/processing/detailed-status/comprehensive_sibo_protocol.txt?group_id=default",
                        timeout=10
                    )
                    
                    if status_response.status_code == 200:
                        status_data = status_response.json()
                        processing_status = status_data.get('processing_status', 'unknown')
                        
                        print(f"Check {i+1}: Processing Status: {processing_status}")
                        
                        if processing_status == 'completed':
                            print("🎉 Processing completed successfully!")
                            print(f"📊 Results:")
                            print(f"  - Episodes: {status_data.get('episodes_count', 0)}")
                            print(f"  - Entities: {status_data.get('entities_count', 0)}")
                            print(f"  - Text Length: {status_data.get('text_length', 0)}")
                            print(f"  - OCR Status: {status_data.get('ocr_status', 'unknown')}")
                            print(f"  - Entity Status: {status_data.get('entity_extraction_status', 'unknown')}")
                            
                            # Check if we have actual content
                            if status_data.get('text_length', 0) > 0:
                                print("✅ OLLAMA MEDITRON OCR SUCCESSFUL!")
                                print("✅ Text extraction working!")
                                print("✅ Primary OCR system functional!")
                                
                                # Analyze the results
                                text_length = status_data.get('text_length', 0)
                                original_length = len(sibo_content)
                                
                                print(f"\n📈 Analysis:")
                                print(f"  - Original text: {original_length} characters")
                                print(f"  - Processed text: {text_length} characters")
                                print(f"  - Processing ratio: {text_length / original_length:.2f}x")
                                
                                if text_length >= original_length * 0.8:
                                    print("✅ Text preservation excellent!")
                                elif text_length >= original_length * 0.5:
                                    print("⚠️ Text preservation good")
                                else:
                                    print("❌ Significant text loss detected")
                                
                                # Check if entities were extracted
                                if status_data.get('entities_count', 0) > 0:
                                    print("✅ Entity extraction also working!")
                                    print(f"  - Extracted {status_data.get('entities_count', 0)} entities")
                                else:
                                    print("⚠️ No entities extracted - may need entity service check")
                                
                                return True
                            else:
                                print("❌ No text extracted - OCR may have failed")
                                return False
                                
                        elif processing_status == 'failed':
                            print(f"❌ Processing failed: {status_data.get('error_message', 'Unknown error')}")
                            return False
                        elif processing_status in ['processing', 'pending']:
                            print(f"⏳ Still processing... (attempt {i+1}/20)")
                        else:
                            print(f"❓ Unknown status: {processing_status}")
                    else:
                        print(f"❌ Status check failed: {status_response.status_code}")
                        
                except Exception as e:
                    print(f"❌ Status check error: {e}")
            
            print("⏰ Processing timeout - checking final status...")
            
        else:
            print(f"❌ Upload failed: {upload_response.status_code}")
            print(f"Error: {upload_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False
    
    return False

if __name__ == "__main__":
    print("🚀 Testing Ollama Meditron as Primary OCR for SIBO Documents")
    print("=" * 70)
    
    success = test_ollama_primary_sibo()
    
    print("\n" + "=" * 70)
    print("📊 FINAL RESULTS:")
    
    if success:
        print("🎉 OLLAMA MEDITRON PRIMARY OCR: ✅ SUCCESS!")
        print("✅ Your SIBO document processing system is working!")
        print("✅ Ollama Meditron is functioning as primary OCR!")
        print("✅ Medical document processing ready!")
        
        print("\n🚀 SYSTEM READY FOR SIBO RESEARCH!")
        print("📋 Current Configuration:")
        print("  - Primary OCR: Ollama Meditron (medical-focused)")
        print("  - Fallback OCR: Local PyMuPDF (always available)")
        print("  - No rate limits!")
        print("  - Medical text understanding")
        print("  - SIBO-specific processing")
        
    else:
        print("❌ OLLAMA MEDITRON PRIMARY OCR: ❌ FAILED")
        print("❌ System needs investigation")
    
    print("\n" + "=" * 70)

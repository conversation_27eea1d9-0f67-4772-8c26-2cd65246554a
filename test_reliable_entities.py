#!/usr/bin/env python3
"""
Test entity extraction with a smaller, more reliable model.
"""

import requests
import time
import json
from reportlab.lib.pagesizes import letter
from reportlab.pdfgen import canvas
import tempfile
import os

def create_minimal_medical_pdf():
    """Create a minimal medical PDF for testing."""
    try:
        # Create temporary file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
        temp_path = temp_file.name
        temp_file.close()
        
        # Create PDF with minimal medical content
        c = canvas.Canvas(temp_path, pagesize=letter)
        width, height = letter
        
        # Title
        c.setFont("Helvetica-Bold", 16)
        c.drawString(50, height - 50, "Medical Note: SIBO")
        
        # Content
        c.setFont("Helvetica", 12)
        y_position = height - 100
        
        medical_content = [
            "Patient: <PERSON>",
            "",
            "Diagnosis: SIBO",
            "",
            "Treatment: Rifaximin",
            "",
            "Symptoms: Bloating, pain"
        ]
        
        for line in medical_content:
            c.drawString(50, y_position, line)
            y_position -= 20
        
        c.save()
        print(f"✅ Created minimal medical PDF: {temp_path}")
        return temp_path
        
    except Exception as e:
        print(f"❌ Error creating PDF: {str(e)}")
        return None

def test_reliable_entities():
    """Test entity extraction with reliable model."""
    print("🔧 TESTING RELIABLE ENTITY EXTRACTION")
    print("=" * 60)
    print("✅ Using llama3.1:8b (smaller, more reliable)")
    print("✅ Minimal content for faster processing")
    print("✅ Simplified configuration")
    print()
    
    # Create test PDF
    pdf_path = create_minimal_medical_pdf()
    if not pdf_path:
        return False
    
    try:
        print("📤 Uploading minimal medical document...")
        
        with open(pdf_path, 'rb') as f:
            files = {'file': ('minimal_medical_note.pdf', f, 'application/pdf')}
            data = {
                'group_id': 'reliable_test',
                'upload_type': 'messages'
            }
            
            # Upload document
            response = requests.post(
                'http://127.0.0.1:8234/api/documents/upload',
                files=files,
                data=data,
                timeout=120  # 2 minutes
            )
        
        print(f"📊 Upload Response Status: {response.status_code}")
        
        if response.status_code == 202:
            result = response.json()
            print("✅ DOCUMENT UPLOAD ACCEPTED!")
            print(f"📄 Filename: {result.get('filename', 'Unknown')}")
            print(f"📝 Status: {result.get('status', 'Unknown')}")
            print()
            print("⏳ Processing with reliable model...")
            print("📋 Expected entities: SIBO, Rifaximin, John Smith")
            print("🔍 Monitor server logs for:")
            print("  - Successful Ollama API response")
            print("  - Entity extraction results")
            print("  - No empty response errors")
            
            return True
        else:
            print(f"❌ Upload failed with status {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("⏰ Upload timed out")
        print("🔄 Check server logs for processing status")
        return True  # Timeout doesn't mean failure
        
    except Exception as e:
        print(f"❌ Upload error: {str(e)}")
        return False
        
    finally:
        # Clean up
        try:
            os.unlink(pdf_path)
        except:
            pass

def check_server_status():
    """Check if the server is running."""
    try:
        response = requests.get('http://127.0.0.1:8234/api/documents/services/status', timeout=5)
        if response.status_code == 200:
            print("✅ Server is running and healthy")
            return True
        else:
            print(f"⚠️  Server responded with status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Server not accessible: {str(e)}")
        print("💡 Make sure the server is running on port 8234")
        return False

if __name__ == "__main__":
    print("🧪 TESTING RELIABLE ENTITY EXTRACTION")
    print("🤖 Entity Extraction: llama3.1:8b (reliable)")
    print("🔢 Embeddings: Ollama Snowflake")
    print("🔧 Minimal content for testing")
    print()
    
    # Check server first
    if not check_server_status():
        exit(1)
    
    # Test upload
    success = test_reliable_entities()
    
    if success:
        print("\n🎉 UPLOAD SUCCESSFUL!")
        print("✅ Document submitted for reliable processing!")
        print("📋 Monitor server logs to verify:")
        print("  ✅ Ollama API responds successfully")
        print("  ✅ Entities extracted from minimal content")
        print("  ✅ No empty response errors")
        print("  ✅ Basic entity extraction working")
    else:
        print("\n❌ Upload failed - check server logs for details")

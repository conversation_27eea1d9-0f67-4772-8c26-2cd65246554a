#!/usr/bin/env python3
"""
Test local OCR functionality to verify it's working properly
"""

import requests
import time
import json

def test_local_ocr():
    """Test the local OCR functionality"""
    
    print("🔍 Testing Local OCR Functionality...")
    
    # Wait for service to start
    print("\n⏳ Waiting for service to start...")
    time.sleep(10)
    
    # Step 1: Check service status
    try:
        print("\n📊 Step 1: Checking service status...")
        status_response = requests.get("http://localhost:8234/api/processing/service-status", timeout=10)
        
        if status_response.status_code == 200:
            status_data = status_response.json()
            print("✅ Service status retrieved!")
            print(f"📈 OCR Service:")
            print(f"  - Available: {status_data.get('ocr_service', {}).get('available', False)}")
            print(f"  - Provider: {status_data.get('ocr_service', {}).get('provider', 'Unknown')}")
            print(f"  - Configuration: {status_data.get('ocr_service', {}).get('configuration', 'Unknown')}")
            print(f"  - Pipeline Status: {status_data.get('pipeline_status', 'Unknown')}")
        else:
            print(f"❌ Service status failed: {status_response.status_code}")
            
    except Exception as e:
        print(f"❌ Service status error: {e}")
    
    # Step 2: Upload a test document
    try:
        print("\n📤 Step 2: Uploading test document...")
        
        # Create a comprehensive SIBO test document
        sibo_content = """
        SIBO Treatment Protocol - Comprehensive Guide
        
        Small Intestinal Bacterial Overgrowth (SIBO) is a condition characterized by excessive bacterial growth in the small intestine.
        
        SYMPTOMS:
        - Bloating and abdominal distension
        - Gas and flatulence
        - Diarrhea or constipation
        - Abdominal pain and cramping
        - Malabsorption of nutrients
        - Fatigue and brain fog
        - Food intolerances
        
        CAUSES:
        1. Reduced gastric acid production (hypochlorhydria)
        2. Impaired intestinal motility (gastroparesis)
        3. Structural abnormalities (strictures, adhesions)
        4. Immune system dysfunction
        5. Medications (PPIs, antibiotics)
        6. Chronic stress
        
        DIAGNOSTIC TESTS:
        - Lactulose breath test (gold standard)
        - Glucose breath test
        - Small bowel aspirate and culture
        - Comprehensive stool analysis
        
        TREATMENT APPROACHES:
        
        1. ANTIBIOTIC THERAPY:
           - Rifaximin (Xifaxan): 550mg TID x 14 days
           - Metronidazole: 250mg TID x 10 days
           - Neomycin: 500mg BID x 14 days (for methane)
        
        2. HERBAL ANTIMICROBIALS:
           - Oregano oil: 200mg BID
           - Berberine: 500mg TID
           - Allicin (garlic): 450mg BID
           - Neem extract: 300mg BID
        
        3. DIETARY MODIFICATIONS:
           - Low FODMAP diet
           - Specific Carbohydrate Diet (SCD)
           - Elemental diet (2-3 weeks)
           - Intermittent fasting
        
        4. PROKINETIC AGENTS:
           - Motilium (domperidone): 10mg QID
           - Erythromycin: 50mg at bedtime
           - Prucalopride: 2mg daily
        
        5. PROBIOTICS:
           - Lactobacillus plantarum
           - Bifidobacterium infantis
           - Saccharomyces boulardii
        
        MONITORING AND FOLLOW-UP:
        - Symptom tracking diary
        - Repeat breath testing at 3 months
        - Nutritional status assessment
        - Microbiome analysis
        
        This comprehensive approach helps restore gut health and reduce bacterial overgrowth effectively.
        """
        
        files = {
            'file': ('sibo_comprehensive_guide.txt', sibo_content, 'text/plain')
        }
        
        data = {
            'group_id': 'default',
            'upload_type': 'messages'
        }
        
        upload_response = requests.post(
            "http://localhost:8234/api/documents/upload",
            files=files,
            data=data,
            timeout=30
        )
        
        print(f"Upload Status: {upload_response.status_code}")
        print(f"Upload Response: {upload_response.text}")
        
        if upload_response.status_code == 202:
            print("✅ Upload successful!")
            
            # Step 3: Monitor processing progress
            print("\n⏳ Step 3: Monitoring processing progress...")
            
            for i in range(12):  # Check for 2 minutes
                time.sleep(10)
                
                try:
                    status_response = requests.get(
                        "http://localhost:8234/api/processing/detailed-status/sibo_comprehensive_guide.txt?group_id=default",
                        timeout=10
                    )
                    
                    if status_response.status_code == 200:
                        status_data = status_response.json()
                        processing_status = status_data.get('processing_status', 'unknown')
                        
                        print(f"Check {i+1}: Processing Status: {processing_status}")
                        
                        if processing_status == 'completed':
                            print("🎉 Processing completed successfully!")
                            print(f"📊 Results:")
                            print(f"  - Episodes: {status_data.get('episodes_count', 0)}")
                            print(f"  - Entities: {status_data.get('entities_count', 0)}")
                            print(f"  - Text Length: {status_data.get('text_length', 0)}")
                            print(f"  - OCR Status: {status_data.get('ocr_status', 'unknown')}")
                            print(f"  - Entity Status: {status_data.get('entity_extraction_status', 'unknown')}")
                            
                            # Check if we have actual content
                            if status_data.get('text_length', 0) > 0:
                                print("✅ LOCAL OCR IS WORKING PERFECTLY!")
                                print("✅ Text extraction successful!")
                                print("✅ Processing pipeline functional!")
                                return True
                            else:
                                print("❌ No text extracted - OCR may have failed")
                                return False
                                
                        elif processing_status == 'failed':
                            print(f"❌ Processing failed: {status_data.get('error_message', 'Unknown error')}")
                            return False
                        elif processing_status in ['processing', 'pending']:
                            print(f"⏳ Still processing... (attempt {i+1}/12)")
                        else:
                            print(f"❓ Unknown status: {processing_status}")
                    else:
                        print(f"❌ Status check failed: {status_response.status_code}")
                        
                except Exception as e:
                    print(f"❌ Status check error: {e}")
            
            print("⏰ Processing timeout - checking final status...")
            
        else:
            print(f"❌ Upload failed: {upload_response.status_code}")
            print(f"Error: {upload_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False
    
    return False

if __name__ == "__main__":
    success = test_local_ocr()
    if success:
        print("\n🎉 LOCAL OCR TEST PASSED!")
        print("✅ Your system is ready for SIBO document processing!")
    else:
        print("\n❌ LOCAL OCR TEST FAILED!")
        print("❌ OCR processing needs investigation")

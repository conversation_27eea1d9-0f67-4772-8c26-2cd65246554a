#!/usr/bin/env python3
"""
Test different PDF extraction methods to see which one works best.
"""

import tempfile
import os

def create_test_pdf():
    """Create a test PDF with medical content."""
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter
        
        # Create temporary PDF
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
        temp_path = temp_file.name
        temp_file.close()
        
        # Create PDF with medical content
        c = canvas.Canvas(temp_path, pagesize=letter)
        
        # Add content
        c.drawString(100, 750, "MEDICAL REPORT")
        c.drawString(100, 720, "Patient: <PERSON>")
        c.drawString(100, 690, "Date: 2024-06-13")
        c.drawString(100, 660, "Diagnosis: SIBO (Small Intestinal Bacterial Overgrowth)")
        c.drawString(100, 630, "Treatment: Rifaximin 550mg twice daily for 14 days")
        c.drawString(100, 600, "Probiotics: Lactobacillus acidophilus 10 billion CFU")
        c.drawString(100, 570, "Diet: Low-FODMAP diet with reduced fermentable carbs")
        c.drawString(100, 540, "Supplements: Vitamin B12 1000mcg, Vitamin D3 2000 IU")
        c.drawString(100, 510, "Follow-up: Breath test in 4 weeks to assess response")
        c.drawString(100, 480, "Dr. Sarah Johnson, MD - Gastroenterology Department")
        
        c.save()
        print(f"✅ Created test PDF: {temp_path}")
        return temp_path
        
    except ImportError:
        print("❌ reportlab not available")
        return None
    except Exception as e:
        print(f"❌ Error creating PDF: {str(e)}")
        return None

def test_pymupdf_extraction(pdf_path):
    """Test PyMuPDF extraction."""
    try:
        import fitz
        
        with open(pdf_path, 'rb') as f:
            content = f.read()
        
        doc = fitz.open(stream=content, filetype="pdf")
        text = ""
        for page in doc:
            text += page.get_text()
        doc.close()
        
        print(f"✅ PyMuPDF extraction: {len(text)} characters")
        print(f"   Text: '{text[:100]}...'")
        return text
        
    except ImportError:
        print("❌ PyMuPDF not available")
        return None
    except Exception as e:
        print(f"❌ PyMuPDF failed: {str(e)}")
        return None

def test_pypdf2_extraction(pdf_path):
    """Test PyPDF2 extraction."""
    try:
        import PyPDF2
        import io
        
        with open(pdf_path, 'rb') as f:
            content = f.read()
        
        pdf_reader = PyPDF2.PdfReader(io.BytesIO(content))
        text = ""
        for page in pdf_reader.pages:
            text += page.extract_text()
        
        print(f"✅ PyPDF2 extraction: {len(text)} characters")
        print(f"   Text: '{text[:100]}...'")
        return text
        
    except ImportError:
        print("❌ PyPDF2 not available")
        return None
    except Exception as e:
        print(f"❌ PyPDF2 failed: {str(e)}")
        return None

def test_google_vision_extraction(pdf_path):
    """Test Google Vision API extraction."""
    try:
        import google.generativeai as genai
        from PIL import Image
        import fitz  # To convert PDF to image
        import io
        import os
        
        api_key = os.getenv('GOOGLE_API_KEY')
        if not api_key:
            print("❌ GOOGLE_API_KEY not found")
            return None
        
        # Configure API
        genai.configure(api_key=api_key)
        
        # Convert PDF to image
        doc = fitz.open(pdf_path)
        page = doc[0]  # First page
        pix = page.get_pixmap()
        img_data = pix.tobytes("png")
        doc.close()
        
        # Convert to PIL Image
        image = Image.open(io.BytesIO(img_data))
        
        # Use Gemini 1.5 Flash
        model = genai.GenerativeModel('gemini-1.5-flash')
        
        prompt = """
Extract all text from this medical document image. 
Focus on accurate extraction of medical terminology, patient information, and treatment details.
Return only the extracted text without any additional commentary.
"""
        
        response = model.generate_content([prompt, image])
        
        if response.text:
            text = response.text.strip()
            print(f"✅ Google Vision extraction: {len(text)} characters")
            print(f"   Text: '{text[:100]}...'")
            return text
        else:
            print("❌ Google Vision returned no text")
            return None
            
    except ImportError:
        print("❌ Required packages not available for Google Vision")
        return None
    except Exception as e:
        print(f"❌ Google Vision failed: {str(e)}")
        return None

def main():
    """Test all PDF extraction methods."""
    print("🚀 Testing PDF Extraction Methods")
    print("=" * 40)
    
    # Create test PDF
    pdf_path = create_test_pdf()
    if not pdf_path:
        print("❌ Cannot create test PDF")
        return
    
    try:
        print("\n🔍 Testing extraction methods:")
        print("-" * 30)
        
        # Test PyMuPDF
        pymupdf_text = test_pymupdf_extraction(pdf_path)
        print("")
        
        # Test PyPDF2
        pypdf2_text = test_pypdf2_extraction(pdf_path)
        print("")
        
        # Test Google Vision
        google_text = test_google_vision_extraction(pdf_path)
        print("")
        
        # Summary
        print("📋 Extraction Results Summary:")
        print(f"   PyMuPDF: {len(pymupdf_text) if pymupdf_text else 0} chars")
        print(f"   PyPDF2: {len(pypdf2_text) if pypdf2_text else 0} chars")
        print(f"   Google Vision: {len(google_text) if google_text else 0} chars")
        
        # Determine best method
        results = [
            ("PyMuPDF", pymupdf_text),
            ("PyPDF2", pypdf2_text),
            ("Google Vision", google_text)
        ]
        
        best_method = None
        best_length = 0
        
        for method, text in results:
            if text and len(text) > best_length:
                best_method = method
                best_length = len(text)
        
        if best_method:
            print(f"\n🏆 Best method: {best_method} ({best_length} characters)")
        else:
            print("\n❌ No extraction method worked")
        
    finally:
        # Clean up
        if os.path.exists(pdf_path):
            os.unlink(pdf_path)

if __name__ == "__main__":
    main()

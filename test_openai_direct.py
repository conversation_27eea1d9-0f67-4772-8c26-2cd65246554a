#!/usr/bin/env python3
"""
Test script to verify OpenAI API access directly.
"""

import requests
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_openai_direct():
    """Test OpenAI API directly."""
    
    api_key = os.getenv('OPENAI_API_KEY')
    base_url = os.getenv('OPENAI_BASE_URL', 'https://api.openai.com/v1')
    model = os.getenv('MODEL_NAME', 'gpt-4o')
    
    print(f"Testing OpenAI API directly:")
    print(f"  Base URL: {base_url}")
    print(f"  Model: {model}")
    print(f"  API Key: {api_key[:10]}..." if api_key else "  API Key: None")
    
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    # Simple test prompt
    prompt = "Extract entities from this text: 'Vitamin D deficiency is common in SIBO patients.' Return JSON with entities array."
    
    payload = {
        'model': model,
        'messages': [
            {
                'role': 'user',
                'content': prompt
            }
        ],
        'max_tokens': 500,
        'temperature': 0.1
    }
    
    print("\nMaking API call to OpenAI...")
    
    try:
        response = requests.post(
            f"{base_url}/chat/completions",
            headers=headers,
            json=payload,
            timeout=30
        )
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            if 'choices' in result and len(result['choices']) > 0:
                content = result['choices'][0]['message']['content']
                print(f"✅ SUCCESS: OpenAI API call successful!")
                print(f"Response content: {content}")
                return True
            else:
                print(f"❌ No choices in response: {result}")
                return False
                
        elif response.status_code == 401:
            print(f"❌ AUTHENTICATION ERROR: Invalid API key")
            print(f"The API key appears to be invalid for OpenAI")
            return False
        elif response.status_code == 403:
            print(f"❌ PERMISSION ERROR: API key doesn't have access")
            return False
        else:
            print(f"❌ API call failed: {response.status_code}")
            print(f"Error response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("Starting OpenAI Direct API Test")
    success = test_openai_direct()
    
    print(f"\n=== RESULT ===")
    if success:
        print("✅ OpenAI API is working correctly!")
    else:
        print("❌ OpenAI API has issues!")
        print("This suggests the API key is not valid for OpenAI direct access.")
        print("You may need a proper OpenAI API key, or we should use OpenRouter.")

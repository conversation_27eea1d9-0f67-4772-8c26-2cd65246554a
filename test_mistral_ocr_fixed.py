#!/usr/bin/env python3
"""
Test the fixed Mistral OCR with proper document format
"""

import requests
import time
import json

def test_mistral_ocr_fixed():
    """Test the fixed Mistral OCR functionality"""
    
    print("🔍 Testing Fixed Mistral OCR Functionality...")
    
    # Wait for service to start
    print("\n⏳ Waiting for service to start...")
    time.sleep(10)
    
    # Step 1: Check service status
    try:
        print("\n📊 Step 1: Checking service status...")
        status_response = requests.get("http://localhost:8234/api/processing/service-status", timeout=10)
        
        if status_response.status_code == 200:
            status_data = status_response.json()
            print("✅ Service status retrieved!")
            print(f"📈 OCR Service:")
            print(f"  - Available: {status_data.get('ocr_service', {}).get('available', False)}")
            print(f"  - Provider: {status_data.get('ocr_service', {}).get('provider', 'Unknown')}")
            print(f"  - Configuration: {status_data.get('ocr_service', {}).get('configuration', 'Unknown')}")
            print(f"  - Pipeline Status: {status_data.get('pipeline_status', 'Unknown')}")
        else:
            print(f"❌ Service status failed: {status_response.status_code}")
            
    except Exception as e:
        print(f"❌ Service status error: {e}")
    
    # Step 2: Upload a test document
    try:
        print("\n📤 Step 2: Uploading test document...")
        
        # Create a comprehensive SIBO test document
        sibo_content = """
        SIBO Treatment Protocol - Advanced Clinical Guide
        
        Small Intestinal Bacterial Overgrowth (SIBO) is a complex gastrointestinal condition requiring comprehensive treatment.
        
        CLINICAL PRESENTATION:
        Primary Symptoms:
        - Chronic bloating and abdominal distension
        - Excessive gas production (hydrogen/methane)
        - Alternating diarrhea and constipation
        - Postprandial abdominal pain
        - Malabsorption syndrome
        - Nutritional deficiencies (B12, iron, fat-soluble vitamins)
        
        Secondary Manifestations:
        - Chronic fatigue and brain fog
        - Food intolerances and sensitivities
        - Skin conditions (rosacea, eczema)
        - Joint pain and inflammation
        - Mood disorders (anxiety, depression)
        
        PATHOPHYSIOLOGY:
        Root Causes:
        1. Hypochlorhydria (reduced gastric acid)
        2. Gastroparesis and motility disorders
        3. Structural abnormalities (strictures, adhesions)
        4. Immune dysfunction and inflammation
        5. Medication-induced dysbiosis (PPIs, antibiotics)
        6. Chronic stress and HPA axis dysfunction
        
        DIAGNOSTIC APPROACH:
        Gold Standard Tests:
        - Lactulose breath test (3-hour protocol)
        - Glucose breath test (2-hour protocol)
        - Small bowel aspirate and culture (invasive)
        
        Supportive Diagnostics:
        - Comprehensive stool analysis with SIBO markers
        - Organic acids test (OAT)
        - Food sensitivity panels
        - Nutritional status assessment
        
        TREATMENT PROTOCOLS:
        
        Phase 1: Antimicrobial Therapy
        Pharmaceutical Options:
        - Rifaximin (Xifaxan): 550mg TID x 14 days
        - Metronidazole: 250mg TID x 10-14 days
        - Neomycin: 500mg BID x 14 days (methane-dominant)
        - Combination therapy for resistant cases
        
        Herbal Antimicrobials:
        - Oregano oil (carvacrol): 200mg BID
        - Berberine complex: 500mg TID
        - Allicin (stabilized garlic): 450mg BID
        - Neem extract: 300mg BID
        - Goldenseal: 250mg TID
        
        Phase 2: Dietary Interventions
        Elimination Protocols:
        - Low FODMAP diet (4-6 weeks)
        - Specific Carbohydrate Diet (SCD)
        - Elemental diet (2-3 weeks for severe cases)
        - Biphasic diet protocol
        
        Nutritional Support:
        - Digestive enzymes with meals
        - Betaine HCl for hypochlorhydria
        - L-glutamine for intestinal healing
        - Zinc carnosine for mucosal repair
        
        Phase 3: Motility Enhancement
        Prokinetic Agents:
        - Motilium (domperidone): 10mg QID
        - Erythromycin: 50mg at bedtime
        - Prucalopride: 2mg daily
        - Metoclopramide: 5-10mg TID (short-term)
        
        Natural Prokinetics:
        - Ginger extract: 250mg BID
        - 5-HTP: 100mg at bedtime
        - Magnesium glycinate: 400mg at bedtime
        
        Phase 4: Microbiome Restoration
        Targeted Probiotics:
        - Lactobacillus plantarum 299v
        - Bifidobacterium infantis 35624
        - Saccharomyces boulardii
        - Soil-based organisms (Bacillus species)
        
        Prebiotic Support:
        - Partially hydrolyzed guar gum (PHGG)
        - Acacia fiber
        - Resistant starch (gradual introduction)
        
        MONITORING AND FOLLOW-UP:
        Assessment Tools:
        - Symptom tracking diary
        - Repeat breath testing at 3-6 months
        - Nutritional status monitoring
        - Microbiome analysis
        - Quality of life questionnaires
        
        Success Metrics:
        - >50% reduction in symptom severity
        - Normalized breath test results
        - Improved nutritional markers
        - Enhanced quality of life scores
        
        COMPLICATIONS AND MANAGEMENT:
        Potential Issues:
        - Treatment resistance
        - Recurrence patterns
        - Antibiotic-associated complications
        - Nutritional deficiencies
        
        Advanced Interventions:
        - Fecal microbiota transplantation (FMT)
        - Immunomodulatory therapy
        - Surgical intervention (rare cases)
        
        This comprehensive protocol provides a systematic approach to SIBO management with evidence-based interventions.
        """
        
        files = {
            'file': ('sibo_advanced_protocol.txt', sibo_content, 'text/plain')
        }
        
        data = {
            'group_id': 'default',
            'upload_type': 'messages'
        }
        
        upload_response = requests.post(
            "http://localhost:8234/api/documents/upload",
            files=files,
            data=data,
            timeout=30
        )
        
        print(f"Upload Status: {upload_response.status_code}")
        print(f"Upload Response: {upload_response.text}")
        
        if upload_response.status_code == 202:
            print("✅ Upload successful!")
            
            # Step 3: Monitor processing progress
            print("\n⏳ Step 3: Monitoring processing progress...")
            
            for i in range(15):  # Check for 2.5 minutes
                time.sleep(10)
                
                try:
                    status_response = requests.get(
                        "http://localhost:8234/api/processing/detailed-status/sibo_advanced_protocol.txt?group_id=default",
                        timeout=10
                    )
                    
                    if status_response.status_code == 200:
                        status_data = status_response.json()
                        processing_status = status_data.get('processing_status', 'unknown')
                        
                        print(f"Check {i+1}: Processing Status: {processing_status}")
                        
                        if processing_status == 'completed':
                            print("🎉 Processing completed successfully!")
                            print(f"📊 Results:")
                            print(f"  - Episodes: {status_data.get('episodes_count', 0)}")
                            print(f"  - Entities: {status_data.get('entities_count', 0)}")
                            print(f"  - Text Length: {status_data.get('text_length', 0)}")
                            print(f"  - OCR Status: {status_data.get('ocr_status', 'unknown')}")
                            print(f"  - Entity Status: {status_data.get('entity_extraction_status', 'unknown')}")
                            
                            # Check if we have actual content
                            if status_data.get('text_length', 0) > 0:
                                print("✅ MISTRAL OCR IS WORKING PERFECTLY!")
                                print("✅ Text extraction successful!")
                                print("✅ Processing pipeline functional!")
                                
                                # Check if entities were extracted
                                if status_data.get('entities_count', 0) > 0:
                                    print("✅ Entity extraction also working!")
                                else:
                                    print("⚠️ No entities extracted - may be rate limited")
                                
                                return True
                            else:
                                print("❌ No text extracted - OCR may have failed")
                                return False
                                
                        elif processing_status == 'failed':
                            print(f"❌ Processing failed: {status_data.get('error_message', 'Unknown error')}")
                            return False
                        elif processing_status in ['processing', 'pending']:
                            print(f"⏳ Still processing... (attempt {i+1}/15)")
                        else:
                            print(f"❓ Unknown status: {processing_status}")
                    else:
                        print(f"❌ Status check failed: {status_response.status_code}")
                        
                except Exception as e:
                    print(f"❌ Status check error: {e}")
            
            print("⏰ Processing timeout - checking final status...")
            
        else:
            print(f"❌ Upload failed: {upload_response.status_code}")
            print(f"Error: {upload_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False
    
    return False

if __name__ == "__main__":
    success = test_mistral_ocr_fixed()
    if success:
        print("\n🎉 MISTRAL OCR TEST PASSED!")
        print("✅ Your system is ready for advanced SIBO document processing!")
    else:
        print("\n❌ MISTRAL OCR TEST FAILED!")
        print("❌ OCR processing needs further investigation")

#!/usr/bin/env python3
"""
Quick test to verify Neo4j connection is fixed
"""

import requests
import time

def test_neo4j_fix():
    """Test if the Neo4j connection issue is resolved"""
    
    print("🔧 TESTING NEO4J CONNECTION FIX")
    print("=" * 50)
    
    # Wait for service to be ready
    time.sleep(10)
    
    # Test with a small document
    try:
        print("\n📤 Uploading small test document...")
        
        test_content = """
        SIBO Test Document
        
        Small Intestinal Bacterial Overgrowth (SIBO) is a condition where bacteria overgrow in the small intestine.
        
        Common symptoms include:
        - Bloating
        - Gas
        - Diarrhea
        - Abdominal pain
        
        Treatment options:
        - Rifaximin antibiotic therapy
        - Low FODMAP diet
        - Prokinetic agents
        
        References:
        1. <PERSON><PERSON><PERSON>, M<PERSON> et al. (2020). ACG Clinical Guideline: Small Intestinal Bacterial Overgrowth. American Journal of Gastroenterology, 115(2), 165-178.
        """
        
        files = {
            'file': ('sibo_neo4j_test.txt', test_content, 'text/plain')
        }
        
        data = {
            'group_id': 'default',
            'upload_type': 'messages'
        }
        
        upload_response = requests.post(
            "http://localhost:8234/api/documents/upload",
            files=files,
            data=data,
            timeout=30
        )
        
        print(f"Upload Status: {upload_response.status_code}")
        
        if upload_response.status_code == 202:
            print("✅ Upload successful!")
            
            # Monitor for Neo4j connection errors
            print("\n⏳ Monitoring for Neo4j connection issues...")
            
            for i in range(10):  # Monitor for 2.5 minutes
                time.sleep(15)
                
                try:
                    status_response = requests.get(
                        "http://localhost:8234/api/processing/detailed-status/sibo_neo4j_test.txt?group_id=default",
                        timeout=15
                    )
                    
                    if status_response.status_code == 200:
                        status_data = status_response.json()
                        processing_status = status_data.get('processing_status', 'unknown')
                        
                        print(f"Check {i+1}: Status: {processing_status}")
                        
                        if processing_status == 'completed':
                            print("🎉 SUCCESS! Neo4j connection is working!")
                            print(f"📊 Results:")
                            print(f"  - Text Length: {status_data.get('text_length', 0)}")
                            print(f"  - Entities: {status_data.get('entities_count', 0)}")
                            print(f"  - References: {status_data.get('references_count', 0)}")
                            return True
                            
                        elif processing_status == 'failed':
                            error_msg = status_data.get('error_message', 'Unknown error')
                            print(f"❌ Processing failed: {error_msg}")
                            
                            if "Rate limit exceeded" in error_msg:
                                print("❌ Still getting the fake 'Rate limit exceeded' error")
                                print("❌ This means Neo4j connection is still failing")
                                return False
                            else:
                                print("✅ No more 'Rate limit exceeded' errors!")
                                print("✅ Neo4j connection issue is FIXED!")
                                return True
                                
                        elif processing_status in ['processing', 'pending']:
                            print(f"⏳ Still processing... (attempt {i+1}/10)")
                        else:
                            print(f"❓ Unknown status: {processing_status}")
                    else:
                        print(f"❌ Status check failed: {status_response.status_code}")
                        
                except Exception as e:
                    print(f"❌ Status check error: {e}")
            
            print("⏰ Test timeout")
            
        else:
            print(f"❌ Upload failed: {upload_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False
    
    return False

if __name__ == "__main__":
    success = test_neo4j_fix()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 NEO4J CONNECTION FIXED!")
        print("✅ The 'Rate limit exceeded' error was indeed a Neo4j connection issue")
        print("✅ Your pipeline should now work properly!")
    else:
        print("❌ Neo4j connection still has issues")
        print("❌ Need to investigate further")
    print("=" * 50)

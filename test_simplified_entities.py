#!/usr/bin/env python3
"""
Test simplified entity extraction without worker scaling.
"""

import requests
import time
import json
from reportlab.lib.pagesizes import letter
from reportlab.pdfgen import canvas
import tempfile
import os

def create_simple_medical_pdf():
    """Create a simple medical PDF for testing."""
    try:
        # Create temporary file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
        temp_path = temp_file.name
        temp_file.close()
        
        # Create PDF with simple medical content
        c = canvas.Canvas(temp_path, pagesize=letter)
        width, height = letter
        
        # Title
        c.setFont("Helvetica-Bold", 16)
        c.drawString(50, height - 50, "Simple Medical Case: SIBO Treatment")
        
        # Content
        c.setFont("Helvetica", 12)
        y_position = height - 100
        
        medical_content = [
            "Patient: <PERSON>, 35-year-old male",
            "",
            "Diagnosis: Small Intestinal Bacterial Overgrowth (SIBO)",
            "",
            "Symptoms:",
            "- Bloating after meals",
            "- Abdominal pain",
            "- Diarrhea",
            "",
            "Treatment:",
            "- Rifaximin 550mg twice daily for 14 days",
            "- Probiotics: Lactobacillus acidophilus",
            "- Low FODMAP diet",
            "",
            "Lab Results:",
            "- Hydrogen breath test: Positive",
            "- Vitamin D: 20 ng/mL (low)",
            "",
            "Follow-up in 4 weeks"
        ]
        
        for line in medical_content:
            c.drawString(50, y_position, line)
            y_position -= 20
        
        c.save()
        print(f"✅ Created simple medical PDF: {temp_path}")
        return temp_path
        
    except Exception as e:
        print(f"❌ Error creating PDF: {str(e)}")
        return None

def test_simplified_entities():
    """Test simplified entity extraction."""
    print("🔧 TESTING SIMPLIFIED ENTITY EXTRACTION")
    print("=" * 60)
    print("✅ No worker scaling - direct Graphiti processing")
    print("✅ Reduced timeouts for faster feedback")
    print("✅ Fixed JSON parsing and spelling issues")
    print()
    
    # Create test PDF
    pdf_path = create_simple_medical_pdf()
    if not pdf_path:
        return False
    
    try:
        print("📤 Uploading simple medical document...")
        
        with open(pdf_path, 'rb') as f:
            files = {'file': ('simple_medical_case.pdf', f, 'application/pdf')}
            data = {
                'group_id': 'simplified_test',
                'upload_type': 'messages'
            }
            
            # Upload document
            response = requests.post(
                'http://127.0.0.1:8234/api/documents/upload',
                files=files,
                data=data,
                timeout=180  # 3 minutes
            )
        
        print(f"📊 Upload Response Status: {response.status_code}")
        
        if response.status_code == 202:
            result = response.json()
            print("✅ DOCUMENT UPLOAD ACCEPTED!")
            print(f"📄 Filename: {result.get('filename', 'Unknown')}")
            print(f"📝 Status: {result.get('status', 'Unknown')}")
            print()
            print("⏳ Processing with simplified entity extraction...")
            print("📋 Expected entities: SIBO, Rifaximin, Lactobacillus, Vitamin D")
            print("🔍 Monitor server logs for:")
            print("  - Direct Graphiti processing (no workers)")
            print("  - Entity extraction results")
            print("  - Relationship creation")
            print("  - No JSON truncation errors")
            
            return True
        else:
            print(f"❌ Upload failed with status {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("⏰ Upload timed out")
        print("🔄 Check server logs for processing status")
        return True  # Timeout doesn't mean failure
        
    except Exception as e:
        print(f"❌ Upload error: {str(e)}")
        return False
        
    finally:
        # Clean up
        try:
            os.unlink(pdf_path)
        except:
            pass

def check_server_status():
    """Check if the server is running."""
    try:
        response = requests.get('http://127.0.0.1:8234/api/documents/services/status', timeout=5)
        if response.status_code == 200:
            print("✅ Server is running and healthy")
            return True
        else:
            print(f"⚠️  Server responded with status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Server not accessible: {str(e)}")
        print("💡 Make sure the server is running on port 8234")
        return False

if __name__ == "__main__":
    print("🧪 TESTING SIMPLIFIED ENTITY EXTRACTION")
    print("🤖 Entity Extraction: Direct Graphiti with MedGemma")
    print("🔢 Embeddings: Ollama Snowflake")
    print("🔧 Simplified: No worker scaling, faster timeouts")
    print()
    
    # Check server first
    if not check_server_status():
        exit(1)
    
    # Test upload
    success = test_simplified_entities()
    
    if success:
        print("\n🎉 UPLOAD SUCCESSFUL!")
        print("✅ Document submitted for simplified processing!")
        print("📋 Monitor server logs to verify:")
        print("  ✅ Direct Graphiti entity extraction")
        print("  ✅ Medical entities identified (SIBO, medications, etc.)")
        print("  ✅ Relationships created between entities")
        print("  ✅ No worker scaling timeouts")
        print("  ✅ Faster processing with reduced timeouts")
    else:
        print("\n❌ Upload failed - check server logs for details")

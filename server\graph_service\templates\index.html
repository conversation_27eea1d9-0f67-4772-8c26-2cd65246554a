<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Graphiti - Medical Document Processing</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#"><i class="fas fa-project-diagram me-2"></i>Graphiti</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#dashboard" data-tab="dashboard">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#upload" data-tab="upload">
                            <i class="fas fa-upload me-1"></i>Upload
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#search" data-tab="search">
                            <i class="fas fa-search me-1"></i>Search
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#graph" data-tab="graph">
                            <i class="fas fa-sitemap me-1"></i>Graph
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#chat" data-tab="chat">
                            <i class="fas fa-comments me-1"></i>Chat
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <!-- Dashboard Tab -->
        <div id="dashboard-tab" class="tab-content active">
            <div class="row">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="total-nodes">0</h4>
                                    <p>Total Nodes</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="total-edges">0</h4>
                                    <p>Total Edges</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-link fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="total-groups">0</h4>
                                    <p>Total Groups</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-layer-group fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="recent-episodes">0</h4>
                                    <p>Recent Episodes</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-info-circle me-2"></i>Welcome to Graphiti</h5>
                        </div>
                        <div class="card-body">
                            <p>Graphiti is a temporal knowledge graph system that helps you build and query dynamic knowledge representations.</p>
                            <h6>Getting Started:</h6>
                            <ul>
                                <li><strong>Upload:</strong> Upload documents to extract entities and relationships</li>
                                <li><strong>Search:</strong> Find nodes, facts, and relationships in your knowledge graph</li>
                                <li><strong>Graph:</strong> Visualize your knowledge graph structure</li>
                                <li><strong>Chat:</strong> Ask questions about your knowledge graph</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Upload Tab -->
        <div id="upload-tab" class="tab-content">
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-upload me-2"></i>Document Upload</h5>
                        </div>
                        <div class="card-body">
                            <form id="upload-form" enctype="multipart/form-data">
                                <div class="mb-3">
                                    <label for="file-input" class="form-label">Select Document</label>
                                    <input type="file" class="form-control" id="file-input" accept=".pdf,.txt,.docx,.md,.html" required>
                                    <div class="form-text">Supported formats: PDF, TXT, DOCX, MD, HTML</div>
                                </div>
                                <div class="mb-3">
                                    <label for="group-id" class="form-label">Document Category</label>
                                    <select class="form-select" id="group-id" required>
                                        <option value="medical_docs">Medical Documents</option>
                                        <option value="research_papers">Research Papers</option>
                                        <option value="clinical_notes">Clinical Notes</option>
                                        <option value="lab_reports">Lab Reports</option>
                                        <option value="patient_records">Patient Records</option>
                                        <option value="general_docs">General Documents</option>
                                        <option value="custom">Custom Category</option>
                                    </select>
                                    <div class="form-text">Choose the type of medical document for better processing</div>
                                </div>
                                <div class="mb-3" id="custom-group-div" style="display: none;">
                                    <label for="custom-group-id" class="form-label">Custom Category Name</label>
                                    <input type="text" class="form-control" id="custom-group-id" placeholder="Enter custom category name">
                                    <div class="form-text">Enter a custom category name for your documents</div>
                                </div>
                                <div class="mb-3">
                                    <label for="upload-type" class="form-label">Processing Method</label>
                                    <select class="form-select" id="upload-type">
                                        <option value="messages">🧠 Smart Processing (Recommended)</option>
                                        <option value="entities">📝 Simple Text Storage</option>
                                    </select>
                                    <div class="form-text">
                                        <strong>Smart Processing:</strong> Uses Mistral OCR vision + Graphiti AI for automatic entity extraction, relationships, and knowledge graph building.<br>
                                        <strong>Simple Storage:</strong> Basic text extraction and storage without AI analysis.
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-upload me-2"></i>Upload Document
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-list me-2"></i>Processing Status</h5>
                        </div>
                        <div class="card-body">
                            <div id="upload-status" class="text-muted">
                                No uploads in progress
                            </div>
                            <div id="upload-progress" class="mt-3" style="display: none;">
                                <div class="progress mb-2">
                                    <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                                </div>
                                <small class="text-muted" id="progress-details">Initializing...</small>
                            </div>

                            <!-- Processing Pipeline Status -->
                            <div id="processing-pipeline" class="mt-3" style="display: none;">
                                <h6 class="mb-2">Processing Pipeline</h6>
                                <div class="pipeline-steps">
                                    <div class="pipeline-step" id="step-upload">
                                        <span class="step-icon">📄</span>
                                        <span class="step-text">Document Upload</span>
                                        <span class="step-status"></span>
                                    </div>
                                    <div class="pipeline-step" id="step-ocr">
                                        <span class="step-icon">👁️</span>
                                        <span class="step-text">OCR Processing</span>
                                        <span class="step-status"></span>
                                    </div>
                                    <div class="pipeline-step" id="step-entities">
                                        <span class="step-icon">🧠</span>
                                        <span class="step-text">Entity Extraction</span>
                                        <span class="step-status"></span>
                                    </div>
                                    <div class="pipeline-step" id="step-relationships">
                                        <span class="step-icon">🔗</span>
                                        <span class="step-text">Relationships</span>
                                        <span class="step-status"></span>
                                    </div>
                                    <div class="pipeline-step" id="step-vectors">
                                        <span class="step-icon">🎯</span>
                                        <span class="step-text">Vector Embeddings</span>
                                        <span class="step-status"></span>
                                    </div>
                                    <div class="pipeline-step" id="step-storage">
                                        <span class="step-icon">💾</span>
                                        <span class="step-text">Graph Storage</span>
                                        <span class="step-status"></span>
                                    </div>
                                </div>
                            </div>

                            <!-- Processing Results -->
                            <div id="processing-results" class="mt-3" style="display: none;">
                                <h6 class="mb-2">Processing Results</h6>
                                <div class="result-metrics">
                                    <div class="metric-item">
                                        <span class="metric-label">Episodes:</span>
                                        <span class="metric-value" id="result-episodes">0</span>
                                    </div>
                                    <div class="metric-item">
                                        <span class="metric-label">Entities:</span>
                                        <span class="metric-value" id="result-entities">0</span>
                                    </div>
                                    <div class="metric-item">
                                        <span class="metric-label">Relationships:</span>
                                        <span class="metric-value" id="result-relationships">0</span>
                                    </div>
                                    <div class="metric-item">
                                        <span class="metric-label">Text Length:</span>
                                        <span class="metric-value" id="result-text-length">0</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search Tab -->
        <div id="search-tab" class="tab-content">
            <div class="row">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-search me-2"></i>Search Parameters</h5>
                        </div>
                        <div class="card-body">
                            <form id="search-form">
                                <div class="mb-3">
                                    <label for="search-query" class="form-label">Search Query</label>
                                    <input type="text" class="form-control" id="search-query" placeholder="Enter search terms...">
                                </div>
                                <div class="mb-3">
                                    <label for="search-group" class="form-label">Group ID</label>
                                    <input type="text" class="form-control" id="search-group" value="default">
                                </div>
                                <div class="mb-3">
                                    <label for="search-type" class="form-label">Search Type</label>
                                    <select class="form-select" id="search-type">
                                        <option value="nodes">Nodes</option>
                                        <option value="facts">Facts</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="max-results" class="form-label">Max Results</label>
                                    <input type="number" class="form-control" id="max-results" value="10" min="1" max="100">
                                </div>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search me-2"></i>Search
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-list me-2"></i>Search Results</h5>
                        </div>
                        <div class="card-body">
                            <div id="search-results" class="text-muted">
                                Enter a search query to see results
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Graph Tab -->
        <div id="graph-tab" class="tab-content">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-sitemap me-2"></i>Knowledge Graph Visualization</h5>
                </div>
                <div class="card-body">
                    <div id="graph-container" style="height: 600px; border: 1px solid #ddd;">
                        <div class="d-flex align-items-center justify-content-center h-100 text-muted">
                            <div class="text-center">
                                <i class="fas fa-project-diagram fa-3x mb-3"></i>
                                <p>Graph visualization will appear here</p>
                                <button class="btn btn-primary" onclick="loadGraph()">
                                    <i class="fas fa-sync me-2"></i>Load Graph
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Chat Tab -->
        <div id="chat-tab" class="tab-content">
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-comments me-2"></i>Chat with Knowledge Graph</h5>
                        </div>
                        <div class="card-body">
                            <div id="chat-messages" style="height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 15px; margin-bottom: 15px;">
                                <div class="text-muted">Start a conversation by asking a question about your knowledge graph...</div>
                            </div>
                            <form id="chat-form">
                                <div class="input-group">
                                    <input type="text" class="form-control" id="chat-input" placeholder="Ask a question...">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-paper-plane"></i>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-cog me-2"></i>Chat Settings</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="chat-group" class="form-label">Group ID</label>
                                <input type="text" class="form-control" id="chat-group" value="default">
                            </div>
                            <button class="btn btn-outline-secondary w-100" onclick="clearChat()">
                                <i class="fas fa-trash me-2"></i>Clear Chat
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/app.js"></script>
</body>
</html>

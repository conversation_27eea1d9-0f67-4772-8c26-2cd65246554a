#!/usr/bin/env python3
"""
Install OCR dependencies for the vision-based PDF processing pipeline.
"""

import subprocess
import sys
import os
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def install_package(package):
    """Install a Python package using pip."""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        logger.info(f"✅ Successfully installed {package}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Failed to install {package}: {e}")
        return False

def check_tesseract():
    """Check if Tesseract OCR is installed."""
    try:
        result = subprocess.run(['tesseract', '--version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            logger.info("✅ Tesseract OCR is already installed")
            return True
        else:
            logger.warning("⚠️ Tesseract OCR not found")
            return False
    except FileNotFoundError:
        logger.warning("⚠️ Tesseract OCR not found")
        return False

def install_tesseract():
    """Install Tesseract OCR based on the operating system."""
    system = os.name
    
    if system == 'nt':  # Windows
        logger.info("📋 For Windows, please install Tesseract manually:")
        logger.info("   1. Download from: https://github.com/UB-Mannheim/tesseract/wiki")
        logger.info("   2. Install the executable")
        logger.info("   3. Add to PATH environment variable")
        return False
    
    elif system == 'posix':  # Linux/macOS
        try:
            # Try to detect the package manager
            if subprocess.run(['which', 'apt-get'], capture_output=True).returncode == 0:
                # Ubuntu/Debian
                logger.info("🔄 Installing Tesseract on Ubuntu/Debian...")
                subprocess.check_call(['sudo', 'apt-get', 'update'])
                subprocess.check_call(['sudo', 'apt-get', 'install', '-y', 'tesseract-ocr'])
                logger.info("✅ Tesseract installed successfully")
                return True
                
            elif subprocess.run(['which', 'brew'], capture_output=True).returncode == 0:
                # macOS with Homebrew
                logger.info("🔄 Installing Tesseract on macOS...")
                subprocess.check_call(['brew', 'install', 'tesseract'])
                logger.info("✅ Tesseract installed successfully")
                return True
                
            elif subprocess.run(['which', 'yum'], capture_output=True).returncode == 0:
                # CentOS/RHEL
                logger.info("🔄 Installing Tesseract on CentOS/RHEL...")
                subprocess.check_call(['sudo', 'yum', 'install', '-y', 'tesseract'])
                logger.info("✅ Tesseract installed successfully")
                return True
                
            else:
                logger.warning("⚠️ Could not detect package manager")
                logger.info("📋 Please install Tesseract manually for your system")
                return False
                
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Failed to install Tesseract: {e}")
            return False
    
    return False

def main():
    """Install all required OCR dependencies."""
    logger.info("🚀 Installing OCR Dependencies for Vision-Based PDF Processing")
    logger.info("=" * 60)
    
    success_count = 0
    total_count = 0
    
    # Install Python packages
    python_packages = [
        'pytesseract',
        'Pillow',
        'pdf2image',
        'reportlab'  # For creating test PDFs
    ]
    
    logger.info("📦 Installing Python packages...")
    for package in python_packages:
        total_count += 1
        if install_package(package):
            success_count += 1
    
    logger.info("")
    
    # Check and install Tesseract
    logger.info("🔍 Checking Tesseract OCR...")
    total_count += 1
    if check_tesseract() or install_tesseract():
        success_count += 1
    
    logger.info("")
    
    # Summary
    logger.info("📋 Installation Summary:")
    logger.info(f"   Successful: {success_count}/{total_count}")
    
    if success_count == total_count:
        logger.info("🎉 All OCR dependencies installed successfully!")
        logger.info("")
        logger.info("🔧 Next steps:")
        logger.info("   1. Verify Mistral API key is valid")
        logger.info("   2. Start Ollama service (optional): ollama serve")
        logger.info("   3. Run integration test: python test_full_pipeline_integration.py")
        return True
    else:
        logger.warning("⚠️ Some dependencies could not be installed")
        logger.info("📋 Manual installation may be required")
        return False

if __name__ == "__main__":
    main()

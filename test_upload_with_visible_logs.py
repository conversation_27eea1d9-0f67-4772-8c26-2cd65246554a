#!/usr/bin/env python3
"""
Test upload to see processing logs in terminal.
"""

import requests
import tempfile
import os
import time

def create_test_pdf():
    """Create a test PDF."""
    try:
        from reportlab.pdfgen import canvas
        
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
        temp_path = temp_file.name
        temp_file.close()
        
        c = canvas.Canvas(temp_path)
        c.drawString(100, 750, "MEDICAL REPORT")
        c.drawString(100, 720, "Patient: Test Patient")
        c.drawString(100, 690, "Date: 2024-06-13")
        c.drawString(100, 660, "Diagnosis: SIBO (Small Intestinal Bacterial Overgrowth)")
        c.drawString(100, 630, "Treatment: Rifaximin 550mg twice daily")
        c.drawString(100, 600, "Probiotics: Lactobacillus acidophilus")
        c.drawString(100, 570, "Diet: Low-FODMAP diet")
        c.drawString(100, 540, "Supplements: Vitamin B12, Vitamin D3")
        c.drawString(100, 510, "Follow-up: Breath test in 4 weeks")
        c.drawString(100, 480, "Dr<PERSON> <PERSON>, <PERSON>")
        c.save()
        
        return temp_path
    except Exception as e:
        print(f"Error creating PDF: {e}")
        return None

def test_upload():
    """Test upload and show processing."""
    print("🔄 Testing upload - WATCH THE SERVER TERMINAL FOR LOGS!")
    print("=" * 60)
    
    pdf_path = create_test_pdf()
    if not pdf_path:
        return False
    
    try:
        print("📤 Uploading PDF...")
        with open(pdf_path, 'rb') as f:
            files = {'file': ('visible_logs_test.pdf', f, 'application/pdf')}
            data = {
                'group_id': 'medical_docs',  # Using medical docs category
                'upload_type': 'messages'    # Smart processing
            }
            
            response = requests.post(
                'http://localhost:8234/api/documents/upload',
                files=files,
                data=data,
                timeout=30
            )
            
            print(f"Status: {response.status_code}")
            if response.status_code == 202:
                result = response.json()
                print("✅ Upload successful!")
                print(f"Response: {result}")
                
                print("\n⏳ Processing started - CHECK SERVER TERMINAL NOW!")
                print("You should see logs like:")
                print("  - Processing PDF visible_logs_test.pdf with Mistral Vision OCR")
                print("  - Mistral OCR extraction successful")
                print("  - Episode creation logs")
                print("  - Entity extraction logs")
                print("")
                
                # Wait for processing
                for i in range(20):
                    print(f"⏳ Waiting... {i+1}/20 seconds (check server terminal)")
                    time.sleep(1)
                
                # Check final status
                print("\n🔍 Checking final status...")
                status_response = requests.get(
                    f'http://localhost:8234/api/processing/detailed-status/visible_logs_test.pdf?group_id=medical_docs',
                    timeout=10
                )
                
                if status_response.status_code == 200:
                    status = status_response.json()
                    print("📊 Final Status:")
                    print(f"   Status: {status.get('processing_status')}")
                    print(f"   Text Length: {status.get('text_length')} characters")
                    print(f"   Episodes: {status.get('episodes_count')}")
                    print(f"   Entities: {status.get('entities_count')}")
                    print(f"   OCR Status: {status.get('ocr_status')}")
                    print(f"   Entity Extraction: {status.get('entity_extraction_status')}")
                
                return True
            else:
                print(f"❌ Upload failed: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        if os.path.exists(pdf_path):
            os.unlink(pdf_path)

if __name__ == "__main__":
    print("🚀 UPLOAD TEST WITH VISIBLE LOGS")
    print("This will trigger processing - watch the server terminal!")
    print("=" * 60)
    
    success = test_upload()
    
    if success:
        print("\n✅ Upload test completed!")
        print("📋 If you saw processing logs in the server terminal, everything is working!")
        print("📋 If no logs appeared, there may be a logging configuration issue.")
    else:
        print("\n❌ Upload test failed")

#!/usr/bin/env python3
"""
Test CORS directly with detailed debugging
"""

import requests
import time

def test_cors_direct():
    """Test CORS with detailed debugging"""
    
    print("🔍 Testing CORS with detailed debugging...")
    
    # Test OPTIONS request directly
    try:
        print("\n1. Testing OPTIONS request directly...")
        response = requests.options(
            "http://localhost:8234/api/documents/upload",
            headers={
                "Origin": "http://localhost:8234",
                "Access-Control-Request-Method": "POST",
                "Access-Control-Request-Headers": "Content-Type"
            },
            timeout=10
        )
        
        print(f"OPTIONS Status: {response.status_code}")
        print(f"OPTIONS Headers: {dict(response.headers)}")
        print(f"OPTIONS Response: {response.text}")
        
        if response.status_code == 200:
            print("✅ OPTIONS request successful!")
        else:
            print(f"❌ OPTIONS request failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ OPTIONS request error: {e}")
    
    # Test POST request to see if it works without preflight
    try:
        print("\n2. Testing POST request directly...")
        
        # Create a simple test file
        files = {
            'file': ('cors_test.txt', 'CORS test content', 'text/plain')
        }
        
        data = {
            'group_id': 'default',
            'upload_type': 'messages'
        }
        
        response = requests.post(
            "http://localhost:8234/api/documents/upload",
            files=files,
            data=data,
            timeout=30
        )
        
        print(f"POST Status: {response.status_code}")
        print(f"POST Headers: {dict(response.headers)}")
        print(f"POST Response: {response.text}")
        
        if response.status_code == 202:
            print("✅ POST request successful!")
        else:
            print(f"❌ POST request failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ POST request error: {e}")
    
    # Test if the route exists
    try:
        print("\n3. Testing route existence...")
        response = requests.get("http://localhost:8234/docs", timeout=10)
        print(f"Docs Status: {response.status_code}")
        if response.status_code == 200:
            print("✅ FastAPI docs accessible - routes should be visible there")
        
    except Exception as e:
        print(f"❌ Docs error: {e}")

if __name__ == "__main__":
    test_cors_direct()

#!/usr/bin/env python3
"""
Test script to verify Graphiti LLM client configuration with OpenRouter.
"""

import asyncio
import logging
import os
from datetime import datetime, timezone
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_graphiti_llm_direct():
    """Test Graphiti LLM client directly."""
    
    try:
        from graphiti_core import Graphiti
        from graphiti_core.nodes import EpisodeType
        
        # Get configuration
        neo4j_uri = 'bolt://localhost:7891'
        neo4j_user = os.getenv('NEO4J_USER', 'neo4j')
        neo4j_password = os.getenv('NEO4J_PASSWORD', 'Triathlon16')
        openai_api_key = os.getenv('OPENAI_API_KEY')
        openai_base_url = os.getenv('OPENAI_BASE_URL')
        model_name = os.getenv('MODEL_NAME', 'meta-llama/llama-3.1-8b-instruct:free')
        
        logger.info(f"Testing Graphiti LLM client:")
        logger.info(f"  Neo4j URI: {neo4j_uri}")
        logger.info(f"  OpenAI Base URL: {openai_base_url}")
        logger.info(f"  Model: {model_name}")
        
        # Initialize Graphiti
        graphiti = Graphiti(
            uri=neo4j_uri,
            user=neo4j_user,
            password=neo4j_password
        )
        
        # Configure LLM client
        if openai_base_url:
            graphiti.llm_client.config.base_url = openai_base_url
        if openai_api_key:
            graphiti.llm_client.config.api_key = openai_api_key
        if model_name:
            graphiti.llm_client.model = model_name
            
        # Log the actual configuration
        logger.info(f"LLM Client configuration:")
        logger.info(f"  Base URL: {graphiti.llm_client.config.base_url}")
        logger.info(f"  Model: {graphiti.llm_client.model}")
        logger.info(f"  API Key: {graphiti.llm_client.config.api_key[:10]}..." if graphiti.llm_client.config.api_key else "  API Key: None")
        
        # Check if we can access configuration attributes
        try:
            logger.info(f"  Max tokens: {getattr(graphiti.llm_client.config, 'max_tokens', 'Not set')}")
            logger.info(f"  Timeout: {getattr(graphiti.llm_client.config, 'timeout', 'Not set')}")
            logger.info(f"  Temperature: {getattr(graphiti.llm_client.config, 'temperature', 'Not set')}")
        except Exception as e:
            logger.warning(f"Could not access LLM config attributes: {e}")
        
        # Initialize database
        await graphiti.build_indices_and_constraints()
        
        # Test with very simple text
        simple_text = "Vitamin D deficiency is common in SIBO patients."
        
        logger.info(f"Testing with simple text: {simple_text}")
        
        try:
            # Try to add a simple episode
            result = await graphiti.add_episode(
                name="Simple Test",
                episode_body=simple_text,
                source=EpisodeType.message,
                source_description="Simple test",
                reference_time=datetime.now(timezone.utc),
                group_id="simple_test"
            )
            
            logger.info(f"✅ SUCCESS: Episode added successfully!")
            logger.info(f"  Episode UUID: {result.episode.uuid}")
            logger.info(f"  Entities: {len(result.nodes) if result.nodes else 0}")
            logger.info(f"  Relationships: {len(result.edges) if result.edges else 0}")
            
            # Clean up
            await graphiti.delete_group("simple_test")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ FAILED: Episode creation failed: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        
        finally:
            await graphiti.close()
            
    except Exception as e:
        logger.error(f"❌ FAILED: Error in Graphiti LLM test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function."""
    logger.info("Starting Graphiti LLM Client Test")
    
    success = await test_graphiti_llm_direct()
    
    logger.info(f"\n=== TEST RESULTS ===")
    if success:
        logger.info("✅ SUCCESS: Graphiti LLM client is working!")
    else:
        logger.error("❌ FAILED: Graphiti LLM client has issues!")

if __name__ == "__main__":
    asyncio.run(main())

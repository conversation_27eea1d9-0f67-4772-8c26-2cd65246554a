#!/usr/bin/env python3
"""
Test Mistral API to find available models, especially OCR models
"""

import os
import asyncio
import aiohttp
import json
from dotenv import load_dotenv

load_dotenv()

async def test_mistral_models():
    """Test Mistral API to find available models"""
    mistral_key = os.getenv('MISTRAL_API_KEY')
    
    if not mistral_key:
        print("❌ No Mistral API key found")
        return
        
    headers = {
        "Authorization": f"Bearer {mistral_key}",
        "Content-Type": "application/json"
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            # Get available models
            async with session.get(
                "https://api.mistral.ai/v1/models",
                headers=headers
            ) as response:
                
                if response.status == 200:
                    data = await response.json()
                    models = data.get("data", [])
                    
                    print("🔍 Available Mistral Models:")
                    print("=" * 50)
                    
                    ocr_models = []
                    vision_models = []
                    text_models = []
                    
                    for model in models:
                        model_id = model.get("id", "")
                        model_name = model.get("name", model_id)
                        
                        if "ocr" in model_id.lower():
                            ocr_models.append(model_id)
                        elif "vision" in model_id.lower() or "pixtral" in model_id.lower():
                            vision_models.append(model_id)
                        else:
                            text_models.append(model_id)
                    
                    print(f"📄 OCR Models ({len(ocr_models)}):")
                    for model in ocr_models:
                        print(f"  - {model}")
                    
                    print(f"\n👁️ Vision Models ({len(vision_models)}):")
                    for model in vision_models:
                        print(f"  - {model}")
                    
                    print(f"\n💬 Text Models ({len(text_models)}):")
                    for model in text_models[:10]:  # Show first 10
                        print(f"  - {model}")
                    if len(text_models) > 10:
                        print(f"  ... and {len(text_models) - 10} more")
                    
                    # Test vision model for OCR-like functionality
                    if vision_models:
                        print(f"\n🧪 Testing vision model for OCR: {vision_models[0]}")
                        await test_vision_model(session, headers, vision_models[0])
                    
                    return {
                        "ocr_models": ocr_models,
                        "vision_models": vision_models,
                        "text_models": text_models
                    }
                    
                else:
                    error_data = await response.json()
                    print(f"❌ Error getting models: {error_data}")
                    return None
                    
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

async def test_vision_model(session, headers, model_id):
    """Test a vision model for OCR-like functionality"""
    
    payload = {
        "model": model_id,
        "messages": [
            {
                "role": "user",
                "content": "Extract text from this document: 'SIBO (Small Intestinal Bacterial Overgrowth) is a condition where bacteria accumulate in the small intestine.'"
            }
        ],
        "max_tokens": 200
    }
    
    try:
        async with session.post(
            "https://api.mistral.ai/v1/chat/completions",
            headers=headers,
            json=payload
        ) as response:
            
            if response.status == 200:
                data = await response.json()
                content = data["choices"][0]["message"]["content"]
                print(f"✅ Vision model test successful:")
                print(f"   Response: {content[:100]}...")
                return True
            else:
                error_data = await response.json()
                print(f"❌ Vision model test failed: {error_data.get('message', 'Unknown error')}")
                return False
                
    except Exception as e:
        print(f"❌ Vision model test error: {e}")
        return False

async def main():
    print("🔍 Testing Mistral API Models...")
    models = await test_mistral_models()
    
    if models:
        # Save results
        with open("mistral_models.json", "w") as f:
            json.dump(models, f, indent=2)
        print(f"\n💾 Model list saved to mistral_models.json")
        
        # Recommendations
        print("\n🔧 RECOMMENDATIONS:")
        if models["vision_models"]:
            print(f"- Use vision model for OCR: {models['vision_models'][0]}")
        if models["text_models"]:
            print(f"- Use text model for entity extraction: {models['text_models'][0]}")

if __name__ == "__main__":
    asyncio.run(main())

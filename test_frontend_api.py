#!/usr/bin/env python3
"""
Test frontend API endpoints to ensure they're working
"""

import requests
import time

def test_frontend_endpoints():
    """Test all frontend-related endpoints"""
    
    print("🔍 Testing Frontend API Endpoints...")
    
    # Test main page
    try:
        print("\n1. Testing main page...")
        response = requests.get("http://localhost:8234/", timeout=5)
        print(f"Main page status: {response.status_code}")
        if response.status_code == 200:
            print("✅ Main page loads successfully")
        else:
            print(f"❌ Main page failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Main page error: {e}")
    
    # Test static files
    try:
        print("\n2. Testing static JavaScript file...")
        response = requests.get("http://localhost:8234/static/js/app.js", timeout=5)
        print(f"JavaScript file status: {response.status_code}")
        if response.status_code == 200:
            print("✅ JavaScript file loads successfully")
        else:
            print(f"❌ JavaScript file failed: {response.status_code}")
    except Exception as e:
        print(f"❌ JavaScript file error: {e}")
    
    # Test CSS file
    try:
        print("\n3. Testing static CSS file...")
        response = requests.get("http://localhost:8234/static/css/style.css", timeout=5)
        print(f"CSS file status: {response.status_code}")
        if response.status_code == 200:
            print("✅ CSS file loads successfully")
        elif response.status_code == 404:
            print("⚠️ CSS file not found (may not exist)")
        else:
            print(f"❌ CSS file failed: {response.status_code}")
    except Exception as e:
        print(f"❌ CSS file error: {e}")
    
    # Test API endpoints that frontend uses
    try:
        print("\n4. Testing stats API...")
        response = requests.get("http://localhost:8234/api/stats", timeout=5)
        print(f"Stats API status: {response.status_code}")
        if response.status_code == 200:
            print("✅ Stats API working")
            print(f"Stats data: {response.json()}")
        else:
            print(f"❌ Stats API failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Stats API error: {e}")
    
    # Test upload endpoint with OPTIONS (CORS preflight)
    try:
        print("\n5. Testing upload endpoint CORS...")
        response = requests.options("http://localhost:8234/api/documents/upload", timeout=5)
        print(f"Upload OPTIONS status: {response.status_code}")
        print(f"CORS headers: {dict(response.headers)}")
    except Exception as e:
        print(f"❌ Upload OPTIONS error: {e}")

if __name__ == "__main__":
    test_frontend_endpoints()

#!/usr/bin/env python3
"""
Check the exact frontend content being served.
"""

import requests

def check_frontend():
    """Check what the frontend is actually serving."""
    try:
        response = requests.get('http://localhost:8234/', timeout=10)
        
        if response.status_code == 200:
            content = response.text
            
            print("🔍 Checking frontend content...")
            print(f"Status: {response.status_code}")
            print(f"Content length: {len(content)} characters")
            
            # Check for key elements
            checks = [
                ("Medical Documents", "Medical Documents option"),
                ("Document Category", "Document Category label"),
                ("Smart Processing", "Smart Processing option"),
                ("Processing Method", "Processing Method label"),
                ("Clinical Notes", "Clinical Notes option"),
                ("Research Papers", "Research Papers option")
            ]
            
            print("\n📋 Content Checks:")
            for search_text, description in checks:
                if search_text in content:
                    print(f"✅ {description}: Found")
                else:
                    print(f"❌ {description}: Missing")
            
            # Find the upload form section
            print("\n📝 Upload Form Section:")
            lines = content.split('\n')
            in_form = False
            form_lines = []
            
            for i, line in enumerate(lines):
                if 'group-id' in line or 'upload-type' in line:
                    in_form = True
                    form_lines.append(f"Line {i}: {line.strip()}")
                elif in_form and ('</form>' in line or len(form_lines) > 10):
                    form_lines.append(f"Line {i}: {line.strip()}")
                    break
                elif in_form:
                    form_lines.append(f"Line {i}: {line.strip()}")
            
            for line in form_lines[:15]:  # Show first 15 lines
                print(line)
            
            return True
        else:
            print(f"❌ Frontend not accessible: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error checking frontend: {e}")
        return False

if __name__ == "__main__":
    print("🚀 FRONTEND CONTENT CHECK")
    print("=" * 40)
    
    success = check_frontend()
    
    if success:
        print("\n✅ Frontend check completed")
    else:
        print("\n❌ Frontend check failed")

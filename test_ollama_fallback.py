#!/usr/bin/env python3
"""
Test Ollama MedGemma fallback when Mistral OCR hits rate limits
"""

import requests
import time
import json

def test_ollama_fallback():
    """Test the Ollama MedGemma fallback functionality"""
    
    print("🔍 Testing Ollama MedGemma Fallback System...")
    
    # Wait for service to start
    print("\n⏳ Waiting for service to start...")
    time.sleep(10)
    
    # Step 1: Upload a SIBO document to trigger processing
    try:
        print("\n📤 Step 1: Uploading SIBO document to test fallback system...")
        
        # Create a comprehensive SIBO document that will benefit from MedGemma analysis
        sibo_content = """
        SIBO Clinical Management Protocol
        
        Patient: <PERSON>
        Date: 2024-06-11
        Diagnosis: Small Intestinal Bacterial Overgrowth (SIBO)
        
        CLINICAL PRESENTATION:
        Chief Complaint: Chronic bloating, gas, and abdominal discomfort for 8 months
        
        Symptoms:
        - Severe postprandial bloating (8/10 severity)
        - Excessive gas production, particularly hydrogen
        - Alternating bowel movements (diarrhea/constipation)
        - Cramping abdominal pain, especially lower quadrants
        - Food intolerances: dairy, gluten, high FODMAP foods
        - Fatigue and brain fog after meals
        - Weight loss (10 lbs over 6 months)
        
        DIAGNOSTIC WORKUP:
        Lactulose Breath Test Results:
        - Baseline hydrogen: 5 ppm
        - 90-minute hydrogen: 45 ppm (positive >20 ppm)
        - Methane levels: 8 ppm (normal <10 ppm)
        - Interpretation: Hydrogen-dominant SIBO
        
        Additional Testing:
        - Comprehensive stool analysis: Low beneficial bacteria
        - B12 level: 280 pg/mL (low normal)
        - Iron studies: Ferritin 15 ng/mL (deficient)
        - Vitamin D: 22 ng/mL (insufficient)
        
        TREATMENT PROTOCOL:
        
        Phase 1: Antimicrobial Therapy (14 days)
        Primary: Rifaximin 550mg TID
        Alternative: Herbal protocol if insurance denial
        - Oregano oil 200mg BID
        - Berberine 500mg TID
        - Allicin 450mg BID
        
        Phase 2: Dietary Intervention (6-8 weeks)
        Low FODMAP Diet Implementation:
        - Eliminate high FODMAP foods
        - Focus on low FODMAP proteins and vegetables
        - Limit portion sizes to reduce fermentation
        - Consider elemental diet if severe symptoms persist
        
        Phase 3: Motility Support
        Prokinetic therapy:
        - Motilium 10mg QID (if available)
        - Alternative: Ginger extract 250mg BID
        - Magnesium glycinate 400mg at bedtime
        
        Phase 4: Microbiome Restoration
        Targeted probiotics (after antimicrobial phase):
        - Lactobacillus plantarum 299v
        - Bifidobacterium infantis 35624
        - Saccharomyces boulardii 250mg BID
        
        MONITORING PLAN:
        Week 2: Symptom assessment
        Week 4: Dietary tolerance evaluation
        Week 8: Repeat breath test consideration
        Week 12: Comprehensive follow-up
        
        PATIENT EDUCATION:
        - SIBO pathophysiology explanation
        - Dietary modification guidance
        - Symptom tracking importance
        - When to contact healthcare provider
        
        PROGNOSIS:
        Good with adherence to treatment protocol
        Recurrence risk: 20-30% within 12 months
        Long-term management may be required
        
        Dr. Sarah Johnson, MD
        Gastroenterology Associates
        """
        
        files = {
            'file': ('sibo_clinical_protocol.txt', sibo_content, 'text/plain')
        }
        
        data = {
            'group_id': 'default',
            'upload_type': 'messages'
        }
        
        upload_response = requests.post(
            "http://localhost:8234/api/documents/upload",
            files=files,
            data=data,
            timeout=30
        )
        
        print(f"Upload Status: {upload_response.status_code}")
        print(f"Upload Response: {upload_response.text}")
        
        if upload_response.status_code == 202:
            print("✅ Upload successful!")
            
            # Step 2: Monitor processing progress and look for fallback activation
            print("\n⏳ Step 2: Monitoring processing progress and fallback system...")
            
            for i in range(20):  # Check for 3+ minutes
                time.sleep(10)
                
                try:
                    status_response = requests.get(
                        "http://localhost:8234/api/processing/detailed-status/sibo_clinical_protocol.txt?group_id=default",
                        timeout=10
                    )
                    
                    if status_response.status_code == 200:
                        status_data = status_response.json()
                        processing_status = status_data.get('processing_status', 'unknown')
                        
                        print(f"Check {i+1}: Processing Status: {processing_status}")
                        
                        if processing_status == 'completed':
                            print("🎉 Processing completed successfully!")
                            print(f"📊 Results:")
                            print(f"  - Episodes: {status_data.get('episodes_count', 0)}")
                            print(f"  - Entities: {status_data.get('entities_count', 0)}")
                            print(f"  - Text Length: {status_data.get('text_length', 0)}")
                            print(f"  - OCR Status: {status_data.get('ocr_status', 'unknown')}")
                            print(f"  - Entity Status: {status_data.get('entity_extraction_status', 'unknown')}")
                            
                            # Check if we have actual content
                            if status_data.get('text_length', 0) > 0:
                                print("✅ OCR PROCESSING SUCCESSFUL!")
                                print("✅ Text extraction working!")
                                print("✅ Fallback system functional!")
                                
                                # Determine which OCR was used based on text length and quality
                                text_length = status_data.get('text_length', 0)
                                if text_length > len(sibo_content) * 1.2:  # Enhanced text suggests MedGemma
                                    print("🔬 LIKELY USED: Ollama MedGemma (enhanced medical text)")
                                elif text_length >= len(sibo_content) * 0.8:  # Normal extraction
                                    print("⚡ LIKELY USED: Mistral OCR or Local OCR")
                                
                                # Check if entities were extracted
                                if status_data.get('entities_count', 0) > 0:
                                    print("✅ Entity extraction also working!")
                                else:
                                    print("⚠️ No entities extracted - may be rate limited")
                                
                                return True
                            else:
                                print("❌ No text extracted - all OCR methods may have failed")
                                return False
                                
                        elif processing_status == 'failed':
                            print(f"❌ Processing failed: {status_data.get('error_message', 'Unknown error')}")
                            return False
                        elif processing_status in ['processing', 'pending']:
                            print(f"⏳ Still processing... (attempt {i+1}/20)")
                        else:
                            print(f"❓ Unknown status: {processing_status}")
                    else:
                        print(f"❌ Status check failed: {status_response.status_code}")
                        
                except Exception as e:
                    print(f"❌ Status check error: {e}")
            
            print("⏰ Processing timeout - checking final status...")
            
        else:
            print(f"❌ Upload failed: {upload_response.status_code}")
            print(f"Error: {upload_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False
    
    return False

def check_logs_for_fallback():
    """Check Docker logs for evidence of fallback activation"""
    print("\n📋 Step 3: Checking logs for fallback evidence...")
    
    try:
        import subprocess
        result = subprocess.run(
            ["docker-compose", "logs", "--tail=50", "graph"],
            capture_output=True,
            text=True,
            timeout=15
        )
        
        if result.returncode == 0:
            logs = result.stdout
            
            # Look for fallback indicators
            fallback_indicators = [
                "Rate limit",
                "Ollama",
                "MedGemma",
                "fallback",
                "Mistral OCR failed"
            ]
            
            found_indicators = []
            for indicator in fallback_indicators:
                if indicator.lower() in logs.lower():
                    found_indicators.append(indicator)
            
            if found_indicators:
                print(f"✅ Fallback evidence found: {', '.join(found_indicators)}")
                return True
            else:
                print("ℹ️ No explicit fallback evidence in recent logs")
                return False
        else:
            print("❌ Could not retrieve logs")
            return False
            
    except Exception as e:
        print(f"❌ Error checking logs: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing Ollama MedGemma Fallback System for SIBO Documents")
    print("=" * 70)
    
    success = test_ollama_fallback()
    fallback_evidence = check_logs_for_fallback()
    
    print("\n" + "=" * 70)
    print("📊 FINAL RESULTS:")
    
    if success:
        print("🎉 OCR PROCESSING TEST: ✅ PASSED")
        print("✅ Document processing is working!")
        print("✅ Fallback system is functional!")
        
        if fallback_evidence:
            print("✅ Fallback activation detected in logs!")
            print("🔬 Ollama MedGemma is working as intended!")
        else:
            print("ℹ️ Processing succeeded (may have used Mistral or local OCR)")
        
        print("\n🚀 YOUR SIBO DOCUMENT PROCESSING SYSTEM IS READY!")
        print("📋 Features available:")
        print("  - Mistral OCR (primary)")
        print("  - Ollama MedGemma fallback (medical-focused)")
        print("  - Local OCR fallback (always available)")
        print("  - Rate limit protection")
        print("  - Medical text enhancement")
        
    else:
        print("❌ OCR PROCESSING TEST: ❌ FAILED")
        print("❌ Document processing needs investigation")
        
        if fallback_evidence:
            print("⚠️ Fallback attempted but processing still failed")
        else:
            print("❌ No fallback activation detected")
    
    print("\n" + "=" * 70)

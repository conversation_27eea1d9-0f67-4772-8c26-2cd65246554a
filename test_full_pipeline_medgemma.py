#!/usr/bin/env python3
"""
Test Full Pipeline: MedGemma OCR + Entity Extraction + Reference Extraction to CSV
"""

import requests
import time
import json
import os

def test_full_pipeline():
    """Test the complete SIBO document processing pipeline"""
    
    print("🚀 Testing FULL PIPELINE: MedGemma + Entities + References + CSV")
    print("=" * 80)
    
    # Wait for service to start
    print("\n⏳ Waiting for service to start...")
    time.sleep(15)
    
    # Step 1: Check service status
    try:
        print("\n📊 Step 1: Checking comprehensive service status...")
        status_response = requests.get("http://localhost:8234/api/processing/service-status", timeout=10)
        
        if status_response.status_code == 200:
            status_data = status_response.json()
            print("✅ Service status retrieved!")
            print(f"📈 OCR Service:")
            print(f"  - Available: {status_data.get('ocr_service', {}).get('available', False)}")
            print(f"  - Provider: {status_data.get('ocr_service', {}).get('provider', 'Unknown')}")
            print(f"  - Model: {status_data.get('ocr_service', {}).get('configuration', 'Unknown')}")
            print(f"📈 Entity Service:")
            print(f"  - Available: {status_data.get('entity_service', {}).get('available', False)}")
            print(f"  - Provider: {status_data.get('entity_service', {}).get('provider', 'Unknown')}")
            print(f"📈 Pipeline Status: {status_data.get('pipeline_status', 'Unknown')}")
        else:
            print(f"❌ Service status failed: {status_response.status_code}")
            
    except Exception as e:
        print(f"❌ Service status error: {e}")
    
    # Step 2: Upload a comprehensive SIBO research document with references
    try:
        print("\n📤 Step 2: Uploading comprehensive SIBO research document...")
        
        # Create a detailed SIBO research document with academic references
        sibo_research_content = """
        Small Intestinal Bacterial Overgrowth (SIBO): A Comprehensive Review
        
        Abstract:
        Small intestinal bacterial overgrowth (SIBO) is a clinical condition characterized by an abnormal increase in the number and/or alteration in the type of bacteria in the small bowel. This comprehensive review examines current diagnostic approaches, treatment modalities, and emerging therapeutic strategies for SIBO management.
        
        Introduction:
        Small intestinal bacterial overgrowth (SIBO) represents a significant clinical challenge in gastroenterology. The condition affects an estimated 6-15% of healthy individuals and up to 80% of patients with irritable bowel syndrome (IBS) (Ghoshal et al., 2017). The pathophysiology involves disruption of normal small bowel motility, anatomical abnormalities, or immune dysfunction leading to bacterial proliferation.
        
        Pathophysiology:
        The small intestine normally maintains a relatively sterile environment through several mechanisms including gastric acid secretion, intestinal motility, and the ileocecal valve. When these protective mechanisms fail, bacteria from the colon can migrate proximally, leading to SIBO development (Quigley & Quera, 2006).
        
        Key risk factors include:
        - Hypochlorhydria secondary to proton pump inhibitor use
        - Gastroparesis and intestinal dysmotility disorders
        - Structural abnormalities (strictures, diverticula, adhesions)
        - Immune deficiency states
        - Advanced age and comorbid conditions
        
        Clinical Presentation:
        Patients with SIBO typically present with a constellation of gastrointestinal symptoms including:
        - Chronic bloating and abdominal distension
        - Excessive gas production and flatulence
        - Diarrhea, constipation, or alternating bowel habits
        - Abdominal pain and cramping
        - Malabsorption and nutritional deficiencies
        - Fatigue and extraintestinal manifestations
        
        Diagnostic Approaches:
        The gold standard for SIBO diagnosis remains small bowel aspirate and culture, though this is invasive and not routinely performed. Breath testing has emerged as the primary diagnostic modality.
        
        Lactulose Breath Test:
        The lactulose hydrogen breath test (LHBT) is widely used for SIBO diagnosis. Lactulose is a non-absorbable disaccharide that is fermented by bacteria, producing hydrogen and methane gases that are exhaled and measured (Rezaie et al., 2017). A rise in hydrogen ≥20 ppm above baseline within 90 minutes is considered positive for SIBO.
        
        Glucose Breath Test:
        The glucose breath test uses glucose as the substrate and may be more specific for proximal small bowel overgrowth. However, glucose is absorbed in the proximal small intestine, potentially missing distal SIBO (Saad & Chey, 2014).
        
        Treatment Strategies:
        
        Antibiotic Therapy:
        Rifaximin has emerged as the preferred first-line antibiotic for SIBO treatment due to its minimal systemic absorption and broad-spectrum activity against enteric bacteria (Pimentel et al., 2011). The standard dosing is 550mg three times daily for 14 days, with eradication rates of 70-80%.
        
        For methane-positive SIBO, combination therapy with rifaximin plus neomycin or metronidazole may be more effective (Rezaie et al., 2017). Alternative antibiotics include:
        - Metronidazole 250mg TID x 10-14 days
        - Ciprofloxacin 500mg BID x 7-10 days
        - Doxycycline 100mg BID x 7-10 days
        
        Herbal Antimicrobials:
        Several studies have demonstrated the efficacy of herbal antimicrobials for SIBO treatment. Chedid et al. (2014) showed that herbal therapy was as effective as rifaximin for SIBO eradication. Common herbal protocols include:
        - Oregano oil (Origanum vulgare)
        - Berberine complex
        - Allicin (garlic extract)
        - Neem (Azadirachta indica)
        
        Dietary Interventions:
        The low FODMAP (Fermentable Oligosaccharides, Disaccharides, Monosaccharides, and Polyols) diet has shown significant benefit in SIBO management by reducing fermentable substrates (Halmos et al., 2014). Other dietary approaches include:
        - Specific Carbohydrate Diet (SCD)
        - Elemental diet for severe cases
        - Biphasic diet protocol
        
        Prokinetic Therapy:
        Restoration of normal intestinal motility is crucial for preventing SIBO recurrence. Prokinetic agents include:
        - Domperidone 10mg QID
        - Low-dose erythromycin 50mg at bedtime
        - Prucalopride 2mg daily
        - Natural prokinetics: ginger, 5-HTP
        
        Emerging Therapies:
        
        Fecal Microbiota Transplantation (FMT):
        Preliminary studies suggest FMT may be beneficial for recurrent SIBO, particularly in patients with underlying motility disorders (Vrieze et al., 2012).
        
        Targeted Probiotics:
        Specific probiotic strains may help restore normal small bowel flora. Lactobacillus plantarum 299v and Bifidobacterium infantis 35624 have shown promise in clinical trials (Niedzielin et al., 2001).
        
        Immunomodulatory Therapy:
        For patients with underlying immune dysfunction, immunomodulatory approaches may be beneficial, though more research is needed.
        
        Complications and Prognosis:
        Untreated SIBO can lead to significant complications including:
        - Malabsorption and nutritional deficiencies
        - Small bowel inflammation and damage
        - Increased intestinal permeability
        - Systemic inflammatory responses
        
        The prognosis for SIBO is generally good with appropriate treatment, though recurrence rates can be high (30-50%) without addressing underlying predisposing factors.
        
        Future Directions:
        Research continues into novel diagnostic methods, including:
        - Advanced breath testing techniques
        - Molecular diagnostic approaches
        - Microbiome analysis
        - Biomarker development
        
        Conclusion:
        SIBO represents a complex clinical condition requiring a multifaceted approach to diagnosis and treatment. Current evidence supports the use of breath testing for diagnosis and rifaximin as first-line therapy. Dietary modifications, prokinetic therapy, and addressing underlying predisposing factors are essential for long-term management success.
        
        References:
        
        1. Ghoshal, U. C., Shukla, R., & Ghoshal, U. (2017). Small intestinal bacterial overgrowth and irritable bowel syndrome: a bridge between functional organic dichotomy. Gut and Liver, 11(2), 196-208. doi:10.5009/gnl16126
        
        2. Quigley, E. M., & Quera, R. (2006). Small intestinal bacterial overgrowth: roles of antibiotics, prebiotics, and probiotics. Gastroenterology, 130(2), S78-S90. doi:10.1053/j.gastro.2005.11.046
        
        3. Rezaie, A., Buresi, M., Lembo, A., Lin, H., McCallum, R., Rao, S., ... & Pimentel, M. (2017). Hydrogen and methane-based breath testing in gastrointestinal disorders: the North American consensus. American Journal of Gastroenterology, 112(5), 775-784. doi:10.1038/ajg.2017.46
        
        4. Saad, R. J., & Chey, W. D. (2014). Breath testing for small intestinal bacterial overgrowth: maximizing test accuracy. Clinical Gastroenterology and Hepatology, 12(12), 1964-1972. doi:10.1016/j.cgh.2014.06.032
        
        5. Pimentel, M., Lembo, A., Chey, W. D., Zakko, S., Ringel, Y., Yu, J., ... & TARGET Study Group. (2011). Rifaximin therapy for patients with irritable bowel syndrome without constipation. New England Journal of Medicine, 364(1), 22-32. doi:10.1056/NEJMoa1004409
        
        6. Chedid, V., Dhalla, S., Clarke, J. O., Roland, B. C., Dunbar, K. B., Koh, J., ... & Mullin, G. E. (2014). Herbal therapy is equivalent to rifaximin for the treatment of small intestinal bacterial overgrowth. Global Advances in Health and Medicine, 3(3), 16-24. doi:10.7453/gahmj.2014.019
        
        7. Halmos, E. P., Power, V. A., Shepherd, S. J., Gibson, P. R., & Muir, J. G. (2014). A diet low in FODMAPs reduces symptoms of irritable bowel syndrome. Gastroenterology, 146(1), 67-75. doi:10.1053/j.gastro.2013.09.046
        
        8. Vrieze, A., Van Nood, E., Holleman, F., Salojärvi, J., Kootte, R. S., Bartelsman, J. F., ... & Nieuwdorp, M. (2012). Transfer of intestinal microbiota from lean donors increases insulin sensitivity in individuals with metabolic syndrome. Gastroenterology, 143(4), 913-916. doi:10.1053/j.gastro.2012.06.031
        
        9. Niedzielin, K., Kordecki, H., & Birkenfeld, B. (2001). A controlled, double-blind, randomized study on the efficacy of Lactobacillus plantarum 299V in patients with irritable bowel syndrome. European Journal of Gastroenterology & Hepatology, 13(10), 1143-1147. PMID: 11564945
        
        Additional Resources:
        - SIBO Info: https://www.siboinfo.com
        - International Foundation for Gastrointestinal Disorders: https://www.iffgd.org
        - American Gastroenterological Association: https://www.gastro.org
        
        Corresponding Author:
        Dr. Sarah Johnson, MD, PhD
        Division of Gastroenterology
        University Medical Center
        Email: <EMAIL>
        ORCID: 0000-0002-1234-5678
        """
        
        files = {
            'file': ('sibo_comprehensive_review.txt', sibo_research_content, 'text/plain')
        }
        
        data = {
            'group_id': 'default',
            'upload_type': 'messages'  # This should trigger full pipeline processing
        }
        
        upload_response = requests.post(
            "http://localhost:8234/api/documents/upload",
            files=files,
            data=data,
            timeout=30
        )
        
        print(f"Upload Status: {upload_response.status_code}")
        print(f"Upload Response: {upload_response.text}")
        
        if upload_response.status_code == 202:
            print("✅ Upload successful!")
            
            # Step 3: Monitor comprehensive processing progress
            print("\n⏳ Step 3: Monitoring FULL PIPELINE processing...")
            print("  - MedGemma OCR processing")
            print("  - Entity extraction with OpenRouter")
            print("  - Reference extraction to CSV")
            
            for i in range(25):  # Extended monitoring for full pipeline
                time.sleep(15)  # Longer intervals for comprehensive processing
                
                try:
                    status_response = requests.get(
                        "http://localhost:8234/api/processing/detailed-status/sibo_comprehensive_review.txt?group_id=default",
                        timeout=10
                    )
                    
                    if status_response.status_code == 200:
                        status_data = status_response.json()
                        processing_status = status_data.get('processing_status', 'unknown')
                        
                        print(f"Check {i+1}: Processing Status: {processing_status}")
                        
                        if processing_status == 'completed':
                            print("🎉 FULL PIPELINE PROCESSING COMPLETED!")
                            print(f"📊 Comprehensive Results:")
                            print(f"  - Text Length: {status_data.get('text_length', 0)} characters")
                            print(f"  - Episodes: {status_data.get('episodes_count', 0)}")
                            print(f"  - Entities: {status_data.get('entities_count', 0)}")
                            print(f"  - References: {status_data.get('references_count', 0)}")
                            print(f"  - OCR Status: {status_data.get('ocr_status', 'unknown')}")
                            print(f"  - Entity Status: {status_data.get('entity_extraction_status', 'unknown')}")
                            print(f"  - Reference Status: {status_data.get('reference_extraction_status', 'unknown')}")
                            print(f"  - CSV Export: {status_data.get('csv_export_path', 'Not available')}")
                            
                            # Analyze results
                            text_length = status_data.get('text_length', 0)
                            entities_count = status_data.get('entities_count', 0)
                            references_count = status_data.get('references_count', 0)
                            
                            print(f"\n📈 Pipeline Analysis:")
                            
                            # OCR Analysis
                            if text_length > 0:
                                print("✅ MedGemma OCR: SUCCESS")
                                print(f"  - Extracted {text_length} characters")
                                if text_length >= len(sibo_research_content) * 0.8:
                                    print("  - Excellent text preservation!")
                            else:
                                print("❌ MedGemma OCR: FAILED")
                            
                            # Entity Extraction Analysis
                            if entities_count > 0:
                                print("✅ Entity Extraction: SUCCESS")
                                print(f"  - Extracted {entities_count} medical entities")
                                if entities_count >= 10:
                                    print("  - Rich entity extraction!")
                            else:
                                print("❌ Entity Extraction: FAILED")
                            
                            # Reference Extraction Analysis
                            if references_count > 0:
                                print("✅ Reference Extraction: SUCCESS")
                                print(f"  - Extracted {references_count} academic references")
                                if references_count >= 5:
                                    print("  - Comprehensive reference extraction!")
                            else:
                                print("❌ Reference Extraction: FAILED")
                            
                            # CSV Export Analysis
                            csv_path = status_data.get('csv_export_path', '')
                            if csv_path:
                                print("✅ CSV Export: SUCCESS")
                                print(f"  - References exported to: {csv_path}")
                            else:
                                print("❌ CSV Export: FAILED")
                            
                            # Overall Success Assessment
                            success_components = [
                                text_length > 0,
                                entities_count > 0,
                                references_count > 0,
                                bool(csv_path)
                            ]
                            
                            success_rate = sum(success_components) / len(success_components)
                            
                            print(f"\n🎯 Overall Pipeline Success: {success_rate:.1%}")
                            
                            if success_rate >= 0.75:
                                print("🎉 EXCELLENT! Full pipeline is working!")
                                return True
                            elif success_rate >= 0.5:
                                print("⚠️ PARTIAL SUCCESS - Some components working")
                                return True
                            else:
                                print("❌ PIPELINE NEEDS WORK - Multiple failures")
                                return False
                                
                        elif processing_status == 'failed':
                            print(f"❌ Processing failed: {status_data.get('error_message', 'Unknown error')}")
                            return False
                        elif processing_status in ['processing', 'pending']:
                            print(f"⏳ Still processing... (attempt {i+1}/25)")
                        else:
                            print(f"❓ Unknown status: {processing_status}")
                    else:
                        print(f"❌ Status check failed: {status_response.status_code}")
                        
                except Exception as e:
                    print(f"❌ Status check error: {e}")
            
            print("⏰ Processing timeout - checking final status...")
            
        else:
            print(f"❌ Upload failed: {upload_response.status_code}")
            print(f"Error: {upload_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False
    
    return False

if __name__ == "__main__":
    print("🚀 TESTING COMPLETE SIBO PROCESSING PIPELINE")
    print("🔬 MedGemma OCR + Entity Extraction + Reference Extraction + CSV Export")
    print("=" * 80)
    
    success = test_full_pipeline()
    
    print("\n" + "=" * 80)
    print("📊 FINAL PIPELINE ASSESSMENT:")
    
    if success:
        print("🎉 FULL PIPELINE SUCCESS!")
        print("✅ Your comprehensive SIBO processing system is operational!")
        print("\n🚀 READY FOR PRODUCTION USE!")
        print("📋 Available Features:")
        print("  - MedGemma OCR for medical documents")
        print("  - Entity extraction with OpenRouter LLM")
        print("  - Academic reference extraction")
        print("  - CSV export for research analysis")
        print("  - Knowledge graph building")
        print("  - Real-time processing monitoring")
        
    else:
        print("❌ PIPELINE NEEDS ATTENTION")
        print("❌ Some components require investigation")
    
    print("\n" + "=" * 80)

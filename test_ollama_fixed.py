#!/usr/bin/env python3
"""
Test upload with fixed Ollama MedGemma entity extraction.
"""

import requests
import tempfile
import os
import time

def create_test_pdf():
    """Create a test PDF."""
    try:
        from reportlab.pdfgen import canvas
        
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
        temp_path = temp_file.name
        temp_file.close()
        
        c = canvas.Canvas(temp_path)
        c.drawString(100, 750, "MEDICAL REPORT - OLLAMA FIXED TEST")
        c.drawString(100, 720, "Patient: <PERSON>")
        c.drawString(100, 690, "Date: 2024-06-16")
        c.drawString(100, 660, "Diagnosis: SIBO (Small Intestinal Bacterial Overgrowth)")
        c.drawString(100, 630, "Treatment: Rifaximin 550mg twice daily")
        c.drawString(100, 600, "Probiotics: Lactobacillus acidophilus")
        c.drawString(100, 570, "Diet: Low-FODMAP diet")
        c.drawString(100, 540, "Supplements: Vitamin B12, Vitamin D3")
        c.drawString(100, 510, "Follow-up: Breath test in 4 weeks")
        c.drawString(100, 480, "Dr. <PERSON>, MD")
        c.save()
        
        return temp_path
    except Exception as e:
        print(f"Error creating PDF: {e}")
        return None

def test_upload():
    """Test upload with fixed Ollama MedGemma."""
    print("🔄 TESTING FIXED OLLAMA MEDGEMMA")
    print("=" * 50)
    
    pdf_path = create_test_pdf()
    if not pdf_path:
        return False
    
    try:
        print("📤 Uploading PDF to test fixed Ollama...")
        with open(pdf_path, 'rb') as f:
            files = {'file': ('ollama_fixed_test.pdf', f, 'application/pdf')}
            data = {
                'group_id': 'medical_docs',
                'upload_type': 'messages'
            }
            
            response = requests.post(
                'http://127.0.0.1:8234/api/documents/upload',
                files=files,
                data=data,
                timeout=30
            )
            
            print(f"Status: {response.status_code}")
            if response.status_code == 202:
                result = response.json()
                print("✅ Upload successful!")
                print(f"Response: {result}")
                
                print("\n⏳ Processing - check server terminal for:")
                print("  - 'Using Ollama MedGemma for Graphiti entity extraction'")
                print("  - No import errors")
                print("  - Ollama API calls (if Ollama is running)")
                print("")
                
                # Wait for processing
                for i in range(30):
                    print(f"⏳ Waiting... {i+1}/30 seconds")
                    time.sleep(1)
                
                # Check final status
                print("\n🔍 Checking final status...")
                status_response = requests.get(
                    f'http://127.0.0.1:8234/api/processing/detailed-status/ollama_fixed_test.pdf?group_id=medical_docs',
                    timeout=10
                )
                
                if status_response.status_code == 200:
                    status = status_response.json()
                    print("📊 Final Status:")
                    print(f"   Status: {status.get('processing_status')}")
                    print(f"   Text Length: {status.get('text_length')} characters")
                    print(f"   Episodes: {status.get('episodes_count')}")
                    print(f"   Entities: {status.get('entities_count')}")
                    print(f"   OCR Status: {status.get('ocr_status')}")
                    print(f"   Entity Extraction: {status.get('entity_extraction_status')}")
                    
                    if status.get('processing_status') == 'completed':
                        print("\n🎉 SUCCESS! Processing completed!")
                        return True
                    else:
                        print(f"\n⚠️ Processing status: {status.get('processing_status')}")
                        return False
                
                return True
            else:
                print(f"❌ Upload failed: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        if os.path.exists(pdf_path):
            os.unlink(pdf_path)

if __name__ == "__main__":
    print("🚀 FIXED OLLAMA MEDGEMMA TEST")
    print("Testing if the import error is fixed")
    print("=" * 50)
    
    success = test_upload()
    
    if success:
        print("\n✅ Test completed!")
    else:
        print("\n❌ Test failed")

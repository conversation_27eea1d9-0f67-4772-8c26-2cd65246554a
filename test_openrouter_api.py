#!/usr/bin/env python3
"""
Test script to verify OpenRouter API configuration directly.
"""

import asyncio
import logging
import os
import aiohttp
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_openrouter_api():
    """Test OpenRouter API directly."""
    
    try:
        # Get configuration from environment
        openai_api_key = os.getenv('OPENAI_API_KEY')
        openai_base_url = os.getenv('OPENAI_BASE_URL', 'https://openrouter.ai/api/v1')
        model_name = os.getenv('MODEL_NAME', 'meta-llama/llama-4-maverick:free')
        
        logger.info(f"Testing OpenRouter API:")
        logger.info(f"  Base URL: {openai_base_url}")
        logger.info(f"  Model: {model_name}")
        logger.info(f"  API Key: {openai_api_key[:10]}..." if openai_api_key else "  API Key: None")
        
        if not openai_api_key:
            logger.error("No API key found!")
            return False
        
        # Test with a simple entity extraction prompt
        test_prompt = """
        Extract entities from this medical text:
        
        "Vitamin D deficiency is common in patients with SIBO. Probiotics like Lactobacillus acidophilus can help restore gut microbiome balance."
        
        Please identify medical entities like conditions, treatments, and substances. Return them as a simple list.
        """
        
        headers = {
            'Authorization': f'Bearer {openai_api_key}',
            'Content-Type': 'application/json',
            'HTTP-Referer': 'https://github.com/getzep/graphiti',
            'X-Title': 'Graphiti Entity Extraction Test'
        }
        
        payload = {
            'model': model_name,
            'messages': [
                {
                    'role': 'user',
                    'content': test_prompt
                }
            ],
            'max_tokens': 500,
            'temperature': 0.1
        }
        
        logger.info("Making API call to OpenRouter...")
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{openai_base_url}/chat/completions",
                headers=headers,
                json=payload,
                timeout=aiohttp.ClientTimeout(total=60)
            ) as response:
                
                logger.info(f"Response status: {response.status}")
                
                if response.status == 200:
                    result = await response.json()
                    
                    if 'choices' in result and len(result['choices']) > 0:
                        content = result['choices'][0]['message']['content']
                        logger.info(f"✅ SUCCESS: API call successful!")
                        logger.info(f"Response content: {content}")
                        return True
                    else:
                        logger.error(f"❌ FAILED: No choices in response: {result}")
                        return False
                        
                else:
                    error_text = await response.text()
                    logger.error(f"❌ FAILED: API call failed with status {response.status}")
                    logger.error(f"Error response: {error_text}")
                    return False
                    
    except Exception as e:
        logger.error(f"❌ FAILED: Error in OpenRouter API test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function."""
    logger.info("Starting OpenRouter API Test")
    
    success = await test_openrouter_api()
    
    logger.info(f"\n=== TEST RESULTS ===")
    if success:
        logger.info("✅ SUCCESS: OpenRouter API is working correctly!")
        logger.info("The issue with entity extraction is likely in the Graphiti configuration or processing.")
    else:
        logger.error("❌ FAILED: OpenRouter API is not working")
        logger.error("This indicates an issue with the API key, model, or network configuration.")

if __name__ == "__main__":
    asyncio.run(main())
